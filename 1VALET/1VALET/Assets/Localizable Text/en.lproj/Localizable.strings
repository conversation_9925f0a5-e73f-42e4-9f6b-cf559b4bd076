"OK" = "OK";
"CANCEL" = "Cancel";
"SETTINGS" = "Settings";
"ON" = "On";
"OFF" = "Off";
"NEXT" = "Next";
"SHOW" = "Show";
"HIDE" = "Hide";
"YES" = "Yes";
"NO" = "No";
"ACCEPT" = "Accept";
"DECLINE" = "Decline";
"GOT_IT" = "Got it";
"TRY_AGAIN" = "Try again";
"I_AGREE" = "I agree";
"CHANGE" = "Change";
"DONE" = "Done";
"AND" = "and";
"OPTIONS" = "Options";
"LATER" = "Later";
"PERMISSIONS" = "Permissions";
"SHARE" = "Share";
"EXIT" = "Exit";
"NO_THANKS" = "No thanks";

"EMAIL_PLACEHOLDER" = "<EMAIL>";

// Home Welcome Scroll
"WELCOME_NAME" = "Hello %@";
"WELCOME_HOME" = "Welcome Home";

// Call Forwarding
"EDIT_NUMBER" = "Edit Number";

"REFRESH" = "Refresh";
"NO_CONNECTION" = "No Internet Connection";

"MESSAGES" = "Messages";
"NOTIFICATIONS" = "Notifications";
"DELETE" = "Delete";

"THERMOSTAT" = "Thermostat";
"THERMOSTATS" = "Thermostats";
"SEND" = "Send";
"EDIT" = "Edit";

"LEAVE" = "Leave";
"STAY" = "Stay";
"CONTINUE" = "Continue";
"SEARCH" = "Search";

// DocBox Sort Type
"DATE" = "Date";
"NAME" = "Name";

// Help
"HELP" = "Help";

"MOVE_IN_DATE" = "Move-in date";
"CLOSE" = "Close";
"SEND_MESSAGE" = "Send message";

// Amenity Booking
"UPCOMING" = "Upcoming";
"PAST" = "Past";
"REVIEW_TITLE" = "Review";
"SCROLL_DOWN" = "Scroll down";

"SUCCESS" = "Success!";
"LEARN_MORE" = "Learn more";
"MORE_INFO" = "More Info";

"EXPANDABLE_TEXT_SHOW_MORE" = "SHOW MORE";
"EXPANDABLE_TEXT_SHOW_LESS" = "SHOW LESS";

"ONE_VALET_APP" = "1VALET App";

// Onboarding
"USER_ALREADY_ACTIVATED" = "Your account has already been created. Would you like to Sign In?";
"GENERIC_ERROR_MESSAGE" = "Oops! Something went wrong. Please try again.";
"SOMETHING_WENT_WRONG" = "Something went wrong.";
"TOO_MANY_REQUESTS_MESSAGE" = "Oops! Too many requests. Please wait a moment and try again.";

// Activation Code
"ACTIVATION_CODE_NOT_RECEIVED" = "I have not received an activation code";
"ACTIVATION_CODE_INCORRECT" = "The activation code you've entered is invalid. Please try again.";

"ACTIVATION_CODE_ONE_TIME_ENTER_RECEIVED" = "Enter your <underline>one-time</underline> 6-digit activation code";
"ACTIVATION_CODE_ONE_TIME_ENTER_RECEIVED_SWIFT" = "Enter your __one-time__ 6-digit activation code";
"ACTIVATION_CODE_ENTER_RECEIVED_SUBTITLE" = "You should have received a code via text message or other option.";
"ACTIVATION_CODE_ENTER_RECEIVED_PM_SUBTITLE" = "You should have received a code from your property management.";
"ACTIVATION_CODE_USER_DISABLED" = "The user is disabled for this activation code. Please contact your building manager.";

// Verify Account
"ONBOARDING_VERIFY_ACCOUNT_TITLE" = "Enter your phone number";
"ONBOARDING_VERIFY_ACCOUNT_DESCRIPTION" = "Receive a new code by entering the phone number associated with your account.";
"ONBOARDING_VERIFY_ACCOUNT_CALL_ME" = "Call me";
"ONBOARDING_VERIFY_ACCOUNT_TEXT_ME" = "Text me";
"ONBOARDING_VERIFY_ACCOUNT_SUCCESS" = "Thank you. You will receive the code shortly if we have this number on file";
"ACTIVATION_CODE_MULTIPLE_USERS" = "Something went wrong. Please contact your property manager to receive the code.";
"ONBOARDING_PROTECT_ACCOUNT" = "Protect your account: We will never ask for your username, passcode or any other account information outside of this app";

// Hint
"HINT_COMPLETE_PHONE_NUMBER_TITLE" = "Enter your phone number";
"HINT_COMPLETE_PHONE_NUMBER_SUBTITLE" = "Verify it's you by entering the last 4 digits of your phone number below.";
"HINT_COMPLETE_TERMS_CONDITIONS_POLICY" = "By continuing you agree to 1VALET's\nTerms & Conditions and Privacy Policy.";
"HINT_COMPLETE_TERMS_CONDITIONS" = "Terms & Conditions";
"HINT_COMPLETE_POLICY" = "Privacy Policy";
"HINT_INVALID" = "We don't recognize the phone number. Please enter the phone number you've shared with your building manager.";
"HINT_USER_DISABLED" = "The user is disabled for this phone number. Please contact your building manager.";

// Link Account
"LINK_ACCOUNT_TITLE" = "Existing account detected";
"LINK_ACCOUNT_DESCRIPTION" = "Link suite to your existing account to access everything from one place, or activate a new account.";
"LINK_ACCOUNT_BUTTON" = "Link to account";
"LINK_ACCOUNT_CREATE_ACCOUNT_BUTTON" = "Activate a new account";

"ACCOUNT_LINKED_TITLE" = "Account linked";
"ACCOUNT_LINKED_DESCRIPTION" = "We have successfully linked your account to the username %@";

// Verified
"VERIFIED_ACCOUNT_DESCRIPTION" = "We have verified your account. Now let's create your resident profile.";
"VERIFIED_ACCOUNT_BUTTON" = "Create profile";

// Username
"USERNAME_CHOOSE_TITLE" = "Create a username for your account";
"USERNAME_IN_USE" = "This username has been taken, please try another one.";
"USERNAME_PLACEHOLDER" = "4+ Characters";

// App PIN
"APP_PIN_NEW_TITLE" = "Create a secure passcode";
"APP_PIN_NEW_SUBTITLE" = "This will be used for your login. Make sure it's not too easy to guess!";
"APP_PIN_TOO_EASY" = "Sorry, this passcode is too easy to guess";

// Account Confirmed
"ACCOUNT_CONFIRMED_CONGRATULATIONS" = "Congrats!";
"ACCOUNT_CONFIRMED_CONGRATULATIONS_DESCRIPTION" = "You've finished activating your resident account!";

// Main Onboarding
"MAIN_ON_NEW_RESIDENT" = "I’m new to 1VALET";
"MAIN_ON_TITLE" = "<light>Welcome
home</light><green>.</green>";

// Sign In
"SIGN_IN" = "Sign in";
"SIGN_INTO_ONEVALET" = "Sign in to 1VALET";
"SIGN_IN_FORGOT_PIN" = "Forgot passcode";
"SIGN_IN_FORGOT_USERNAME" = "Forgot username";
"SIGN_IN_USERNAME" = "Enter username";
"SIGN_IN_PIN" = "Enter passcode";
"SIGN_IN_USERNAME_NOT_RECOGNIZED" = "We do not recognize the username you've entered.";
"SIGN_IN_USER_LOCKOUT" = "You are locked-out as you have entered the wrong password multiple times. Please try to login after sometime!";
"SIGN_IN_NO_SUITES" = "You do not seem to have any suites associated with your account. We've logged you out.";
"SIGN_IN_INACTIVE" = "It seems you are no longer active in the Building community.";
"SIGN_IN_INVALID_PASSWORD" = "Oops! The passcode you’ve entered is incorrect. Please try again.";
"SIGN_IN_SET_DEFAULT_SUITE" = "Pick your primary suite";
"SIGN_IN_SET_DEFAULT_SUITE_BUTTON" = "Done";
"SIGN_IN_SET_DEFAULT_SUITE_DESCRIPTION" = "You can always change this later on.";
"SIGN_IN_YOUR_ACCOUNTS" = "Your Accounts";
"SIGN_IN_ADD_ACCOUNT" = "Add Account";
"SIGN_IN_EXISTING_ACCOUNT" = "Log into existing account";
"SIGN_IN_ACTIVATE_ACCOUNT" = "Activate a new account";
"SIGN_IN_DIGITAL_FOB" = "Digital Fob";
"SIGN_IN_DIGITAL_FOB_TITLE" = "Can’t sign in? No problem!";
"SIGN_IN_DIGITAL_FOB_DESCRIPTION" = "You have access to your digital fob for a limited time. Full functionality will be restored when you are able to sign in.";
"SIGN_IN_ACCESS_DIGITAL_FOB" = "Access Digital Fob";
"SIGN_IN_EXPIRED_DIGITAL_FOB_TITLE" = "Sign in to restore app functionality";
"SIGN_IN_EXPIRED_DIGITAL_FOB_DESCRIPTION" = "Your access to your digital fob has ended. Full functionality will be restored when you are able to sign in.";
"SIGN_IN_FOUR_DIGIT" = "Four-digit passcode";
"SIGN_IN_NEW_TO_1VALET" = "New to 1VALET? ";
"SIGN_IN_ACTIVATE_AN_ACCOUNT" = "Activate an account";
"SIGN_IN_ACTIVATE_NEW_ACCOUNT" = "Activate new account";
"SIGN_IN_LOCKED_OUT" = "Locked out of your building?";
"SIGN_IN_ACCESS_DIGITAL_FOB" = "Access your digital Fob";
"SIGN_IN_INCORRECT_CREDENTIALS" = "Oops! The username or passcode you’ve entered is incorrect. Please try again.";
"SIGN_IN_REMOVE_ACCOUNT" = "Are you sure you want to remove this account?";
"SIGN_IN_RESET_DOOR_LOCK" = "Please reset your suite door lock and delete old pin codes.";
"SIGN_IN_ACCOUNT_SETUP" = "Account Setup";
"SIGN_IN_ENTER_USERNAME" = "Please enter your username to continue with passcode recovery.";
"SIGN_IN_DID_YOU_FORGET" = "Did you forget your username or passcode?";
"SIGN_IN_DID_YOU_FORGET_USERNAME" = "username";
"SIGN_IN_DID_YOU_FORGET_PASSCODE" = "passcode?";
"SIGN_IN_USERNAME_OR_EMAIL" = "Username or Email";
"SIGN_IN_REQUIRED" = "Required";

// Reset PIN
"RESET_PIN_TITLE" = "Forgot passcode?
No worries!";
"RESET_PIN_INSTRUCTIONS" = "Choose how you'd like to receive your verification code to confirm your identity. It can be sent via text or email linked to your account.";
"RESET_PIN_SEND_TEXT_CODE" = "Text code";
"RESET_PIN_SEND_EMAIL_CODE" = "Email code";
"RESET_PIN_ENTER_CODE" = "Enter the 6-digit verification code";
"RESET_PIN_RESEND_ERROR" = "We are sorry you are having trouble receiving the code. Contact your building manager for help resetting your passcode.";
"RESET_PIN_RESEND_CODE" = "Resend Code";
"RESET_PIN_CODE_SENT_EMAIL" = "A verification code has been sent to the email associated with your account and should arrive shortly.";
"RESET_PIN_CODE_SENT_SMS" = "A verification code has been sent to the mobile number associated with your account and should arrive shortly.";
"RESET_PIN_STILL_NO_CODE" = "I still have not received the code";
"RESET_PIN_CREATE_NEW_PIN" = "Create a new passcode";
"RESET_PIN_INCORRECT_PIN" = "The verification code you entered is incorrect. Please try again.";
"RESET_PIN_DID_NOT_RECEIVE_PIN" = "I did not receive a verification code.";
"RESET_PIN_CODE_EXPIRED" = "The verification code you entered is expired. Please request a new code.";
"RESET_PIN_STILL_NOT_RECEIVING" = "Still not receiving the code?";
"RESET_PIN_STILL_NO_EMAIL" = "If you don't receive the email, please check your junk mail folder and make sure our emails aren't blocked or marked as spam.\n\nFor additional troubleshooting, check out our help article or tap below to try receiving the code via text instead.";
"RESET_PIN_STILL_NO_SMS" = "If you don’t receive the text, check with your property manager to see if we have the correct number on your resident profile, ensure you have enough signal, and verify that our number isn’t blocked.\n\nFor additional troubleshooting, check out our help article or tap below to try receiving the code via email instead.";
"RESET_PIN_STILL_NO_CODE_SECTION" = "check out our help article";
"RESET_PIN_TEXT_INSTEAD" = "Text me instead";
"RESET_PIN_EMAIL_INSTEAD" = "Email me instead";
"RESET_PIN_HELP" = "https://help.1valet.com/en/articles/********-i-have-not-received-a-verification-code-when-attempting-to-recover-a-forgotten-passcode";

// Forgot Username
"FORGOT_USERNAME_SMS" = "Send SMS";
"FORGOT_USERNAME_DESCRIPTION" = "Enter the phone number associated with your account to receive an SMS with your username";
"FORGOT_USERNAME_INCORRECT_PHONE_NUMBER" = "We don't recognize the phone number. Please enter the phone number you've shared with your building manager.";
"FORGOT_USERNAME_SMS_ERROR" = "We are not able to send SMS. Please try again!";

"ACCOUNT_LOCKED" = "Your account has been locked.";

// Alerts
"LOG_OUT_DIALOG_TITLE" = "Logged out";
"LOG_OUT_DIALOG_MESSAGE" = "It seems you are logged-out. Please login again to continue. Sorry for the inconvenience.";
"MOVED_OUT_DIALOG_TITLE" = "Moved Out";
"MOVED_OUT_DIALOG_MESSAGE" = "We're sorry to see you go! Since you've moved out of %@ in %@, We've logged you out.";
"MOVED_OUT_TO_NON_RESIDENT_DIALOG_MESSAGE" = "Your account settings have changed for %@ in %@. You will need to sign in to the app again.";

"REQUEST_THROTTLED_MESSAGE" = "We're still trying to send you the verification code you requested. If you do not receive it in the next few minutes, please try again.";

"REQUEST_USERNAME_THROTTLED_MESSAGE" = "We're still trying to send you the username you requested. If you do not receive it in the next few minutes, please try again.";

"ENTER_VERIFICATION_CODE_MESSAGE" = "Are you sure you want to go back? You may need to wait a few minutes before you can generate a new verification code.";

"MICROPHONE_DIALOG_TITLE" = "Microphone";
"MICROPHONE_DIALOG_MESSAGE" = "Recommended to have microphone for call so other person can hear you.";

"CAMERA_DIALOG_TITLE" = "Camera";
"CAMERA_DIALOG_MESSAGE" = "We need access to your cameras to be able to take a photo for the facial entry feature.";
"SIGN_OUT_DIALOG_MESSAGE" = "If you sign out, you will no longer receive video calls from the Entry System on this device. To receive voice-only calls from the Entry System on your phone, you must __enable call forwarding__.";
"SIGN_OUT_DIALOG_MESSAGE_SHORT" = "Are you sure you want to sign out?";
"SIGN_OUT_YES_SURE" = "Yes, I'm sure";

"NOTIFICATION_INCOMING_CALL_PLACEHOLDER" = "Incoming Call
You have an incoming video call";

// App Update Alerts
"APP_UPDATE_DIALOG_DESCRIPTION" = "This feature is no longer supported by this version of the app. In order to complete this action, you must first update the 1VALET Resident App to the newest version.";
"APP_UPDATE_DIALOG_NEGATIVE_BUTTON" = "Cancel";
"APP_UPDATE_DIALOG_POSITIVE_BUTTON" = "Update";

"APP_UPDATE_POSITIVE_BUTTON" = "Update App";
"APP_UPDATE_NON_CRITICAL_DIALOG_DESCRIPTION" = "Dear Resident\n\nThere is a new version of the 1VALET app available for your community. Please update as soon as possible to ensure we deliver the best possible experience.";
"APP_UPDATE_NON_CRITICAL_DIALOG_NEGATIVE_BUTTON" = "Later";

"APP_UPDATE_OLD_DIALOG_DESCRIPTION" = "Dear Resident\n\nThis version is now out of date. The performance and security of some of its features may be affected. Please update as soon as possible to continue using all its features.";
"APP_UPDATE_OLD_DIALOG_NEGATIVE_BUTTON" = "Continue using an unsupported version";

"APP_UPDATE_URGENT_DIALOG_DESCRIPTION" = "Dear Resident\n\nThis version of the 1VALET app is out of date and we no longer support it. Please download the latest version to continue using all its features.";
"APP_UPDATE_URGENT_DIALOG_NEGATIVE_BUTTON" = "Continue using an unsupported version";

"OPEN_LINK_DIALOG_TITLE" = "Open Link";
"OPEN_LINK_DIALOG_DESCRIPTION" = "You are leaving the Resident App to \n\n %@";

"DELETE_MESSAGE_DIALOG_TITLE" = "Delete Message?";
"DELETE_MESSAGE_DIALOG_DESCRIPTION" = "You will no longer be able to see this message.";
"DELETE_FOREVER" = "Delete Forever";

// Facial Recognition
"FACIAL_RECOGNITION_COMPLETE_POLICY" = "By enabling you agree to 1VALET's Facial Entry Terms & Conditions and Facial Entry Policy.";
"FACIAL_RECOGNITION_TERMS_CONDITIONS" = "Terms & Conditions";
"FACIAL_RECOGNITION_POLICY" = "Facial Entry Policy";
"FACIAL_RECOGNITION_AGREEMENT" = "Agreement";
"FACIAL_RECOGNITION_RETAKE" = "Retake";
"FACIAL_RECOGNITION_RETAKE_DIALOG_DESCRIPTION" = "Are you sure you want to delete and retake this photo?";
"FACIAL_RECOGNITION_AWAITING_OPT_IN" = "Enable";
"FACIAL_RECOGNITION_DISABLED" = "Disabled";
"FACIAL_RECOGNITION_DISABLED_BUTTON" = "Re-enable facial entry";
"FACIAL_RECOGNITION_ENABLED" = "Enabled %@";
"FACIAL_RECOGNITION_ENABLED_BUTTON" = "Disable";
"FACIAL_RECOGNITION_MISSING_PHOTO" = "Setup incomplete";
"FACIAL_RECOGNITION_MISSING_PHOTO_BUTTON" = "Continue setup";
"FACIAL_RECOGNITION_IN_PROGRESS" = "Processing...";
"FACIAL_RECOGNITION_DISABLED_DIALOG_DESCRIPTION" = "You are about to disable facial entry. Are you sure you want to proceed?";
"FACIAL_RECOGNITION_DISABLED_DIALOG_CHECKBOX_DESCRIPTION" = "Delete my selfies";
"FACIAL_RECOGNITION_WHAT" = "Facial recognition entry";
"FACIAL_RECOGNITION_SUCCESS_UPLOAD" = "We will let you know when Facial Entry is ready for you to use.";
"FACIAL_RECOGNITION_SOMETHING_WRONG" = "Something went wrong with your photo. Please retake.";
"FACIAL_RECOGNITION_CANCEL_MESSAGE" = "By cancelling facial entry all your photos will be deleted.  Are you sure you want to proceed?";
"FACIAL_RECOGNITION_MORE" = "Learn more";
"FACIAL_RECOGNITION_AWAITING_OPT_IN_DESCRIPTION" = "Don’t have your keys?\n\nEnable Facial Entry and enter through your building’s digital Entry System with only a smile.\n\nFacial entry works by referencing photos of your face. Once enabled, all you’ll need to do when you get to the Entry system is tap “Facial Entry” and smile.";
"FACIAL_RECOGNITION_PENDING_DESCRIPTION" = "We are processing your selfies and training the system. This won't take long...";
"FACIAL_RECOGNITION_ENABLED_DESCRIPTION" = "You can now enter your building with only a smile.";
"FACIAL_RECOGNITION_REENABLED_DESCRIPTION" = "Re-enable Facial Entry to enter your building with only a smile.";
"FACIAL_RECOGNITION_FIRST_TIME_INSTRUCTIONS_DESCRIPTION" = "Position your face within the outline until it turns blue and take a selfie.";

"VIDEO_CALL_IN_PROGRESS" = "Video call in progress";
"VIDEO_CALL_MUTED" = "Microphone on mute";

// Temperature Unit
"UNIT_KELVIN" = "K";
"UNIT_CELSIUS" = "°";
"UNIT_FAHRENHEIT" = "°";
"CELSIUS" = "Celsius";
"FAHRENHEIT" = "Fahrenheit";

// Distance Unit
"UNIT_METERS" = "m";
"UNIT_KILOMETERS" = "km";

// Legal
"PRIVACY_POLICY" = "Privacy Policy";
"TERMS_CONDITIONS" = "Terms and Conditions";
"PRIVACY_POLICY_URL" = "https://1valet.com/privacy-policy/?viewMode=embedded";
"FACIAL_PRIVACY_POLICY_URL" = "https://1valet.com/facial-entry-policy/?viewMode=embedded";
"TERMS_CONDITIONS_URL" = "https://1valet.com/terms-of-use/?viewMode=embedded";
"FACIAL_ENTRY_POLICY" = "Facial Entry Policy";

// Change Passcode
"CHANGE_PASSCODE_TITLE" = "Change passcode";
"CHANGE_PASSCODE_ENTER_CURRENT_DESCRIPTION" = "To make account changes, you must first enter your 4-digit passcode";
"CHANGE_PASSCODE_DESCRIPTION" = "Enter a new passcode";
"CHANGE_PASSCODE_WARNING" = "Changing your password will sign you out on your devices.";

// Push Notification Keys
"INCOMING_VIDEO_CALL_TITLE" = "Incoming call";
"INCOMING_VIDEO_CALL_BODY" = "You have an incoming video call.";
"INCOMING_VIDEO_PROPERTY_MANAGER_TITLE" = "Property Management";
"MISSED_CALL_TITLE" = "Missed call";
"MISSED_CALL_BODY" = "You have a missed call from entry system.";
"MISSED_CALL_BODY_PORTAL" = "You have a missed call from property management.";
"FACIAL_RECOGNITION_TRAINING_COMPLETE_TITLE" = "Facial recognition enabled";
"FACIAL_RECOGNITION_TRAINING_COMPLETE_BODY" = "You can now enter the building with a smile.";
"CANCELLED_CALL_TITLE" = "Video call cancelled";
"CANCELLED_CALL_BODY" = "We couldn't reach you on the 1VALET resident app so we cancelled the video call.";
"COMMUNITY_MESSAGE_TITLE" = "New Community Message";
"DOCBOX_FILE_UPLOAD_TITLE" = "DocBox";
"DOCBOX_FILE_UPLOAD_BODY" = "%@ has been uploaded for residents of %@. Please review it at your convenience.";
"AMENITY_BOOKING_CHANGE_TITLE" = "Amenity Booking";
"AMENITY_BOOKING_APPROVED_BODY" = "Your booking for %@ on %@ at %@ has been approved.";
"AMENITY_BOOKING_DENIED_BODY" = "Your booking for %@ on %@ at %@ has been denied.";
"AMENITY_BOOKING_MODIFIED_BODY" = "Your booking for %@ has been modified. See more details under My Bookings.";
"AMENITY_BOOKING_CANCELLED_BODY" = "Your booking for %@ was cancelled by property management.";
"AMENITY_BOOKING_REFUNDED_BODY" = "A refund was issued and will be processed within a few business days.";
"AMENITY_BOOKING_NOTE_BODY" = "Note from management: %@";
"AMENITY_BOOKING_REMINDER_BODY" = "Hi %@, your %@ booking on %@ at %@ is coming up.";
"AMENITY_BOOKING_REMINDER_TITLE" = "Amenity Booking";
"MAINTENANCE_REQUEST_NOTIFICATION_TITLE" = "Maintenance Request";
"MAINTENANCE_REQUEST_STATUS_CHANGE_NOTIFICATION_BODY" = "The status for your maintenance request #%@ has changed to: %@";
"MAINTENANCE_REQUEST_STATUS_CHANGE_NOTIFICATION_DEFAULT_BODY" = "The status for your maintenance request has changed.";
"DOOR_SENSOR_NOTIFICATION_TITLE" = "Door Sensors";
"DOOR_SENSOR_TRIGGERED_BODY" = "A door sensor was triggered in your suite at %@";
"DOOR_SENSOR_MANAGER_ARMED_BODY" = "Property Manager turned on the door sensors in your suite at %@";
"DOOR_SENSOR_MANAGER_DISARMED_BODY" = "Property Manager turned off the door sensors in your suite at %@";
"DOOR_SENSOR_RESIDENT_DISARMED_BODY" = "%@ turned off the door sensors in your suite at %@";
//"DOOR_SENSOR_TABLET_DISARMED_BODY" = "The door sensor in your suite was turned off at %@ via the suite tablet.";
"DOOR_SENSOR_PIN_MODIFIED_BODY" = "%@ changed the door sensor master pincode at %@";
"DOOR_SENSOR_YOU" = "You";
"APP_UPDATE_START_UP_DIALOG_TITLE" = "Update available";
"APP_UPDATE_START_UP_DIALOG_DESCRIPTION" = "In order to ensure the best possible experience, we recommend you update to the newest version of the 1VALET app.";
"TECHNICIAN_REQUEST_NOTIFICATION_TITLE" = "Access Request";
"TECHNICIAN_REQUEST_NOTIFICATION_REQUESTING_BODY" = "%@ is requesting access at %@."; // Name, Time
"TECHNICIAN_REQUEST_NOTIFICATION_COMPLETE_BODY" = "%@ has completed repairs at %@."; // Name, Time
"TECHNICIAN_REQUEST_NOTIFICATION_CANCELLED_BODY" = "%@ has cancelled their access."; // Name
"TECHNICIAN_REQUEST_SUITE_CHANGED" = "Your suite changed";
"MARKETPLACE_TITLE" = "Marketplace";
"MARKETPLACE_FEEDBACK_REMINDER_BODY" = "Tell us how your experience was with %@.";
"AMENITY_PAYMENT_DEPOSIT_COLLECTED_TITLE" = "Amenities";
"AMENITY_PAYMENT_DEPOSIT_COLLECTED_BODY" = "Management has collected %@ from your deposit on %@."; // Amount, Date
"VIRTUAL_PARCEL_ROOM_BODY" = "There is a delivery waiting for you";
"COMMUNITY_BULLETIN_BOARD_POST_REMOVED_NOTIFICATION_BODY" = "A post titled %@ was removed from Marketplace.";
"COMMUNITY_BULLETIN_BOARD_USER_BANNED_BODY" = "You have been banned from posting in Marketplace.";
"COMMUNITY_BULLETIN_BOARD_MESSAGE_FROM_MANAGEMENT" = "Message from management: %@";
"COMMUNITY_BULLETIN_BOARD_POST_REMOVED_BODY" = "A post of yours was removed from Marketplace.";
"COMMUNITY_BULLETIN_BOARD_NO_POSTS_TITLE" = "No marketplace posts";
"COMMUNITY_BULLETIN_BOARD_NO_POSTS_DESCRIPTION" = "Upcoming and past marketplace posts will appear here.";

// Amenity Dialogs
"AMENITY_BOOKING_TIME_CONSECUTIVE_DIALOG" = "Some amenities may allow you to select consecutive time slots to extend the time of your booking.\n\nTap on time slots with a dark border around them to extend it.";
"AMENITY_BOOKING_TIME_BOOKING_DIALOG" = "Limits may be set for how many bookings can be made on some amenities. When picking a time you’ll see how many bookings you have left.";

// Call Forwarding
"CALL_SETTINGS_FORWARDING" = "Phone call (audio only)";
"CALL_SETTINGS_FORWARDING_NUMBER" = "Forward to";
"CALL_SETTINGS_DESCRIPTION" = "You can receive one-way video calls from guests (if applicable) or property managers. If a video call can't connect, it will be forwarded to the phone number below.\n\nIf you prefer to always receive phone calls, select that option below.";
"CALL_SETTINGS_CHOOSE_ONE" = "Choose one";
"CALL_SETTINGS_VIDEO_CALL" = "One-Way Video Calling (Default)*";
"CALL_SETTINGS_CALL_FORWARDING_NUMBER" = "Call forwarding number";
"CALL_SETTINGS_FOOTER" = "When receiving a phone call, press any key on your device’s keypad during the call to unlock the door for guests.\n\n*Others will not be able to see you.";
"CALL_SETTINGS_EDIT_DESCRIPTION" = "Number used for call forwarding";
"CALL_SETTINGS_OPTIONS_DESCRIPTION" = "How would you like to receive calls?";

// Video Call Tutorial
"VIDEO_CALLING_TITLE" = "1-way video calling";
"VIDEO_CALLING_DESCRIPTION" = "You will now receive guest calls from the Entry System on this app.

In order to use the feature, we need permission to send you notifications when calls come in, and to use your device's microphone so that you can speak to guests during these calls.

You can make further changes to how you receive calls in Call Settings.";

"NO_CONNECTION_DIALOG_DESCRIPTION" = "We could not detect an Internet connection. Please make sure you're connected to the Internet and try again.";

"NO_VIDEO_CALL" = "Entry System video is temporarily offline.";
"NO_SPEAKERS_CALL" = "Entry System speakers are temporarily offline.";
"INCOMING_CALL_INSTRUCTIONS" = "tap below to answer, decline or unlock";

// Watch Strings
"WT_CARD_UNLOCK" = "Tap to unlock";
"WT_NO_CARDS" = "Select the building doors you want to display here on the 1VALET mobile app.";
"WT_CARD_UNLOCK_FAIL" = "Oops! We've encountered an error.";
"WT_OPEN_MOBILE_APP" = "Open mobile app";
"WT_SIGN_IN" = "Please sign into the 1VALET app on your phone.";
"WATCH_HOLD_NEAR_READER" = "Hold watch near reader";
"WATCH_CONNECTING_DIGITAL_FOB" = "Connecting";
"WATCH_RENEWING_DIGITAL_FOB" = "Renewing digital fob";
"WATCH_SUCCESS" = "Success";
"WATCH_RENEWED_DIGITAL_FOB" = "Digital fob Renewed!";
"WATCH_RENEW_FAILED" = "Please try again later or make sure you have access to the internet.";
"WATCH_DIGITAL_FOB_TIMED_OUT" = "Digital fob reader not detected. Please bring your watch to the fob reader and try again.";
"WATCH_TRY_AGAIN" = "Try again";
"WATCH_DIGITAL_FOB_REJECTED" = "Sorry, but your digital fob doesn't have access to this door. If you believe this is an error, please renew your digital fob and try again.";
"WATCH_RENEW_DIGITAL_FOB" = "Renew fob";
"WATCH_DIGITAL_FOB_SOMETHING_WENT_WRONG" = "Something went wrong. Please renew your digital fob and try again.";
"WATCH_DIGITAL_FOB" = "Digital fob";
"WATCH_NO_DOORS" = "You currently don't have any digital entry cards.";

// Time
"TIME_DAYS_SHORT" = "d";
"TIME_HOURS_SHORT" = "h";
"TIME_MINUTES_SHORT" = "m";
"TIME_SECONDS_SHORT" = "now";
"TIME_HOURS_MEDIUM" = "hr";
"TIME_MINUTES_MEDIUM" = "min";

"NO_NOTIFICATIONS" = "You have no new notifications";
"RESIDENT_NOTIFICATION" = "resident";

// Thermostat
"THERMOSTAT_SETPOINT" = "Setpoint";
"THERMOSTAT_CHANGE_SETPOINT" = "Change Setpoint";
"THERMOSTAT_INDOOR_TEMPERATURE" = "Indoor temp.";
"THERMOSTAT_HUMIDITY" = "Humidity";
"THERMOSTAT_TEMPERATURE" = "Temperature";
"THERMOSTAT_CODE" = "Thermostat Code";
"THERMOSTAT_CODE_WARNING_DESCRIPTION" = "This code enables you to access advanced settings on your physical thermostat. <bold>Do not<bold> change this code or factory reset the thermostat, otherwise, it may lose connection to the 1VALET Platform, and you will be charged for a service call to re-enable access.";
"THERMOSTAT_TEMPERATURE_UNITS" = "Temperature units";

// Thermostat HVAC
"HVAC_NONE" = "None";
"HVAC_OFF" = "Off";
"HVAC_HEAT" = "Heating";
"HVAC_COOL" = "Cooling";
"HVAC_AUTO" = "Auto";
"HVAC_AUX_HEAT_ONLY" = "Auxiliary Heat";
"HVAC_IDLE" = "Idle";
"HVAC_MODE" = "HVAC Mode";

// Thermostat Fan
"FAN_NONE" = "None";
"FAN_AUTO" = "Auto";
"FAN_LOW" = "Low";
"FAN_MEDIUM" = "Medium";
"FAN_HIGH" = "High";
"FAN_ON" = "On";
"FAN_OFF" = "Off";
"FAN_MODE" = "Fan Mode";

"THERMOSTAT_UNREACHABLE" = "Device cannot be detected.";
"THERMOSTAT_CURRENTLY_OFF" = "Thermostat is currently <bold>Off</bold>";
"THERMOSTAT_CURRENTLY_OFF_DESCRIPTION" = "Your thermostat is currently set to \"Off\". To change the setpoint, please turn it on by selecting from the list of modes.";

"UNITS" = "Units";
"MODE" = "Mode";

"MY_SUITE" = "My suite";
"REMOTE_UNLOCK" = "Remote Unlock";
"OUTDOOR_TEMPERATURE" = "Outdoor temp.";

// PARCELS
"PARCEL_READY_BODY" = "Hello,\nYou have a parcel waiting for you in a smart locker. Please scan your unique <bold>QR code</bold> or enter your <bold>numeric code</bold> in the locker console to retrieve your parcel <bold>as soon as possible.</bold>\n\nNumeric code: <boldBig>%@</boldBig>\nCompartment: <boldBig>%@</boldBig>";

"STRICT_PARCEL_ROOM_PARCEL_READY_BODY" = "Hello,\nYou have a delivery from %@ waiting for you in the parcel room. Please scan your unique <bold>QR code</bold> or enter your <bold>numeric code</bold> on the entry console next to the door of the parcel room to retrieve your parcel(s) <bold>as soon as possible.</bold>\n\nNumeric code: <boldBig>%@</boldBig>";

"PARCEL_READY_NO_QR_BODY" = "Hello,\nYou have a parcel waiting for you in a smart locker. Please enter your <bold>numeric code</bold> in the locker console to retrieve your parcel <bold>as soon as possible.</bold>\n\nNumeric code: <boldBig>%@</boldBig>\nCompartment: <boldBig>%@</boldBig>";

"STRICT_PARCEL_ROOM_PARCEL_READY_NO_QR_BODY" = "Hello,\nYou have a delivery from %@ waiting for you in the parcel room. Please enter your <bold>numeric code</bold> on the entry console next to the door of the parcel room to retrieve your parcel(s) <bold>as soon as possible.</bold>\n\nNumeric code: <boldBig>%@</boldBig>";

"PARCEL_READY_VIRTUAL_LOCKER" = "Hello, there is a delivery waiting for you in your building's designated area. Please retrieve your delivery as soon as possible";

"PARCEL_RETRIEVED_BODY" = "Your parcel has been retrieved using the code provided when it was initially delivered. If you have any questions please see your property manager.";

"PARCEL_SENDER" = "Parcel Delivery";

"STRICT_PARCEL_ROOM_SENDER" = "Parcel Room";

"PARCEL_READY_FOR_PICKUP" = "You have a delivery for pick-up";

// PARCEL Keys
"PARCEL_DEPOSIT_TITLE" = "Parcel Delivery";
"PARCEL_DEPOSIT_BODY" = "You have a parcel for pick-up %@";
"PARCEL_DEPOSIT_SHORT_BODY" = "You have a delivery for pick-up";

// Strict Parcel Room Keys
"STRICT_PARCEL_DEPOSIT_BODY" = "A parcel has been delivered to the parcel room by %@. Code %@";
"MULTIPLE_STRICT_PARCEL_DEPOSITS_BODY" = "%@ parcels have been delivered to the parcel room by %@. Code %@";

"PARCEL_PICKUP_TITLE" = "Parcel Retrieved";
"PARCEL_PICKUP_BODY" = "Your parcel has been retrieved";

"PARCEL_RETRIEVED" = "Your parcel has been retrieved";

"PARCEL_REMINDER_TITLE" = "Reminder to pick-up your parcel %@";
"PARCEL_REMINDER_BODY" = "Please remember to retrieve your package from the smart lockers or it will be removed by your property manager. Use your QR code or unique numerical code to retrieve your parcel.";
"PARCEL_REMINDER_VIRTUAL_LOCKER_BODY" = "This is a reminder that you have a delivery waiting for you in your building's designated area. Please retrieve your delivery as soon as possible.";

"PARCEL_ROOM_PICKUP_TITLE" = "Parcel Room Delivery";
"PARCEL_ROOM_PICKUP_BODY" = "Your parcel has been delivered to the parcel room! Please use the door card on your app to unlock the Parcel Room";

// Parcel Nudge
"PARCEL_NUDGE_BODY" = "Hello %@, We’ve received a parcel for you. Please pick it up as soon as you can.";

// GUEST ACCESS INVITE v2
"GUEST_ACCESS" = "Guest Access";
"GUEST_ACCESS_NICKNAME_SCREEN_TITLE" = "Name";
"GUEST_ACCESS_SCHEDULE_SCREEN_TITLE" = "Schedule";
"GUEST_ACCESS_REVIEW_SCREEN_TITLE" = "Review";
"GUEST_ACCESS_GUEST_DETAILS_SCREEN_TITLE" = "Guest Details";
"GUEST_ACCESS_CURRENT_ACCESS_SCREEN_TITLE" = "Invited guest";
"GUEST_ACCESS_CONTACTS_SCREEN_TITLE" = "Contacts";

"GUEST_ACCESS_CURRENT_ACCESS_FORMAT" = "Invited guests (%d)";

"GUEST_ACCESS_WHICH_DOORS_DESCRIPTION" = "Which doors would you like to give your guests access to?";
"GUEST_ACCESS_CODE_DESCRIPTION" = "Your guest will receive a door code to be used at the Entry System.";
"GUEST_ACCESS_APP_DESCRIPTION" = "Your guest will be invited to download the 1VALET app.";
"GUEST_ACCESS_NICKNAME_CODE_DESCRIPTION" = "Guests will receive a door code they can use at the door’s Entry System.";
"GUEST_ACCESS_GUEST_DETAILS_DESCRIPTION" = "The guest will see the first and last name you provide. The nickname will only be seen by you.";

"GUEST_ACCESS_NICKNAME_PLACEHOLDER" = "Food delivery";
"GUEST_ACCESS_NAME_PLACEHOLDER" = "Enter name";
"GUEST_ACCESS_OPTIONAL_NICKNAME_PLACEHOLDER" = "Enter nickname or title";
"GUEST_ACCESS_PHONE_NUMBER_PLACEHOLDER" = "Enter number";

"GUEST_ACCESS_NICKNAME_SECTION" = "Code nickname";
"GUEST_ACCESS_PHONE_NUMBER_SECTION" = "Phone number";
"GUEST_ACCESS_START_DATE_SECTION" = "Start date";
"GUEST_ACCESS_END_DATE_SECTION" = "End date";
"GUEST_ACCESS_START_TIME_SECTION" = "Start time";
"GUEST_ACCESS_END_TIME_SECTION" = "End time";
"GUEST_ACCESS_FIRST_NAME_SECTION" = "First name";
"GUEST_ACCESS_LAST_NAME_SECTION" = "Last name";
"GUEST_ACCESS_NICKNAME_OPTIONAL_SECTION" = "Nickname (Optional)";
"GUEST_ACCESS_RECENT_ACCESS_SECTION" = "Recent access";
"GUEST_ACCESS_ACCESS_INFO_SECTION" = "Access info";
"GUEST_ACCESS_REPEAT_SECTION" = "Repeat";

"GUEST_ACCESS_NEW_ACCESS_BUTTON" = "New Access";
"GUEST_ACCESS_CREATE_CODE_BUTTON" = "Create Code";
"GUEST_ACCESS_CREATE_INVITE_BUTTON" = "Create Invite";

"GUEST_ACCESS_IMPORT_FROM_CONTACTS" = "Import from contacts";
"GUEST_ACCESS_SEARCH_CONTACT_PLACEHOLDER" = "Enter name or number";

"GUEST_ACCESS_CODE_COPIED_TOAST" = "Code %@ copied!";

"GUEST_ACCESS_INVITED_BY_CURRENT_ITEM_FORMAT" = "Invited by %@";
"GUEST_ACCESS_EXPIRES_IN_CURRENT_ITEM_FORMAT" = "EXPIRES IN %@";
"GUEST_ACCESS_CODE_CURRENT_ITEM_FORMAT" = "Code: %@";

"GUEST_ACCESS_CODE_CREATED_SUCCESS" = "Code created!";
"GUEST_ACCESS_CODE_CREATED_DESCRIPTION" = "Share your code with guests below. When they arrive all they need to do is enter the code at the building intercom to enter.";
"GUEST_ACCESS_INVITE_CREATED_SUCCESS" = "Invite created!";
"GUEST_ACCESS_INVITE_CREATED_DESCRIPTION" = "Share the invite with guest below. They should download the app before they arrive.";
"GUEST_ACCESS_REVIEW_DESCRIPTION" = "Please confirm that the details are correct before sending. Keep in mind, all occupants in your suite will be able to see and edit the guest access you've made.";
"GUEST_ACCESS_SCHEDULE_UPDATED" = "Scheduled Updated!";
"GUEST_ACCESS_UPDATE_SCHEDULE_BUTTON" = "Update Schedule";

"GUEST_ACCESS_LEAVE_GUEST_DIALOG_DESCRIPTION" = "Your invitation was not sent. Are you sure you want to leave?";
"GUEST_ACCESS_LEAVE_GUEST_DIALOG_LEAVE" = "Leave";
"GUEST_ACCESS_LEAVE_GUEST_DIALOG_STAY" = "Stay";

"GUEST_ACCESS_REMOVE_GUEST_DIALOG_DESCRIPTION" = "Removing a guest will take away their access";
"GUEST_ACCESS_REMOVE_GUEST_DIALOG_GO_BACK" = "Go back";
"GUEST_ACCESS_REMOVE_GUEST_DIALOG_RMOVE" = "Remove";

"GUEST_ACCESS_SHARE_CODE_BUTTON" = "Share code";
"GUEST_ACCESS_SHARE_IVITE_BUTTON" = "Share invite";
"GUEST_ACCESS_EDIT_SCHEDULE_BUTTON" = "Edit schedule";
"GUEST_ACCESS_REMOVE_BUTTON" = "Remove";

"GUEST_ACCESS_DATE_RANGE" = "%@ on %@, till %@ on %@.";
"GUEST_ACCESS_DATE_NO_END_RANGE" = "%@ on %@, does not expire.";
"GUEST_ACCESS_NO_END_DATE" = "No end";
"GUEST_ACCESS_DOES_NOT_EXPIRE" = "does not expire";

"GUEST_ACCESS_REQUIRES_APP" = "Requires 1VALET App";
"GUEST_ACCESS_SUITE_ACCESS" = "Suite %@";

"GUEST_ACCESS_REVOKED" = "Access revoked!";
"GUEST_ACCESS_REVOKED_DESCRIPTION" = "Your guest will no longer have access to the building.";

// Building, Suite, Start Date, End Date, Console, Code, Name
"GUEST_ACCESS_SHARE_CODE_MESSAGE" = "I'm giving you the access code for %@, suite %@. You'll have continuous access from %@ to %@ Once you arrive at the %@, enter code %@ to unlock the door.

-%@";
"GUEST_ACCESS_SHARE_CODE_NO_END_MESSAGE" = "I'm giving you the access code for %@, suite %@. You'll have continuous access from %@ Once you arrive at the %@, enter code %@ to unlock the door.

-%@";
// Building, Suite, App URL, App activation code
"GUEST_ACCESS_APP_CODE_MESSAGE" = "I am giving you access to %@, suite %@.

To use your digital keys you need to download the app here %@ and activate your account using the code %@.";

// Building, Suite, Console, Code, Name
"GUEST_ACCESS_SHARE_ONE_TIME_CODE_MESSAGE" = "I'm giving you a one-time access code for %@, suite %@. Once you arrive at the %@, enter code %@ to unlock the door.  

-%@";

"GUEST_ACCESS_MORE_OPTIONS" = "More options";
"GUEST_ACCESS_FEWER_OPTIONS" = "Fewer options";
"GUEST_ACCESS_SET_TIME" = "Set time";
"GUEST_ACCESS_WEEKLY" = "Weekly";
"GUEST_ACCESS_NO_REPEAT" = "No repeat";
"GUEST_ACCESS_SELECT_REPEAT_FREQUENCY" = "Select repeat frequency"; 
"GUEST_ACCESS_NO_REPEAT_DESCRIPTION" = "for daily access like a hotel, with a set time at the beginning and end.";
"GUEST_ACCESS_WEEKLY_DESCRIPTION" = "for weekly access on specific days of the week.";
"GUEST_ACCESS_SELECT_DAYS_OF_WEEK" = "Select days of the week";
"GUEST_ACCESS_REPEAT_TITLE" = "Repeat";
"GUEST_ACCESS_ACTIVATION_CODE_FORMAT" = "Activation code: %@";
"GUEST_ACCESS_CODE_FORMAT" = "Access code: %@";
"GUEST_END_TIME_EARLY_ERROR" = "End time can’t be earlier than or same as start time.";

"GUEST_ACCESS_WIDGET_TITLE" = "Access Info";
"GUEST_ACCESS_WIDGET_OTHER_SUITES" = "Other suites";
"GUEST_ACCESS_SUITE_ADDED" = "Suite Added";
"GUEST_ACCESS_GAINED_NEW_SUITE" = "You have gained access to a new suite.";
"GUEST_ACCESS_WEEKLY_ACCESS" = "Weekly access";
"GUEST_ACCESS_DAILY_ACCESS" = "Daily access";
"GUEST_ACCESS_DOORS_UNAVAILABLE" = "Digital entry cards will be available during access period.";
"GUEST_ACCESS_CHANGE_SUITE_TOAST" = "You've switched to suite %@";
"GUEST_ACCESS_PENDING_REGISTRATION" = "Pending registration";

"GUEST_ACCESS_ONE_TIME_ACCESS_CODE" = "One-time Code";
"GUEST_ACCESS_ONE_TIME_ACCESS_CODE_CREATED_DESCRIPTION" = "Share your code with guests below. When they arrive all they need to do is enter the code at the building intercom to enter. This code will expire after one use.\n\nTo quickly create a one-time entry code, you can press and hold the \"Guests\" button on the home screen.";

// CONTACTS
"CONTACTS" = "Contacts";
"CONTACTS_PERMISSION_DIALOG" = "We need access to your contacts to be able to let you choose who to send an invite to.";

// DOCBOX
"SORT_TITLE" = "List By";
"DOCBOX_RECENT_VIEW" = "Recent";
"DOCBOX_FILES_VIEW" = "Files";
"DOCBOX_EMPTY_LIST" = "0 Files";
"DOCBOX_EMPTY_RESULTS" = "0 Results";
"DOCBOX_EXPORT" = "Export";
"DOCBOX_EMPTY_TITLE" = "No files added";
"DOCBOX_EMPTY_DESCRIPTION" = "Files added by your property manager will appear here.";

// BLUETOOTH
"BLUETOOTH_PERMISSION_TITLE" = "Bluetooth disabled";
"BLUETOOTH_PERMISSION_MESSAGE" = "Please enable bluetooth and try again.";

// AMENITY BOOKING
"AMENITY_BOOKING_BBQ" = "Barbecue";
"AMENITY_BOOKING_ELEVATOR" = "Elevator";
"AMENITY_BOOKING_GUEST_SUITE" = "Guest Suite";
"AMENITY_BOOKING_GYM" = "Gym";
"AMENITY_BOOKING_LAUNDRY" = "Laundry Room";
"AMENITY_BOOKING_MEETING" = "Meeting Room";
"AMENITY_BOOKING_MOVIE_THEATRE" = "Movie Theatre";
"AMENITY_BOOKING_PARTY" = "Party Room";
"AMENITY_BOOKING_POOL" = "Pool";
"AMENITY_BOOKING_RECREATION" = "Recreation";
"AMENITY_BOOKING_GUEST_PARKING" = "Guest Parking";
"AMENITY_BOOKING_NOTE_PLACEHOLDER" = "Max 200 characters";

"AMENITY_BOOKING_MY_BOOKINGS" = "My Bookings";
"AMENITY_BOOKING_HELP" = "If you have questions or concerns regarding Amenity Booking for your building, please contact property management.";
"AMENITY_BOOKING_START_BUTTON" = "Start Booking";
"AMENITY_BOOKING_SUCCESS_DESCRIPTION" = "You will be notified once your booking has been approved.";
"AMENITY_BOOKING_CANCEL_CONFIRMATION_FORMAT" = "Are you sure you want to cancel your %@ booking?";
"AMENITY_BOOKING_PRIVATE" = "Private use";
"AMENITY_BOOKING_SHARED" = "Shared use";
"AMENITY_BOOKING_SHARED_PRIVATE" = "Shared or private use";
"AMENITY_BOOKING_SEND_REQUEST" = "Send Request";
"AMENITY_BOOKING_CONFIRMATION" = "Please confirm the details are correct before sending. Once sent, the property management will review your requests before approving your booking.";
"AMENITY_BOOKING_DETAILS" = "Details";
"AMENITY_BOOKING_DATE_TITLE" = "Pick a date";
"AMENITY_BOOKING_TIME_TITLE" = "Pick a time";
"AMENITY_BOOKING_BOOK_PRIVATE_USE" = "Book for private use";
"AMENITY_BOOKING_AVAILABLE_TIMES" = "Available times:";
"AMENITY_BOOKING_OPTIONAL_MESSAGE" = "Optional message:";
"AMENITY_BOOKING_SENT" = "Request Sent!";
"AMENITY_BOOKING_CAPACITY_FORMAT" = "(Capacity: %d)";
"AMENITY_BOOKING_NO_AVAILABLE_TIMES" = "No available times";
"AMENITY_BOOKING_NO_LONGER_AVAILABLE" = "We’re sorry but this time is no longer available.";
"AMENITY_BOOKING_SELECT_ONE" = "Select one";

"MY_BOOKINGS_APPROVED" = "Approved";
"MY_BOOKINGS_PENDING" = "Pending";
"MY_BOOKINGS_DENIED" = "Denied";
"MY_BOOKINGS_CANCELLED" = "Cancelled";
"MY_BOOKINGS_NO_UPCOMING" = "No upcoming bookings";
"MY_BOOKINGS_NO_PAST" = "No past bookings (Last 30 days)";

"AMENITY_BOOKING_UP_TO_UNIT_FORMAT" = "Up to %d %@";
"AMENITY_BOOKING_CAPACITY_UNIT_FORMAT" = "%d %@";
"AMENITY_BOOKING_NUMBER_OF_UNIT_FORMAT" = "Number of %@";
"AMENITY_BOOKING_HOURS_UNIT_FORMAT" = "%@ %@";

"AMENITY_BOOKING_LIMIT_PERIOD_DAILY" = "day";
"AMENITY_BOOKING_LIMIT_PERIOD_WEEKLY" = "week";
"AMENITY_BOOKING_LIMIT_PERIOD_MONTHLY" = "month";

"AMENITY_BOOKING_POLICY" = "Policy";
"AMENITY_BOOK_AGAIN" = "Book again";
"AMENITY_VIEW_ALL_BOOKINGS" = "View all bookings";
"AMENITY_FUTURE_BOOKINGS" = "Future bookings will appear here";
"AMENITY_LAST_BOOKED_ON" = "Last booked on ";
"AMENITY_CHOOSE_CATEGORY" = "Choose a category";
"AMENITY_MY_BOOKINGS" = "My Bookings";
"AMENITY_UPCOMING" = "Upcoming";
"AMENITY_PAST_BOOKINGS" = "Past bookings";
"AMENITY_QUESTIONS" = "Questions?";
"AMENITY_PREFERRED_METHOD" = "If you have questions or concerns regarding your booking, please contact your Property Management using your building’s preferred method of communication:";
"AMENITY_QUESTIONS_CONTACT_MANAGEMENT" = "If you have questions or concerns regarding your booking, please contact property management.";
"AMENITY_CONTACT_MANAGEMENT" = "Contact Building Management";
"AMENITY_NO_AMENITY_BOOKINGS" = "No amenity bookings";
"AMENITY_UPCOMING_BOOKINGS_HERE" = "Upcoming and past amenity bookings will appear here.";
"AMENITY_PAY_IN_PERSON" = "Pay in person";
"AMENITY_BANK_ACCOUNT" = "Bank accounts cannot be used for amenity payments.";
"AMENITY_BANK_ACCOUNT_DIALOG" = "Bank accounts cannot be used for amenity payments. Please select a debit or credit card instead.";

// DAYS OF WEEK
"DAY_MONDAY_SHORT" = "Mon";
"DAY_TUESDAY_SHORT" = "Tue";
"DAY_WEDNESDAY_SHORT" = "Wed";
"DAY_THURSDAY_SHORT" = "Thu";
"DAY_FRIDAY_SHORT" = "Fri";
"DAY_SATURDAY_SHORT" = "Sat";
"DAY_SUNDAY_SHORT" = "Sun";

"DAY_MONDAY_LONG" = "Monday";
"DAY_TUESDAY_LONG" = "Tuesday";
"DAY_WEDNESDAY_LONG" = "Wednesday";
"DAY_THURSDAY_LONG" = "Thursday";
"DAY_FRIDAY_LONG" = "Friday";
"DAY_SATURDAY_LONG" = "Saturday";
"DAY_SUNDAY_LONG" = "Sunday";

// FUTURE RESIDENTS
"AVAILABLE_SERVICES" = "Available Services";
"UNTIL_MOVE_IN" = "until move in";
"FUTURE_RESIDENT_WELCOME_MESSAGE_OWNER" = "You will have full access to the features in this app on your move-in date. For now, check out our services below.";
"HOME_FUTURE_RESIDENT_TITLE" = "Prepare for your move";

// Services
"INSURANCE" = "Insurance";
"CLEANING" = "Cleaning";
"NO_SERVICES_LOCALE" = "No services available in your selected language";

// More
"MORE_DOCBOX" = "DocBox";
"MORE_PROFILE_SECTION" = "Profile";
"MORE_SUPPORT_SECTION" = "Help & Support";
"MORE_ACCOUNT_INFO" = "Account Info";
"MORE_FACIAL_ENTRY" = "Facial Entry";
"MORE_CALL_SETTINGS" = "Call Settings";
"MORE_EMAIL" = "Email 1VALET";
"MORE_CALL" = "Call 1VALET";
"MORE_HELP_CENTER" = "Help Center";
"MORE_EMERGENCY_CONTACT" = "Emergency Contact";
"MORE_SIGN_OUT" = "Sign Out";
"MORE_SUITE_FORMAT" = "Suite %@";
"MORE_SUITE_CODE_FORMAT" = "Suite %@ / Code %@";
"MORE_DARK_MODE" = "Dark Mode";
"MORE_VERSION" = "Version %@";
"MORE_SUITE" = "Suites";
"MORE_SUITE_DESCRIPTION" = "Choose a suite";
"MORE_TITLE" = "...More";
"MORE_FEATURES_SECTION" = "Features";
"MORE_1VALET_SUPPORT" = "1VALET Support";
"MORE_MY_BUILDING" = "My Building";
"MORE_MY_ACCOUNT" = "My Account";
"MORE_ACCOUNT_SETTINGS" = "Account settings";
"MORE_NEED_ASSISTANCE" = "Need assistance?";
"MORE_RESOURCES" = "Resources";
"MORE_MY_FEATURES" = "My features";
"MORE_SUBMIT_MAINTENANCE" = "Submit Maintenance Request";
"MORE_BACKUP_BUILDING_ACCESS" = "Backup Building Access";
"MORE_INTERCOM_CALL_SETTINGS" = "Intercom & Call Settings";
"MORE_MY_PAYMENTS" = "My Payments";
"MORE_MY_DELIVERIES" = "My Deliveries";
"MORE_MY_MAINTENANCE_REQUESTS" = "My Maintenance Requests";
"MORE_MY_INBOX" = "My Inbox";
"MORE_RESOURCES_DOCBOX_DESCRIPTION" = "View documents related to your building and suite";
"MORE_RESOURCES_BUILDING_QUESTIONS_DESCRIPTION" = "Building rules and policies";
"MORE_SUITE_SELECTED" = "Suite Selected";
"MORE_ADD_ADDITIONAL_SUITE" = "Add additional suite";

// Profile
"PROFILE_SIGN_IN_CREDENTIALS" = "Sign in credentials";
"PROFILE_USERNAME" = "Username";
"PROFILE_PASSCODE" = "Passcode";
"PROFILE_CONTACT_INFO" = "Contact info";
"PROFILE_BUILDING_ACCESS" = "Building access";
"PROFILE_EMAIL" = "Email";
"PROFILE_PHONE" = "Account phone number";
"PROFILE_ACCOUNT_INFO" = "Account info";
"PROFILE_LIMITED_MOBILITY" = "Limited Mobility";
"PROFILE_OFFLINE_QR_CODE" = "Offline QR code";
"PROFILE_QR_CODE_TITLE" = "The entry system is offline or powered off. If the entry system is still on, please scan this QR code to enter.";
"PROFILE_CHANGE_EMAIL_ALERT" = "Please talk to property management to edit the email on your account.";
"PROFILE_CREDENTIALS" = "1VALET sign-in credentials";
"PROFILE_APP_SETTINGS" = "App settings";
"PROFILE_MOBILITY" = "Mobility accommodation";
"PROFILE_CHANGE_DIALOG" = "You can’t edit this but if this information is inaccurate, please contact your property manager.";

// Dark Mode
"DARK_MODE_SUBTITLE" = "Dark mode settings";
"DARK_MODE_SYSTEM" = "System";
"DARK_MODE_LIGHT" = "Off";
"DARK_MODE_DARK" = "On";

// Tabs
"TAB_HOME" = "Home";
"TAB_AMENITY_BOOKING" = "Amenities";
"TAB_MARKETPLACE" = "Marketplace";
"TAB_MORE" = "More";
"TAB_INBOX" = "Inbox";

"MENU_SERVICES" = "Resident Services";

// Email Username
"EMAIL_USERNAME_CHOOSE_TITLE" = "Enter your Email Address";
"EMAIL_USERNAME_CHOOSE_SUBTITLE" = "Your email address will be used as your username.";
"EMAIL_CHOOSE" = "Receive important updates from management directly to your inbox";
"EMAIL_IN_USE" = "Oops! Looks like this email has been used for another account. Please enter a different email.";
"EMAIL_NONE" = "I do not have an email address";
"EMAIL_CHANGE_BELOW" = "Enter your email address below.";

// Inbox
"INBOX_EMPTY_NOTIFICATIONS" = "You have no new notifications";
"INBOX_EMPTY_MESSAGES" = "You have no new messages";
"TIME_AGO" = "%@ ago";

"FIRST_TIME_LOGIN_DIALOG" = "Please check your account details for any errors and contact your property management if any changes are required.";

// Discover
"DISCOVER_DOCBOX_PREVIEW" = "All your building documents in one place";
"DISCOVER_FACIAL_PREVIEW" = "A fun and quick way to enter your building";
"DISCOVER_GUEST_ACCESS_PREVIEW" = "Smartphone building entry for guests";
"DISCOVER_DOOR_SENSOR" = "Door Sensor Alerts";
"DISCOVER_DOOR_SENSOR_PREVIEW" = "Get peace of mind while you’re away";
"DISCOVER_CONVERSATIONS_PREVIEW" = "Chat with management right from your inbox";
"DISCOVER_DOCBOX_DESCRIPTION" = "Find your important building related documents all in one place.\n\nYour property manager may add things like meeting notes, appliance manuals, welcome documents, and more.\n\nDocBox can be found by going to the “More” section in the app.";
"DISCOVER_FACIAL_DESCRIPTION" = "With a simple smile quickly enter your building with Facial Entry.\n\nEasy to set up, secure, and controlled by you. Try this fun new way to access your building!";
"DISCOVER_GUEST_ACCESS_APP_DESCRIPTION" = "Next time friends or family plan on coming over, provide them with instant access to your building, and suite.

Simply tap the Guest Access button in the top row of the home screen to get started, and follow the prompts.

If you're giving them building entry only, your guest will receive an invite with a code to use. All they have to do when they arrive is <bold>enter the code</bold> at the entry system, and the door will unlock.

If you're giving them building entry and suite entry, they'll receive an email with a link to download the 1VALET App to access their digital keys.";
"DISCOVER_GUEST_ACCESS_CODE_DESCRIPTION" = "Next time friends or family plan on coming over, provide them with instant access to your building.

Simply tap the Guest Access button in the top row of the home screen to get started.

Your guest will receive an invite with a code to use. All they have to do when they arrive is <bold>enter the code</bold> at the Entry System, and the door will unlock.";

"DISCOVER_DOOR_SENSOR_DESCRIPTION" = "Enjoy the added security of being notified when your suite door is opened without a pincode.\n\nWhen leaving, simply tap the “Door Sensors” action from the home screen to turn on your door sensors.\n\nOthers in your household will also receive the same notifications, so everyone is aware of what is happening.";
"DISCOVER_GUEST_ACCESS_ALERT" = "Please remember to follow local Covid-19 protocols.";
"DISCOVER_GO_NOW" = "Go now";
"DISCOVER_TRY_NOW" = "Try now";
"DISCOVER_HOME" = "Discover";
"DISCOVER_ENTRY_SYSTEM_CALLS" = "Entry System Calls";
"DISCOVER_ENTRY_SYSTEM_PREVIEW" = "Choose how to answer calls from guests";
"DISCOVER_ENTRY_SYSTEM_DESCRIPTION" = "Calls from the Entry System are received as one-way video calls to the 1VALET app. If a video call doesn’t connect, it will be forwarded to your phone.\n\nYou can also choose to forward all Entry System calls to any phone to be received as a regular phone call.\n\nYou can change this setting at any time under <bold>Call Settings</bold> in the <bold>More</bold> section of the app.";
"DISCOVER_AVAILABLE_UPON_MOVE_IN" = "Available upon move-in";
"DISCOVER_ONLY_AMENITY_PAYMENTS_AVAILABLE" = "Only amenity payments available until move-in";
"DISCOVER_CONVERSATIONS" = "Conversations";
"DISCOVER_CONVERSATIONS_DESCRIPTION" = "Need to chat with your property management? Now you can communicate with them via chat messages directly from your Resident App.\n\nSimply click on the Inbox at the bottom of the Home screen of the app, and you’ll see a green chat icon at the bottom right.\n\nClick on the icon to compose your message, which can include photos. New and ongoing conversations can be found in the Messages tab of the Inbox.";
"DISCOVER_MARKETPLACE" = "Marketplace";
"DISCOVER_MARKETPLACE_PREVIEW" = "Buy and sell items with others in your building";
"DISCOVER_MARKETPLACE_DESCRIPTION" = "Welcome to Marketplace, a space where you can buy & sell items, offer a service, or organize an activity in your community, all from the 1VALET app.\n\nAccess Marketplace by selecting the new Marketplace icon located on the bottom navigation bar.";
"DISCOVER_MAINTENANCE_REQUESTS" = "Maintenance";
"DISCOVER_MAINTENANCE_REQUESTS_PREVIEW" = "Track requests right from your app";
"DISCOVER_MAINTENANCE_REQUESTS_DESCRIPTION" = "Is something awry with the plumbing in your unit? Or maybe a cabinet unexpectedly broke in your kitchen?\n\nMake a request for maintenance or repair right from your Resident App, under the More section.\n\nSelect your issue, and add in details, including photos and whether it’s an urgent request or not. Entry details for your unit can also be added to the request.\n\nOnce submitted, you can track the status of your requests in the app.";
"DISCOVER_PAYMENTS" = "Payments";
"DISCOVER_PAYMENTS_RENT_PREVIEW" = "A convenient way to pay your balance";
"DISCOVER_PAYMENTS_RENT_DESCRIPTION" = "Making payments has never been easier with 1VALET’s in-app payment feature.\n\nMake a one-time payment or set up <bold>Balance AutoPay</bold> for ease of mind, knowing you’ll always make payments when they’re due.\n\nYour balance will always show on the home screen where it’s easy to view at any time. Tap below to get started.";
"DISCOVER_PAYMENTS_AMENITY_PREVIEW" = "A convenient way to pay for amenities";
"DISCOVER_PAYMENTS_AMENITY_DESCRIPTION" = "Making payments has never been easier with 1VALET’s in-app payment feature.\n\nYou can now book and pay for certain amenities that require deposits or booking fees directly from the app.\n\nSimply select an amenity that requires deposits or booking fees, and start the booking process. During the booking process, you can select a debit or credit card to pay for the amenity.";
"DISCOVER_PAYMENTS_AMENITY_RENT_PREVIEW" = "A convenient way to pay with 1VALET";
"DISCOVER_PAYMENTS_AMENITY_RENT_DESCRIPTION" = "Making payments has never been easier with 1VALET’s in-app payment feature.\n\nMake a one-time payment or set up <bold>Balance AutoPay</bold> for ease of mind, knowing you’ll always make payments when they’re due. You can also book and pay for certain amenities that require deposits or booking fees directly from the app.\n\nYour balance will always show on the home screen where it’s easy to view at any time. Tap below to get started.";
"DISCOVER_LOCAL_SERVICES" = "Local Services";
"DISCOVER_LOCAL_SERVICES_PREVIEW" = "Order services in your area";
"DISCOVER_LOCAL_SERVICES_DESCRIPTION" = "Marketplace now has local services! Find new services in your area like house cleaning and dog walking. Available services depend on your location. You can also recommend a new service here.\n\nAccess Marketplace by selecting the new Marketplace icon located on the bottom navigation bar.";
"DISCOVER_LOCAL_SERVICES_NO_SERVICES_DESCRIPTION" = "No services are currently available for your building. Let us know of any service or small business in your area that you often use. You can also mention professional services you provide. We may be able to add these to the app.\n\n(Ex. House cleaning, dog walking, cafes, tutoring, physical training, barbershops, music lessons, catering.)";
// Digital Keyfob
"DIGITAL_KEYCARD_ENABLE_BLUETOOTH" = "Enable Bluetooth to use this card";

// Digital Access
"DIGITAL_ACCESS_KEY_CARD_TITLE" = "Find reader";
"DIGITAL_ACCESS_KEY_CARD_DESCRIPTION" = "Put your phone flat against the reader or the lock.";
"DIGITAL_ACCESS_KEY_CARD_CONNECTING" = "Connecting";
"DIGITAL_ACCESS_KEY_CARD_SUCCESS" = "Success";
"DIGITAL_ACCESS_KEY_CARD_ERROR" = "Something went wrong. Please renew your digital fob and try again.";
"DIGITAL_ACCESS_KEY_CARD_NO_ACCESS_ERROR" = "Sorry, but your card doesn't have access to this door. If you believe this is an error, please renew your digital fob and try again.";
"DIGITAL_ACCESS" = "Digital Access";
"DIGITAL_ACCESS_KEY_CARD_ENTRY" = "Key Card Entry";
"DIGITAL_ACCESS_DIGITAL_KEY_CARD" = "Digital Key Card";
"DIGITAL_ACCESS_REMOTE_UNLOCK" = "Remote Unlock";
"DIGITAL_ACCESS_VIEW_ALL_BUTTON" = "View All";
"DIGITAL_ACCESS_TIPS_MESSAGE" = "Press and drag up or down to change the order of remote unlock doors.";
"DIGITAL_ACCESS_REMOTE_UNLOCKING" = "Unlocking %@";
"DIGITAL_ACCESS_REMOTE_UNLOCKED" = "%@ unlocked!";
"DIGITAL_ACCESS_REMOTE_ERROR" = "There was an error unlocking the %@";
"DIGITAL_ACCESS_REMOTE_LOCK_ERROR" = "There was an error locking the %@";
"DIGITAL_ACCESS_OOPS" = "Oops!";
"DIGITAL_ACCESS_ALWAYS_USE_BLUETOOTH" = "Always use bluetooth";
"DIGITAL_ACCESS_FOB_RENEWED" = "Digital fob Renewed!";
"DIGITAL_ACCESS_RENEW_FAILED" = "Something went wrong. Please make sure you have access to the internet, or try again later.";
"DIGITAL_ACCESS_TIMED_OUT" = "Digital fob reader not detected. Please bring your phone to the fob reader and try again.";
"DIGITAL_ACCESS_RENEW" = "Renew digital fob";
"DIGITAL_ACCESS_RENEWING" = "Renewing digital fob";
"DIGITAL_ACCESS_EXPIRED" = "Your digital fob is expired. Please renew your digital fob and try again.";
"DIGITAL_ACCESS_KEY_CARD_ERROR_LOGGED_OUT" = "Something went wrong.";
"DIGITAL_ACCESS_KEY_CARD_NO_ACCESS_ERROR_LOGGED_OUT" = "Sorry, but your card doesn't have access to this door.";
"DIGITAL_ACCESS_CONFIGURE_BLUETOOTH_DIALOG" = "You can also configure bluetooth settings by pressing and holding on your suite entry card.";
"DIGITAL_ACCESS_GENERIC_ERROR_MESSAGE" = "Something went wrong. Please try again.";

// Version Update
"VERSION_UPDATE_WELCOME" = "Welcome to the new design of our app! We’ve received a lot of your feedback and are very excited for you to try out the new look and feel.";

// Attachments
"COMMUNITY_MESSAGE_NUMBER_OF_ATTACHMENTS" = "Attachments (%d)";
"COMMUNITY_MESSAGE_DELETED" = "The message has been deleted";

// Maintenance Request
"MAINTENANCE_REQUESTS" = "Maintenance Requests";
"MAINTENANCE_REQUEST_START_REQUEST" = "Start Request";
"MAINTENANCE_REQUEST_YOUR_REQUESTS" = "Your requests";
"MAINTENANCE_REQUEST_NEW_REQUEST" = "New Request";
"MAINTENANCE_REQUEST_NAME" = "Maintenance";
"MAINTENANCE_REQUEST_CHOOSE_CATEGORY" = "Hi, please choose a category for your maintenance request.";
"MAINTENANCE_REQUEST_CHOOSE_SUBCATEGORY" = "Choose a sub-category.";
"MAINTENANCE_REQUEST_IS_URGENT" = "Is this request urgent?";
"MAINTENANCE_REQUEST_PROVIDE_DETAILS" = "Please provide some details about your issue.";
"MAINTENANCE_REQUEST_GIVE_PERMISSION" = "Do you give permission for staff to enter your suite if needed?";
"MAINTENANCE_REQUEST_ACCESS_INSTRUCTIONS" = "Please provide access Instructions if needed.";
"MAINTENANCE_REQUEST_READY_TO_SUBMIT" = "Great, you’re all set! Ready to submit?\n\nYou’ll be able to view the progress of this request once it is submitted.";
"MAINTENANCE_REQUEST_SUBMIT" = "Submit";
"MAINTENANCE_REQUEST_MORE_OPTIONS" = "More options";
"MAINTENANCE_REQUEST_MESSAGE_PLACEHOLDER" = "Message";
"MAINTENANCE_REQUEST_CAMERA_ATTACHMENTS" = "Camera";
"MAINTENANCE_REQUEST_PHOTOS_ATTACHMENTS" = "Photos";
"MAINTENANCE_REQUEST_DOCUMENTS_ATTACHMENTS" = "Documents";
"MAINTENANCE_REQUEST_NONE" = "None of these";
"MAINTENANCE_REQUEST_LEAVE_DIALOG" = "You are about to cancel this request. Are you sure you want to leave?";
"MAINTENANCE_REQUEST_NO_REQUESTS" = "Currently no requests have been made";
"MAINTENANCE_REQUEST_SUCCESS" = "Thank you for submitting your request (#%@).";
"MAINTENANCE_REQUEST_REQUEST_NUMBER" = "Request #%@";
"MAINTENANCE_REQUEST_MORE_DETAILS_DIALOG" = "Would you like to add any more details or attachments before sending?";
"MAINTENANCE_REQUEST_ATTACHMENT_TOO_BIG" = "Attachments can’t be more than <%.0fMB>";
"MAINTENANCE_REQUEST_ATTACHMENTS_TOTAL_TOO_BIG" = "Attachments have reached the limit of <%.0fMB>";

// Door Sensor
"DOOR_SENSOR" = "Door Sensors";
"DOOR_SENSOR_DELAY_PERIOD" = "Delay period";
"DOOR_SENSOR_PINCODE" = "Pincode";
"DOOR_SENSOR_SETTINGS_DESCRIPTION" = "Changes here will affect everyone in your suite.";

"DOOR_SENSOR_DELAY_PERIOD_DESCRIPTION" = "Set the entry and exit delay period.";

"DOOR_SENSOR_CHANGE_PINCODE_TITLE" = "Change Pincode";
"DOOR_SENSOR_CHANGE_PINCODE_CURRENT_DESCRIPTION" = "To change the master pincode, you must first enter your current master pincode.";
"DOOR_SENSOR_CHANGE_PINCODE_NEW_DESCRIPTION" = "Enter a new master pincode";
"DOOR_SENSOR_FORGOT_PINCODE" = "Forgot pincode";
"DOOR_SENSOR_FORGOT_PINCODE_DESCRIPTION" = "We’ll send you a verification code to the number associated with your account.";
"DOOR_SENSOR_FORGOT_PINCODE_SEND_CODE" = "Send code";

"DOOR_SENSOR_LEARN_MORE_DESCRIPTION" = "Next time you leave your suite, turn on the door sensors from the home screen. While you’re away, you’ll get notified if the door is opened without a pincode.\n\nWhen you arrive back home simply enter the pincode to turn off your door sensors.\n\nChange your door sensor settings anytime from the More menu.";

"DOOR_SENSOR_SETUP_PIN_DESCRIPTION" = "Create a master pincode used by everyone in the suite to turn off the sensors. This can be changed anytime.";
"DOOR_SENSOR_SETUP_DELAY_DESCRIPTION" = "Choose the delay period you’ll have for leaving the suite, or turning off the sensors before an alert is sent. This can be changed anytime.";
"DOOR_SENSOR_SETUP_SUCCESS_DESCRIPTION" = "Great you’re all set!\n\nSimply tap the door sensors quick action next time you’re headed out. ";

"DOOR_SENSOR_PIN_EASY_ERROR" = "Sorry, this pincode is too easy to guess";
"DOOR_SENSOR_SAME_PIN_ERROR" = "Please enter something other than your current pin.";
"DOOR_SENSOR_INVALID_PIN" = "Please enter a valid pincode.";
"DOOR_SENSOR_LOCKED_PIN" = "You are locked out as you have entered the wrong pincode multiple times. Please try again in a little while.";
"DOOR_SENSOR_DISARM_DESCRIPTION" = "Enter the master pincode to turn off the door sensors";
"DOOR_SENSOR_DISARM_TRIGGERED_DESCRIPTION" = " Your door has been opened. Enter the master pincode to turn off the door sensors.";

"DOOR_SENSOR_TOAST" = "Delay period has started. Tap “Door Sensor” again if you wish to turn off the sensor.";

"DOOR_SENSOR_QUICK_ACTION_TITLE" = "Door sensor";

// Verification Code
"VERIFICATION_CODE" = "Verification";
"VERIFICATION_CODE_DESCRIPTION" = "Enter your verification number";
"VERIFICATION_CODE_NO_CODE" = "I have not received a verification code";
"VERIFICATION_CODE_INVALID" = "Please enter a valid verification number.";
"VERIFICATION_CODE_MAX_ATTEMPTS" = "Please try again after some time.";

// Help Center
"HELP_CENTER" = "Help Center";
"HELP_CENTER_BUILDING_QUESTIONS" = "Building Questions";
"HELP_CENTER_APP_QUESTIONS" = "Do you have a question about the 1VALET App?";
"HELP_CENTER_GET_MORE_HELP" = "Get More Help";
"HELP_CENTER_CONTACT_APP_SUPPORT" = "Contact 1VALET support";
"HELP_CENTER_CONTACT_SUPPORT_EMAIL" = "Email";
"HELP_CENTER_CONTACT_SUPPORT_CALL" = "Call";
"HELP_CENTER_CONTACT_SUPPORT_GO_BACK" = "Go back";
"HELP_CENTER_CONTACT_1VALET_SUPPORT" = "Contact 1VALET support";
"HELP_CENTER_VIEW_MORE" = "View More";
"HELP_CENTER_NEED_HELP" = "Need help?";
"HELP_CENTER_MAINTENANCE_REQUEST_INQUIRES" = "For Maintenance Related Inquiries…";
"HELP_CENTER_BUILDING_INQUIRES" = "For Other Building Inquiries…";
"HELP_CENTER_NO_BUILDING_INQUIRES" = "For other building inquiries please contact your front office.";
"HELP_CENTER_NO_INQUIRES" = "Please contact your front office.";
"HELP_CENTER_EMAIL_MANAGEMENT" = "Email Management";
"HELP_CENTER_CALL_MANAGEMENT" = "Call Management";
"HELP_CENTER_EMAIL_APP_SUPPORT" = "Email App Support";
"HELP_CENTER_CALL_APP_SUPPORT" = "Call App Support";
"HELP_CENTER_EMAIL_SUBJECT" = "Message from %@ %@ in %@, %@"; //Message from {FirstName} {Last Name} in {Suite Number}, {Building Name}
"HELP_CENTER_BUILDING_QUESTIONS_DESCRIPTION" = "E.g. questions about your building's policies, rules, and procedures, etc.";
"HELP_CENTER_APP_QUESTIONS_DESCRIPTION" = "For all app-related questions, please consult help.1valet.com for a comprehensive list of help topics and step-by-step tutorials.\n\nIf you can't find what you're looking for, contact <NAME_EMAIL>";
"HELP_BUILDING_NO_RESULTS" = "No results found";
"BUILDING_QUESTIONS_NO_RESULTS" = "Sorry, we couldn’t find any results. Check for any misspelled words and try different terms to describe what you want to find.";
"HELP_CENTER_1VALET" = "1VALET Help Center";
"HELP_CENTER_VISIT_WEB" = "Visit help.1valet.com";
"HELP_CENTER_NO_BUILDING_QUESTIONS" = "Frequently asked questions that are curated by your property manager will appear here.";

// Help Resources
"HELP_RESOURCES" = "Help Resources";
"HELP_RESOURCES_MAINTENANCE_ISSUES" = "For maintenance-related issues";
"HELP_RESOURCES_START_REQUEST" = "Start Request";
"HELP_RESOURCES_BUILDING_QUESTIONS" = "For other building-related questions";
"HELP_RESOURCES_CONTACT_MANAGEMENT" = "Contact Management";
"HELP_RESOURCES_APP_ISSUES" = "For 1VALET app-related issues";
"HELP_RESOURCES_CONTACT_SUPPORT" = "Contact 1VALET Support";

// Technician / Staff Access
"TECHNICIAN" = "Technician";
"TECHNICIAN_ACCESS_REQUEST" = "Access Request";
"TECHNICIAN_REQUEST_ACCESS" = "Access Request";
"TECHNICIAN_REQUEST_REPAIR_IN_PROGRESS" = "Authorized entry in progress";
"TECHNICIAN_REQUEST_TEMPORARY_ACCESS" = "Temporary access";
"TECHNICIAN_REQUEST_TECHNICIAN_REQUESTING_ACCESS" = "Access request";
"TECHNICIAN_REQUEST_DESCRIPTION" = "%@ is requesting app access for troubleshooting and repairs. During this time they will have access to your resident app as if they were another occupant. You may remove them at any time."; // Name
"TECHNICIAN_REQUEST_GRANTED_DESCRIPTION" = "You have granted access to building staff member %@.";
"TECHNICIAN_REQUEST_OTHER_GRANTED_DESCRIPTION" = "%@ has granted access to %@.";
"TECHNICIAN_REQUEST_TIME_OF_REQUEST" = "Time of request";
"TECHNICIAN_REQUEST_TIME_OF_APPROVAL" = "Time of approval";
"TECHNICIAN_REQUEST_CANCELLED" = "The request to access your suite has been cancelled";
"TECHNICIAN_REQUEST_REQUESTING" = "A member of building staff is requesting access to your suite";
"TECHNICIAN_REQUEST_APPROVE_DIALOG" = "Are you sure you want to approve this request?";
"TECHNICIAN_REQUEST_DENY_DIALOG" = "Are you sure you want to deny this request?";
"TECHNICIAN_REQUEST_REVOKE_DIALOG" = "Are you sure you want to revoke %@'s access to your suite?";
"TECHNICIAN_REQUEST_APPROVED_DESCRIPTION" = "You have approved %@'s access request."; // Name
"TECHNICIAN_REQUEST_DENIED_DESCRIPTION" = "You have denied %@'s access request."; // Name
"TECHNICIAN_REQUEST_REVOKED_DESCRIPTION" = "You have revoked %@'s access."; // Name
"TECHNICIAN_REQUEST_TECHNICIAN_LEFT" = "The building staff member you granted access to has left your suite.";
"TECHNICIAN_REQUEST_REVOKED" = "Access revoked";
"TECHNICIAN_REQUEST_DENIED" = "Access denied";
"TECHNICIAN_REQUEST_APPROVED" = "Access granted";
"TECHNICIAN_REQUEST_REVOKE" = "Revoke Access";
"TECHNICIAN_REQUEST_APPROVE" = "Approve";
"TECHNICIAN_REQUEST_DENY" = "Deny";
"TECHNICIAN_REQUEST_REVIEW_NOW" = "Review Now";
"TECHNICIAN_REQUEST_CONFIRM" = "Confirm";
"STAFF_ENTRY_TITLE" = "Authorized Entry";
"STAFF_ENTRY_APPROVED_DESCRIPTION" = "Staff members have a temporary access to your suite";

"SUITE_BUILDING_CODE" = "Buzzer code: %@";
"SUITE_DOOR" = "Suite Door";
"SUITE_DOOR_LOCK" = "Lock";
"SUITE_DOOR_UNLOCK" = "Unlock";
"SUITE_DETAILS" = "Suite Details";
"SUITE_MEMBERS" = "Suite members";
"SUITE_DOOR_CREATE_PIN" = "Create pin code";
"SUITE_DOOR_RESET_MASTERCODE" = "Reset Mastercode";
"SUTE_DOOR_MASTERCODE" = "Suite Mastercode";
"SUITE_DOOR_PAIR_DESCRIPTION" = "Place hand over keypad to activate lock.\n\nMake sure to enable Bluetooth on your mobile device and bring it close to the lock.";
"SUITE_DOOR_PAIR_LOCK" = "Pair Lock";
"SUITE_DOOR_PIN_NICKNAME" = "Enter a nickname for your pin code";
"SUITE_DOOR_NEW_PIN" = "Enter a new pin code";
"SUITE_DOOR_CREATE_SUCCESS" = "A new pin code was created.";
"SUITE_DOOR_PINS" = "Suite door pin codes";
"SUITE_DOOR_DELETE_SUCCESS_DESCRIPTION" = "You've successfully deleted the pin code.";
"SUITE_DOOR_RESET_MASTERCODE_SUCCESS_DESCRIPTION" = "A new suite mastercode has been created.";
"SUITE_DOOR_RESET_MASTERCODE_DIALOG" = "You are about to reset the suite mastercode.";
"SUITE_DOOR_RESET" = "Reset";
"SUITE_DOOR_DELETE_DIALOG" = "You are about to delete %@ Pin.";
"SUITE_DOOR_LOCKING" = "Locking %@";
"SUITE_DOOR_LOCKED" = "%@ Locked!";
"SUITE_DOOR_UNLOCKING" = "Unlocking Suite Door";
"SUITE_DOOR_UNLOCKED" = "Suite Door unlocked!";
"SUITE_DOOR_MASTER_PIN_FAIL_RESETTING_DIALOG_TITLE" = "Failure reseting";
"SUITE_DOOR_MASTER_PIN_FAIL_RESETTING_DIALOG_DESCRIPTION" = "Lock must be reset again, otherwise the lock won’t work.";
"SUITE_DOOR_PIN_USE_SHORT" = "Use 4-digit pin";
"SUITE_DOOR_PIN_USE_LONG" = "Use 6-digit pin";
"SUITE_DOOR_LONG_PRESS_TIP" = "Tip: You can press and hold any button on your suite lock keypad to lock the door.";
"SUITE_DOOR_MORE_TIP" = "Tip: Go to Suite Details in More to edit the pin codes of your lock.";
"SUITE_DOOR_ALERT_TIP" = "Hold down any suite door for options like locking and find tips for using your new smart locks.";
"SUITE_DOOR_TOAST" = "Hold down any suite door to see more options like locking your door.";
"SUITE_DOOR_PIN_NAME_IN_USE" = "Sorry, this name is already being used.";
"SUITE_DOOR_MAX_PINS_DIALOG" = "You’ve reached the maximum of %d pin codes.";
"SUITE_DOOR_CONNECT_FAIL" = "Something went wrong. Please make sure you are within range.";
"SUITE_DOOR_OPERATE_FAIL" = "Something went wrong. Please try again.";
"SUITE_DOOR_DUPLICATE_PIN_ERROR" = "Error. Please try a different pin.";
"SUITE_DOOR_FIRST_TIME_DIALOG" = "Please remember to reset your suite door lock mastercode, and delete any old pin codes. Go to <bold>Suite Details<bold> in the <bold>More<bold> section of the app.";
"SUITE_DOOR_TRY_AGAIN_BLUETOOTH" = "Try again with bluetooth";
"SUITE_DOOR_PINS_TITLE" = "Door Lock Pin Codes";

// Missed Calls Flow
"ENTRY_SYSTEM_CALLS" = "Entry System Calls";
"ENTRY_SYSTEM_ONBOARDING_FOOTER" = "You can change this setting at any time under <bold>Call Settings</bold> in the <bold>More</bold> section of the app.\n\n*Guests will not be able to see you.";
"ENTRY_SYSTEM_CALLS_CONFIRM_NUMBER" = "Confirm Number";
"ENTRY_SYSTEM_CALLS_CONFIRM_NUMBER_ONE_WAY_DESCRIPTION" = "Calls from the Entry System are received as one-way video calls to the 1VALET app. If a video call doesn’t connect, it will be forwarded to the phone number below.";
"ENTRY_SYSTEM_CALLS_CONFIRM_NUMBER_FORWARDING_DESCRIPTION" = "Please confirm the phone number you’d like to use for call forwarding.";

// Marketplace
"MARKETPLACE" = "Marketplace";
"MARKETPLACE_BUY_SELL_ITEM" = "Buy/sell an item";
"MARKETPLACE_BUY_SELL_SERVICE" = "Buy/sell a service";
"MARKETPLACE_ORGANIZE_ACTIVITY" = "Organize an activity";
"MARKETPLACE_SELECT_POST_CATEGORY" = "Select Post Category";
"MARKETPLACE_TITLE_OF_POST" = "Title";
"MARKETPLACE_FOR_SALE" = "For sale";
"MARKETPLACE_PRICE" = "Price";
"MARKETPLACE_DESCRIPTION" = "Description";
"MARKETPLACE_DESCRIPTION_PLACEHOLDER" = "Max 200 Characters";
"MARKETPLACE_PHOTOS" = "Photos";
"MARKETPLACE_NAME" = "Contact name";
"MARKETPLACE_CONTACT_INFO_EMAIL_PHONE" = "Contact info (Email/Phone #)";
"MARKETPLACE_SUBMIT_POST" = "Submit Post";
"MARKETPLACE_PREVIEW_POST" = "Preview Post";
"MARKETPLACE_CANCEL" = "Cancel";
"MARKETPLACE_OK" = "Ok";
"MARKETPLACE_GO_BACK" = "Go Back";
"MARKETPLACE_CONFIRMATION_DELETE_POST" = "Are you sure you want to leave without saving?";
"MARKETPLACE_CONFIRMATION_WONT_SAVE_CHANGES_POST" = "Are you sure you want to cancel? Your post will not be edited.";
"MARKETPLACE_POST_SUBMITTED" = "Post Submitted";
"MARKETPLACE_POST_REMOVED_AFTER_90_DAYS" = "Your post will be automatically removed after 90 days.";
"MARKETPLACE_CREATE_POST" = "Create Post";
"MARKETPLACE_ITEMS" = "Items";
"MARKETPLACE_SERVICES_POST" = "Services";
"MARKETPLACE_ACTIVITIES" = "Activities";
"MARKETPLACE_ALL_TYPES" = "All";
"MARKETPLACE_DESCRIPTION_MAX_LIMIT_VALIDATION" = "Description field must be under %@ characters.";
"MARKETPLACE_TITLE_VALIDATION" = "Title";
"MARKETPLACE_TITLE_NOT_EMPTY_VALIDATION" = "Please add a title to this post in order to submit or preview it.";
"MARKETPLACE_TITLE_LIMIT_VALIDATION" = "Title field must be under %@ characters.";
"MARKETPLACE_CATEGORY_NOT_EMPTY_VALIDATION" = "Please choose a category in order to submit or preview it.";
"MARKETPLACE_NAME_NOT_EMPTY_VALIDATION" = "Please add a name to this post in order to submit or preview it.";
"MARKETPLACE_NAME_LIMIT_VALIDATION" = "Name field must be under %@ characters.";
"MARKETPLACE_CONTACT_NOT_EMPTY_VALIDATION" = "Please add contact info to this post in order to submit or preview it.";
"MARKETPLACE_CONTACT_LIMIT_VALIDATION" = "Contact field must be under %@ characters.";
"MARKETPLACE_CONTACT_INFO_VALIDATION" = "Contact Info";
"MARKETPLACE_CONTACT_MUST_BE_EMAIL_PHONE_VALIDATION" = "Contact field must be either a valid email or a valid phone number.";
"MARKETPLACE_DESCRIPTION_NOT_EMPTY_VALIDATION" = "Please add a description to this post in order to submit or preview it.";
"MARKETPLACE_ALL_FIELDS_VALIDATION" = "Please add all required fields to this post in order to submit or preview it.";
"MARKETPLACE_YOUR_POSTS" = "Your posts";
"MARKETPLACE_POSTS" = "Community Posts";
"MARKETPLACE_SOLD" = "SOLD";
"MARKETPLACE_NO_POSTS" = "No posts yet!";
"MARKETPLACE_NO_POSTS_SUBTITLE" = "No one in your building has added a post yet. Add one below!";
"MARKETPLACE_MARK_AS_SOLD_CONFIRMATION" = "Are you sure you want to mark this post as sold?";
"MARKETPLACE_REMOVE_POST_CONFIRMATION" = "Are you sure you want to remove this post?";
"MARKETPLACE_CONFIRM" = "Confirm";
"MARKETPLACE_REMOVE" = "Remove";
"MARKETPLACE_NO_FILTERS" = "No filters selected!";
"MARKETPLACE_FLAG_POST" = "Report Post";
"MARKETPLACE_REMOVE_POST" = "Remove";
"MARKETPLACE_ITEM_SOLD" = "Item Sold";
"MARKETPLACE_EDIT_POST" = "Edit Post";
"MARKETPLACE_SAVE_CHANGES" = "Save Changes";
"MARKETPLACE_POST_FLAGGED_SUCCESS_TITLE" = "Post Reported";
"MARKETPLACE_POST_FLAGGED_SUCCESS_MESSAGE" = "The content of this post has been flagged as inappropriate. To escalate further, contact your property manager.";
"MARKETPLACE_FLAG_POST_CONFIRMATION" = "Are you sure you want to report this post as inappropriate?";
"MARKETPLACE_VIEW_POST" = "View Post";
"MARKETPLACE_FREE" = "Free";
"MARKETPLACE_POST_SUBMITTED" = "Post Submitted";
"MARKETPLACE_POST_AUTOMATIC_REMOVAL" = "Your post will automatically be removed after 90 days.";
"MARKETPLACE_POST_NO_MATCH_FOR_SEARCH" = "Sorry, we didn't find any match for \"%@\". Please try another search, check for any typo, or browse by categories";
"MARKETPLACE_COMMUNITY" = "My Community";
"MARKETPLACE_COMMUNITY_BUY_AND_SELL" = "Buy and Sell";
"MARKETPLACE_COMMUNITY_BUY_AND_SELL_DESCRIPTION" = "Find stuff to buy in your community";
"MARKETPLACE_SAVE" = "Save";
"MARKETPLACE_NO_RESULTS" = "No results found";
"MARKETPLACE_NOTE_PLACEHOLDER" = "Max 200 characters";
"MARKETPLACE_OPTIONAL_MESSAGE" = "Optional message:";
"MARKETPLACE_SHOPS_BOOK_AGAIN" = "Book again";
"MARKETPLACE_SHOPS_LOCAL" = "Local Shops";
"MARKETPLACE_SHOPS_SERVICES" = "Services";
"MARKETPLACE_SHOPS_FOOD" = "Food";
"MARKETPLACE_SHOPS_SERVICES_AND_SERVICES" = "Services + Food";
"MARKETPLACE_SHOPS_RECOMMEND_SHOPS" = "Recommend";
"MARKETPLACE_SHOPS_RECOMMEND_A_SHOP" = "Recommend a service";
"MARKETPLACE_SHOPS_RECOMMEND_A_BUSINESS" = "Recommend small businesses";
"MARKETPLACE_SHOPS_RECOMMEND_SERVICES" = "Know any small businesses you’d like to recommend?";
"MARKETPLACE_SHOPS_RECOMMEND_RESTAURANTS" = "Know any restaurants you’d like to recommend?";
"MARKETPLACE_SHOPS_SUBMIT_SUGGESTIONS" = "Submit";
"MARKETPLACE_SHOP_RECOMMENDATIONS_DESCRIPTION" = "Let us know of any service or small business in your area that you often use. You can also mention professional services you provide. We may be able to add these to the app.\n\n(Ex. House cleaning, dog walking, cafes, tutoring, physical training, barbershops, music lessons, catering.)";
"MARKETPLACE_SHOPS_RECOMMEND_INSTAGRAM" = "DM local shops on Instagram with our onboarding webpage for shops";
"MARKETPLACE_SHOPS_RECOMMEND_YOUR_SUGGESTIONS" = "Your recommendations";
"MARKETPLACE_SHOPS_RECOMMEND_PLACEHOLDER" = "Acme Dog Walking (363-123-1232), Sam’s Cafe...";
"MARKETPLACE_SHOPS_DISCOVER_SHOPS" = "Discover";
"MARKETPLACE_SHOPS_POPULAR" = "Popular";
"MARKETPLACE_SHOPS_CHOOSE_OPTIONS" = "Choose options below";
"MARKETPLACE_SHOPS_SHOP_STAFF" = "Staff";
"MARKETPLACE_SHOPS_LOCAL_SERVICES_IN_AREA" = "Local services in your area";
"MARKETPLACE_SHOPS_ANY_STAFF" = "Any staff member";
"MARKETPLACE_SHOPS_MY_BOOKINGS" = "My Bookings";
"MARKETPLACE_SHOPS_NEW_SHOPS_ADDED" = "New services added!";
"MARKETPLACE_SHOPS_NEW_ITEMS_ADDED" = "New items added!";
"MARKETPLACE_SHOPS_PRICING" = "Pricing:";
"MARKETPLACE_SHOPS_CANCELLATIONS" = "Cancellations:";
"MARKETPLACE_SHOPS_CONTACT_INFO" = "Contact info:";
"MARKETPLACE_SHOPS_EMAIL" = "Email (Optional)";
"MARKETPLACE_SHOPS_FIRST_NAME" = "First name";
"MARKETPLACE_SHOPS_LAST_NAME" = "Last name";
"MARKETPLACE_SHOPS_PHONE_NUMBER" = "Phone Number";
"MARKETPLACE_SHOPS_EDIT_ORDER" = "Edit your booking";
"MARKETPLACE_SHOPS_BOOK_NOW" = "Book now";
"MARKETPLACE_SHOPS_BOOKING_MADE" = "Booking Made";
"MARKETPLACE_SHOPS_BOOKING_MADE_DESCRIPTION" = "We'll see you soon!";
"MARKETPLACE_SHOPS_VIEW_MORE_DETAILS" = "View more details";
"MARKETPLACE_SHOPS_PICK_TIME" = "Pick a time";
"MARKETPLACE_SHOPS_TODAY" = "Today";
"MARKETPLACE_SHOPS_CLOSED" = "Closed";
"MARKETPLACE_SHOPS_SERVICE_OPTIONS" = "%d options";
"MARKETPLACE_SHOPS_SERVICE_DURATION_MINUTES" = "%d min";
"MARKETPLACE_SHOPS_CHOOSE_ONE" = "(Choose 1)";
"MARKETPLACE_SHOPS_CHOOSE_UP_TO" = "(Choose up to %d)";
"MARKETPLACE_SHOPS_YOUR_BOOKING" = "Your Booking";
"MARKETPLACE_SHOPS_SERVICE_WITH" = "Service with %@";
"MARKETPLACE_SHOPS_CANCEL_DESCRIPTION" = "Please contact the business as soon as possible.";
"MARKETPLACE_SHOPS_CANCEL_BOOKING" = "Are you sure you want to cancel this booking?";
"MARKETPLACE_SHOPS_TOTAL" = "Total (Pay in person)";
"MARKETPLACE_SHOPS_AGREE_TERMS_CONDITIONS" = "I agree to 1VALET’s Terms & Conditions.";
"MARKETPLACE_SHOPS_HOURS" = "Hours";
"MARKETPLACE_SHOPS_NEXT_AVAILABLE" = "Next available day %@";
"MARKETPLACE_SHOPS_ALL_BOOKED" = "There are no available time slots";
"MARKETPLACE_SHOPS_RECOMMENDATION_TOAST" = "Thank you for the recommendations, we really appreciate it!";
"MARKETPLACE_SHOPS_FEEDBACK_TITLE" = "Feedback";
"MARKETPLACE_SHOPS_FEEDBACK_DESCRIPTION" = "Tell us how your experience was with %@.";
"MARKETPLACE_SHOPS_FEEDBACK_BUTTON" = "How was your experience?";
"MARKETPLACE_SHOPS_DISTANCE_FROM_BUILDING" = "(%@ away)";
"MARKETPLACE_SHOPS_RECOMMEND_BUTTON_TITLE" = "Want more services?";
"MARKETPLACE_SHOPS_RECOMMEND_BUTTON_DESCRIPTION" = "Recommend some in your area";
"MARKETPLACE_SHOPS_CANCEL_ORDER_CALL_DESCRIPTION" = "Please call the business as soon as possible to cancel your booking.";
"MARKETPLACE_SHOPS_CANCEL_ORDER_CALL" = "Call";
"MARKETPLACE_SHOPS_CANCEL_ORDER_GO_BACK" = "Go Back";
"MARKETPLACE_SHOPS_CHANGE_INFO_FIRST_NAME_PLACEHOLDER" = "Enter first name";
"MARKETPLACE_SHOPS_CHANGE_INFO_LAST_NAME_PLACEHOLDER" = "Enter last name";
"MARKETPLACE_SHOPS_CHANGE_INFO_EMAIL_PLACEHOLDER" = "Enter email";
"MARKETPLACE_SHOPS_CHANGE_INFO_PHONE_NUMBER_PLACEHOLDER" = "Enter phone number";
"MARKETPLACE_SHOPS_OPTIONAL_EMAIL_DESCRIPTION" = "Enter an email address so that we can send you booking updates and important messages.";
"MARKETPLACE_SHOPS_CONTACT_EMAIL_TITLE" = "Contact email";
"MARKETPLACE_SHOPS_SKIP_BUTTON" = "Skip";
"MARKETPLACE_SHOPS_NO_UPCOMING_BOOKING" = "No upcoming bookings";
"MARKETPLACE_SHOPS_NO_PAST_BOOKING" = "No past bookings";
"MARKETPLACE_SHOPS_CARD_UPCOMING_BOOKING" = "Upcoming booking with %@ on %@ at %@";
"MARKETPLACE_SHOPS_BUSINESS_NOT_AVAILABLE" = "Sorry this business is no longer available in the app.";
"MARKETPLACE_SHOPS_SERVICE_NOT_AVAILABLE" = "Sorry this service is no longer available in the app.";
"MARKETPLACE_SHOPS_ADDRESS_NOT_FOUND" = "Address not found";
"MARKETPLACE_SHOPS_FUTURE_BOOKINGS_PLACEHOLDER" = "Future bookings will appear here";
"MARKETPLACE_SHOPS_ADD_EMAIL_TO_ACCOUNT" = "Add this email to my account";
"MARKETPLACE_FEEDBACK_TOAST" = "Thank you for providing feedback, we really appreciate it!";
"MARKETPLACE_SHOPS_PRE_LAUNCH" = "[Pre Launch]";
"MARKETPLACE_COMMUNITY_CATEGORY" = "Category";
"MARKETPLACE_COMMUNITY_SELECT_CATEGORY" = "Select a category";
"MARKETPLACE_COMMUNITY_STATUS_PENDING" = "Pending";
"MARKETPLACE_COMMUNITY_STATUS_APPROVED" = "Approved";
"MARKETPLACE_COMMUNITY_STATUS_SOLD" = "Sold";
"MARKETPLACE_COMMUNITY_STATUS_DENIED" = "Denied";
"MARKETPLACE_COMMUNITY_STATUS_REMOVED" = "Removed";
"MARKETPLACE_POSTED_ON_DATE" = "Posted on %@";
"MARKETPLACE_COMMUNITY_BANNED" = "You are banned from posting in Marketplace. Please contact management for more information.";
"MARKETPLACE_COMMUNITY_MY_POSTS" = "My posts";
"MARKETPLACE_COMMUNITY_ACTIVE" = "Active";
"MARKETPLACE_COMMUNITY_ARCHIVE" = "Archived";
"MARKETPLACE_COMMUNITY_VIEW_MY_POSTS" = "View my posts";

"IMAGE_INDEX" = "%@ of %@";
"SEARCH_PLACEHOLDER" = "Search";

// Messages
"MESSAGES_MAINTENANCE" = "Maintenance";
"MESSAGES_FAQ" = "FAQ";
"MESSAGES_GENERAL_INQUIRY" = "General Inquiry";
"MESSAGES_NEW_MESSAGE" = "New Conversation";
"MESSAGES_CONTINUE" = "Continue";
"MESSAGES_CANCEL" = "Cancel";
"MESSAGES_SELECT_CATEGORY" = "Select a category";
"MESSAGES_ENTER_TITLE" = "Enter a subject";
"MESSAGES_TITLE_MAX_CHARACTERS" = "Max 40 Characters";
"MESSAGES_ENTER_MESSAGE" = "Enter your message";
"MESSAGES_MESSAGE_MAX_CHARACTERS" = "(Max 750 Characters)";
"MESSAGES_ATTACH_FILES" = "Attach files";
"MESSAGES_SEND_MESSAGE" = "Send";
"MESSAGES_SINGLE_ATTACHMENT_MAX_SIZE" = "Attachments can’t be more than 10MB";
"MESSAGES_ALL_ATTACHMENTS_MAX_SIZE" = "Attachments have reached the limit of 25MB";
"MESSAGES_CANCEL_MESSAGE_CONFIRMATION" = "Are you sure you want to cancel this message?";
"MESSAGES_SUCCESS" = "Success";
"MESSAGES_MESSAGE_SENT_FIRST_LABEL" = "Thank you for submitting your message";
"MESSAGES_MESSAGE_SENT_SECOND_LABEL" = "Is there anything else we can help you with?";
"MESSAGE" = "Message";
"MESSAGES_CONVERSATION_CLOSED_LABEL" = "This conversation is closed.";
"MESSAGES_START_NEW_LABEL" = "START NEW CONVERSATION";
"MESSAGES_END_CONVERSATION" = "END CONVERSATION";
"MESSAGES_END_CONVERSATION_WARNING" = "Are you sure you want to end the conversation with Property Management?";
"MESSAGES_DELETE_FOREVER" = "You will no longer be able to view conversation";
"MESSAGES_START_CONVERSATION_BLOCKED" = "We’re sorry.";
"MESSAGES_START_CONVERSATION_BLOCKED_MESSAGE_ONE" = "Your building’s Property Management has turned off the ability for residents to start a conversation through the 1VALET app. Instead, please use your building’s preferred method of communication:";
"MESSAGES_START_CONVERSATION_BLOCKED_MESSAGE_TWO" = "Please note that you will still be able to respond to conversations initiated by Property Management";

"CONVERSATION_NOTIFICATION_TITLE" = "Conversation";
"CONVERSATION_NEW_REPLY_NOTIFICATION_COMPLETE_BODY" = "Property Management at %@ has replied to a conversation with you. Open the 1VALET app to view, or reply to it.";
"CONVERSATION_NEW_CONVERSATION_NOTIFICATION_COMPLETE_BODY" = "Property Management at %@ has started a conversation with you. Open the 1VALET app to view, or reply to it.";
"CONVERSATION_CONVERSATION_RESOLVED_NOTIFICATION_COMPLETE_BODY" = "Property Management at %@ has marked a conversation with you as resolved. Open the 1VALET app to view it.";

"PAYMENT_EMAIL_CHANGED_NOTIFICATION_TITLE" = "Payments";
"PAYMENT_EMAIL_CHANGED_NOTIFICATION_COMPLETE_BODY" = "Your email has been changed to %@ by property management. Any payment notifications and receipts will be sent there.";

"PAYMENT_METHOD_SETUP_STATUS_CHANGED_NOTIFICATION_TITLE" = "Payments";
"PAYMENT_METHOD_SETUP_STATUS_CHANGED_FAILURE_NOTIFICATION_COMPLETE_BODY" = "There was a problem verifying your bank account. Please try again.";

"PAD_PAYMENT_FAILED_NOTIFICATION_TITLE" = "Payments";
"PAD_PAYMENT_FAILED_NOTIFICATION_COMPLETE_BODY" = "Bank payment could not be processed. Contact your bank for more details.";

"PAYMENTS_NOTIFICATION_TITLE" = "Payments";
"PAYMENTS_BALANCE_DUE_NOTIFICATION_COMPLETE_BODY" = "Your balance payment is due in %@, on %@. Disregard if you’ve set up automatic payments outside of the app.";
"PAYMENTS_BALANCE_DUE_NOTIFICATION_AUTOPAY_OFF_COMPLETE_BODY" = "Your balance payment is due in %@, on %@. Disregard if you’ve set up automatic payments outside of the app.";
"PAYMENTS_BALANCE_DUE_NOTIFICATION_AUTOPAY_ON_COMPLETE_BODY" = "You have an upcoming automatic payment that will be withdrawn in %@, on %@.";
"PAYMENTS_UPCOMING_AUTOMATIC_PAYMENT_NOTIFICATION_COMPLETE_BODY" = "You have an upcoming automatic payment that will be withdrawn in %@, on %@.";
"PAYMENTS_AUTOPAY_DECLINED_BY_BANK_NOTIFICATION_COMPLETE_BODY" = "AutoPay payment for %@ failed. Please verify your payment method details and try again using the one-time payment action in the app to avoid possible late fees from your property.";
"PAYMENTS_AUTOPAY_DECLINED_NOTIFICATION_WITH_AMOUNT_COMPLETE_BODY" = "AutoPay payment for %@ failed. Please try again using the one-time payment action in the app to avoid possible late fees from your property.";
"PAYMENTS_AUTOPAY_DECLINED_NOTIFICATION_WITHOUT_AMOUNT_COMPLETE_BODY" = "AutoPay payment failed. Please try again using the one-time payment action in the app to avoid possible late fees from your property.";
"PAYMENTS_ALREADY_MADE_FOR_SUITE_NOTIFICATION_COMPLETE_BODY" = "A payment has been made recently by another occupant in your suite. You will need to restart the payment process.";

"CAMERA" = "Camera";
"PHOTOS" = "Photos";
"DOCUMENTS" = "Documents";
"DRAFT" = "[DRAFT]";
"YOU" = "You";
"PROPERTY_MANAGEMENT" = "Property Management";
"TOO_MANY_FILES_LABEL" = "You are attempting to attach too many files.";

"SUITE_INSPECTION_INSTRUCTION" = "Please inspect each item in the list below:";
"SUITE_INSPECTION_MOVE_IN_INSPECTION" = "Move-in inspection";
"SUITE_INSPECTION_SUBMIT_REPORT" = "Sign & Submit";
"SUITE_INSPECTION_REPORT_SUBMITTED" = "Move-in Inspection Submitted";
"SUITE_INSPECTION_BEGIN_BUTTON" = "Begin Inspection";
"SUITE_INSPECTION_ISSUES_FOUND" = "Issues found";
"SUITE_INSPECTION_DESCRIBE_ISSUE" = "Please describe the issue below:";
"SUITE_INSPECTION_PM_MESSAGE" = "Message from your building management:";
"SUITE_INSPECTION_TOGGLE_CONFIRMATION_DESCRIPTION" = "This will approve all items and remove any comments and pictures.";
"SUITE_INSPECTION_TOGGLE_CONFIRMATION_CONFIRM_BUTTON" = "Confirm";
"SUITE_INSPECTION_SAVE_BUTTON" = "Save";
"SUITE_INSPECTION_VIEW_BUTTON" = "View";
"SUITE_INSPECTION_BANNER_DESCRIPTION" = "Please complete your suite inspection";
"SUITE_INSPECTION_NO_ISSUES_FOUND" = "No issues found";
"SUITE_INSPECTION_NO_MORE_ISSUES_FOUND" = "No more issues found";
"SUITE_INSPECTION_DESCRIPTION_PLACEHOLDER" = "Description (max %d characters)";
"SUITE_INSPECTION_SINGLE_ATTACHMENT_MAX_SIZE" = "Attachments can’t be more than %.1fMB";
"SUITE_INSPECTION_ALL_ATTACHMENTS_MAX_SIZE" = "Attachments have reached the limit of %.1fMB";
"SUITE_INSPECTION_ENTER_VALID_NAME" = "Please enter a valid name.";
"SUITE_INSPECTION_SUBMIT_BUTTON" = "Submit";
"SUITE_INSPECTION_SIGN_ACKNOWLEDGE" = "By typing your name below, you are signing the move-in inspection as complete, and accurate.";
"SUITE_INSPECTION_SIGN_HINT" = "Type your name %@, in the space above.";

"PAYMENTS" = "Payments";
"PAYMENTS_VIEW_DETAILS" = "View Details";
"PAYMENTS_CURRENT_BALANCE" = "Current Balance";
"PAYMENTS_AUTO_PAY" = "AutoPay";
"PAYMENTS_REFRESH_NOW" = "Refresh now";
"PAYMENTS_PAY_BALANCE" = "Pay Balance";
"PAYMENTS_SETTINGS" = "Payment Settings";
"PAYMENTS_HISTORY" = "Payment History";
"PAYMENTS_FEES_WILL_BE_CALCULATED" = "Please note service fees will be calculated on the next step.";
"PAYMENTS_BALANCE_PAYMENT" = "Balance Payment";
"PAYMENTS_METHOD" = "Payment Method";
"PAYMENTS_ADD_METHOD" = "Add payment method";
"PAYMENTS_AMOUNT" = "Payment amount";
"PAYMENTS_LESS_THAN_BALANCE_MESSAGE" = "Property management has set it so payments cannot be less than your balance owing.";
"PAYMENTS_MORE_THAN_LIMIT_MESSAGE" = "Property management has set it so payments cannot be over $10,000.";
"PAYMENTS_MORE_THAN_LIMIT_ERROR" = "The total payment amount of %@ (%@, plus the service fee of %@) exceeds the maximum payment amount of %@.";
"PAYMENTS_LESS_THAN_MINIMUM" = "The payment amount must be at least %@";
"PAYMENTS_UNVERIFIED_BANK_ACCOUNT_MESSAGE" = "This account has not been verified yet. Please follow the instructions in the emails sent to %@.";
"PAYMENTS_VERIFICATION_ERROR_BANK_ACCOUNT_MESSAGE" = "There was a problem verifying your account. Please remove it and try again.";
"PAYMENTS_CARD_EXPIRED_MESSAGE" = "This credit card has expired. Please delete it and add a new card.";
"PAYMENTS_CREDIT_DEBIT_CARD" = "Credit / Debit Card";
"PAYMENTS_TYPE" = "Payment type";
"PAYMENTS_SUCCESS" = "Submitted";
"PAYMENTS_SERVICE_FEE" = "Service fee";
"PAYMENTS_TOTAL" = "Total";
"PAYMENTS_AGREE_TO" = "I agree to ";
"PAYMENTS_1VALET_TERMS" = "1VALET’s Terms & Conditions";
"PAYMENTS_AGREE_PAY_FULL_BALANCE_FEE" = " based on the full balance at time of the charge";
"PAYMENTS_AGREE_PAY_FEE_OF" = " of %@";
"PAYMENTS_AGREE_PAY_SERVICE_FEE" = "I agree to pay the ";
"PAYMENTS_PAY_NOW" = "Pay Now";
"PAYMENTS_REVIEW" = "Review";
"PAYMENTS_LAST_UPDATED" = "Last updated: %@";
"PAYMENTS_CURRENT_BALANCE_WITH_AMOUNT" = "Current balance (%@)";
"PAYMENTS_PAYMENT_SUBMITTED" = "Thank you, your payment of %@ has been submitted.";
"PAYMENTS_PROBLEM_PROCESSING_PAYMENT" = "There was a problem processing your payment. Reason: %@";
"PAYMENTS_PAYMENT_SUBMITTED_WITH_EMAIL" = "Thank you, your payment of %@ has been submitted. A receipt will be sent to %@ shortly.";
"PAYMENTS_EMAIL_RECEIPT" = "Email Receipt";
"PAYMENTS_NO_THANKS" = "No thanks";
"PAYMENTS_EMAIL_INVALID" = "Please enter a valid email address.";
"PAYMENTS_ADD_EMAIL" = "Add email";
"PAYMENTS_EMAIL_ADDED" = "Email Added";
"PAYMENTS_ENTER_EMAIL" = "Enter an email address so that we can send you transaction receipts and messages from property managment.";
"PAYMENTS_SAVE" = "Save";
"PAYMENTS_CONTACT_EMAIL" = "Contact email";
"PAYMENTS_RECEIPT_WILL_BE_SENT_TO_EMAIL" = "A receipt will be sent to %@ shortly.";
"PAYMENTS_DELETE_CONFIRMATION" = "Are you sure you want to delete %@?";
"PAYMENTS_PAYMENT_METHODS" = "Payment methods";
"PAYMENTS_NO_HISTORY" = "You haven’t made any payments through the app yet.";
"PAYMENTS_VIEW_RECEIPT" = "View Receipt";
"PAYMENTS_PENDING" = "Pending";
"PAYMENTS_PROCESSED" = "Processed";
"PAYMENTS_FEE_AMOUNT" = "Fee %@";
"PAYMENTS_PAY_FULL_BALANCE_REQUIRED" = "Your building's Property Management requires you to pay the full balance.";
"PAYMENTS_SERVICE_FEES_VARY" = "Service fees vary depending on the payment method and the amount being paid. Adjust the amount below to see the estimated fee.";
"PAYMENTS_SERVICE_FEES" = "Service Fees";
"PAYMENTS_ESTIMATED_FEES" = "Estimated fees";
"PAYMENTS_DEBIT" = "Debit";
"PAYMENTS_OTHER_CREDIT" = "Other credit cards";
"PAYMENTS_OTHER_DEBIT" = "Other debit cards";
"PAYMENTS_BANK_ACCOUNT" = "Bank Account";
"PAYMENTS_UPDATE_PENDING" = "Balance update pending";
"PAYMENTS_BANK_METHODS_NOT_ALLOWED" = "Bank payment methods are no longer accepted by your building. Please choose another payment method.";
"PAYMENTS_CARD_METHODS_NOT_ALLOWED" = "Card payment methods are no longer accepted by your building. Please choose another payment method.";
"PAYMENTS_INTERNATIONAL_CARDS_NOT_ALLOWED_WARNING" = "International cards cannot be used to make payments.";
"PAYMENTS_MANDATE_INACTIVE" = "There is a problem with your payment method and it cannot be used for future payments. Please delete the payment method and add another one to continue making payments.";
"PAYMENTS_CARD_BRAND_INVALID" = "Your building has restricted this card brand (%@) from being used for payments. Please try using a different card.";
"PAYMENTS_INTERNATIONAL_CARDS_NOT_ALLOWED_POPUP" = "International cards cannot be used to make payments. Please try using a different card.";
"PAYMENTS_DETAILS" = "Details";
"PAYMENTS_REFUND" = "Refund %@";
"PAYMENTS_HISTORY_TOTAL" = "Total: %@";
"PAYMENTS_CONFIRM_INFORMATION" = "If your information is incorrect please talk to property management before continuing.";
"PAYMENTS_NAME" = "Name";
"PAYMENTS_EMAIL" = "Email";
"PAYMENTS_EMAIL_REQUIRED" = "An email is required to add this payment method.";
"PAYMENTS_BANK_ACCOUNTS_NEED_VERIFICATION" = "Note: bank accounts will need to be verified before they can be used to pay. This could take 2-5 days.";
"PAYMENTS_BANK_PENDING_PAYMENT" = "Payment pending";
"PAYMENTS_BANK_PENDING_PAYMENT_POSTED_ON_SUCCESS_MESSAGE" = "You or someone in your suite has a payment that is pending. Your balance has been adjusted to assume the payment will be successful, however, if the payment fails, your balance will automatically update and reflect any additional fees your building may charge as as a result.";
"PAYMENTS_BANK_PENDING_PAYMENT_NOT_POSTED_ON_SUCCESS_MESSAGE" = "You or someone in your suite has a payment that is pending. If the payment fails, your balance will automatically update and reflect any additional fees your building may charge as as a result.";
"PAYMENTS_BANK_BALANCE_PANDING_MESSAGE" = "An update to your balance should be happening soon.";
"PAYMENTS_UNVERIFIED_PAYMENT_METHOD_CANNOT_BE_DELETED" = "The payment method cannot be deleted during verification.";
"PAYMENTS_BANK_PAYMENTS_TAKE_FIVE_DAYS_TO_PROCESS" = "Note: Payment with a bank account may take up to 5 days to process.";
"PAYMENTS_ADD_VERIFIED_PAYMENT_TO_USE_AUTOPAY" = "You will need to add a verified payment method to use 1VALET AutoPay";
"PAYMENTS_BALANCE_AUTOPAY" = "1VALET AutoPay Setup";
"PAYMENTS_TURN_OFF_AUTOPAY_WARNING" = "Are you sure you want to turn off Balance Autopay?";
"PAYMENTS_TURN_OFF" = "Turn Off";
"PAYMENTS_FULL_BALANCE" = "Full balance";
"PAYMENTS_FULL_BALANCE_AUTOPAY_ALREADY_SETUP" = "Another occupant in your suite has already set up full balance AutoPay.";
"PAYMENTS_CUSTOM_AMOUNT_AUTOPAY_ALREADY_SETUP" = "Note another occupant in your suite has set up autopay with a custom amount.";
"PAYMENTS_LAST" = "last day";
"PAYMENTS_CURRENT_AUTOPAY_CONFIGURATION_DESCRIPTION_FULL_BALANCE" = "The full balance plus an additional service fee will be charged to %@ on the %@ of every month.";
"PAYMENTS_CURRENT_AUTOPAY_CONFIGURATION_DESCRIPTION_CUSTOM_AMOUNT" = "%@ plus an additional service fee of %@ will be charged to %@ on the %@ of every month.";
"PAYMENTS_CURRENT_AUTOPAY_CONFIGURATION_DESCRIPTION_FULL_BALANCE_FEE" = "plus an additional service fee ";
"PAYMENTS_CURRENT_AUTOPAY_CONFIGURATION_DESCRIPTION_CUSTOM_AMOUNT_FEE" = "plus an additional service fee of %@ ";
"PAYMENTS_CURRENT_AUTOPAY_CONFIGURATION_DESCRIPTION_SERVICE_FEE" = " of %@";
"PAYMENTS_AUTOPAY_DESCRIPTION_AMOUNT" = "";
"PAYMENTS_AUTOPAY_DESCRIPTION" = "%@ will be charged to your selected payment method on the %@ of each month.";
"PAYMENTS_AUTOPAY_NOTE" = "Please note that if you set up AutoPay on the %@ of the month, it will not take effect until the next payment cycle.";
"PAYMENTS_THE_FULL_BALANCE" = "The full balance";
"PAYMENTS_YOUR_FULL_BALANCE" = "Your full balance";
"PAYMENTS_DELETE_AUTOPAY_METHOD_CONFIRMATION" = "Are you sure you want to delete %@, and stop AutoPay?";
"PAYMENTS_ONE_TIME_PAYMENT_WITH_AUTO_PAY_ENABLED" = "You have automatic payments enabled. Your next payment is already scheduled for the %@ day of the month. Do you still want to continue?";
"PAYMENTS_WARNING_TITLE" = "Important";
"PAYMENTS_WARNING_MESSAGE" = "If you already have payments set up with your building, please note that you will **not** see those payment methods or receipts in the 1VALET app.\n\nIf you want to use the 1VALET app for payments instead, please contact your property management to cancel any existing scheduled payments **BEFORE** you set up payments in the 1VALET app, **otherwise you will accidentally pay twice.**";
"PAYMENTS_WARNING_CHECKBOX" = "I have read and understand";
"PAYMENTS_SETTINGS_NOTE" = "Please note that if you want to make any changes to 1VALET AutoPay, you must first turn it off, and then set it up again.";
"PAYMENTS_AUTO_PAY_OFF" = "AUTOPAY OFF";
"PAYMENTS_EXTERNAL_PAYMENTS" = "External Payments";
"PAYMENTS_AUTO_PAY_ON" = "AUTOPAY ON";
"PAYMENTS_MAKE_ONE_TIME_PAYMENT" = "Make One-time Payment";
"PAYMENTS_SETTINGS_AUTO_PAY" = "Payment Settings & AutoPay";
"PAYMENTS_NEXT_PAYMENT" = "Next payment on %@";
"PAYMENTS_1VALET_AUTO_PAY" = "1VALET AutoPay";
"PAYMENTS_TURN_OFF_AUTO_PAY" = "Turn Off 1VALET AutoPay";
"PAYMENTS_TURN_ON_AUTO_PAY" = "Turn On 1VALET AutoPay";
"PAYMENTS_SELECT_PAYMENT_METHOD" = "Select payment method";
"PAYMENTS_SELECT_PAYMENT_AMOUNT" = "Select payment amount";
"PAYMENTS_ENTER_AMOUNT" = "Enter amount";
"PAYMENTS_FINISH_AUTO_PAY_SETUP" = "Finish 1VALET AutoPay setup";
"PAYMENTS_CUSTOM_AMOUNT" = "Custom amount";
"PAYMENTS_NO_EMAIL_ON_FILE" = "We currently do not have an email on file. If you would like email receipts, please add an email address.";
"PAYMENTS_1VALET_AUTOPAY_ENABLED" = "1VALET AutoPay has been turned on";
"PAYMENTS_1VALET_AUTOPAY_OFF" = "1VALET AutoPay has been turned off";
"PAYMENTS_TERMS_CONDITIONS" = "Terms and Conditions";
"PAYMENTS_THANKS_FOR_LETTING_US_KNOW" = "Thanks for letting us know!";
"PAYMENTS_WELCOME_TO_PAYMENTS" = "Welcome to 1VALET Payments";
"PAYMENTS_WELCOME_PAYMENTS_IS_EXTERNAL_DESCRIPTION" = "Okay, we've noted this on your account.\n\nIf you decide to switch to 1VALET for payments so that you can have everything all in one place, make sure to cancel any other payment arrangements before making a payment through the 1VALET app.";
"PAYMENTS_WELCOME_PAYMENTS_NOT_EXTERNAL_DESCRIPTION" = "You can set up 1VALET AutoPay, by adding your preferred payment option or go directly to your balance.";
"PAYMENTS_WELCOME_PAYMENTS_UNDEFINED_EXTERNAL_DESCRIPTION" = "Do you or another occupant in your suite have external payments for your building set up outside the 1VALET app? E.g. scheduled mobile payments using another app, automatic withdrawals through your bank account, or postdated paper cheques, or another arrangement?";
"PAYMENTS_VIEW_BALANCE" = "View Balance";
"PAYMENTS_SETUP_ONEVALET_AUTOPAY" = "Set up 1VALET AutoPay";
"PAYMENTS_SWITCH_TO_1VALET" = "Switch to 1VALET AutoPay";
"PAYMENTS_PREVIOUSLY_INDICATED_EXTERNAL_PAYMENTS" = "You have previously indicated that you or another occupant in your suite have building payments managed outside the 1VALET app.\n\nWould you like to start using 1VALET for automatic payments instead?";
"PAYMENTS_CONFIRM_EXTERNAL_CANCELLED" = "I have cancelled existing payments to ensure I will not be duplicating payments";
"PAYMENTS_ONE_MORE_THING" = "One more thing ...";
"PAYMENTS_KEEP_PAYMENT_STATUS_UPDATED" = "To keep your payment status updated, please select what you will be using for building payments?";
"PAYMENTS_1VALET_WITHOUT_AUTOPAY" = "1VALET app without AutoPay";
"PAYMENTS_EDIT_AUTOPAY" = "Edit 1VALET AutoPay";
"PAYMENTS_SAVE_AUTOPAY" = "Save 1VALET AutoPay setup";
"PAYMENTS_AUTOPAY_SAVED" = "1VALET AutoPay changes saved";
"PAYMENTS_YOUR_FULL_BALANCE" = "Your full balance";
"PAYMENTS_PLUS_SERVICE_FEE" = "%@, plus the";
"PAYMENTS_OF" = "of";
"PAYMENTS_WILL_BE_CHARGED" = "will be charged to your payment method.";
"PAYMENTS_NEXT_CYCLE_EDIT_ALERT" = "Changes made today will not take effect until your next payment cycle on %@.";
"PAYMENTS_NEXT_CYCLE_SETUP_ALERT" = "If you set up AutoPay today, your first payment will not be initiated until %@.";
"PAYMENTS_FINANCIAL_INSTITUTION" = "Financial institution";
"PAYMENTS_ROUTING_NUMBER" = "Routing number";
"PAYMENTS_ACCOUNT_NUMBER" = "Account number";
"PAYMENTS_AGREEMENT_DATE" = "Agreement date";
"PAYMENTS_MANDATE_BODY" = "By clicking \"I agree\", you authorize STATEMENT_DESCRIPTOR to debit the bank account specified above for any amount owed for charges arising from your use of STATEMENT_DESCRIPTOR’s services and/or purchase of products from STATEMENT_DESCRIPTOR, pursuant to STATEMENT_DESCRIPTOR’s website and terms, until this authorization is revoked.\n\nPayments will be debited from the specified account according to the following schedule: Upon completion of a one-time payment, or when monthly automatic payments are scheduled.\n\nYou may amend or cancel this authorization at any time by providing notice to STATEMENT_DESCRIPTOR with 30 (thirty) days notice. If you use STATEMENT_DESCRIPTOR’s services or purchase additional products periodically pursuant to STATEMENT_DESCRIPTOR’s terms, you authorize STATEMENT_DESCRIPTOR to debit your bank account periodically. Payments that fall outside of the regular debits authorized above will only be debited after your authorization is obtained.";
"PAYMENTS_SCROLL_DOWN" = "Scroll down";
"PAYMENTS_AGREE" = "I agree";
"PAYMENTS_AGREEMENT" = "Agreement";
"PAYMENTS_NEXT_PAYMENT_PAUSED" = "Next payment paused";
"PAYMENTS_AUTOPAY_PAUSED" = "AUTOPAY PAUSED";
"PAYMENTS_ISSUES_ONLINE_PAYMENTS" = "Issues with Online Payments";
"PAYMENTS_PAYMENTS_DISABLED" = "Payments Disabled";
"PAYMENTS_AUTOPAY_IS_PAUSED" = "AutoPay is paused";
"PAYMENTS_PAYMENTS_DISABLED_WARNING" = "Your <bold>property manager has disabled</bold> your ability to make online payments. Until they enable online payments for your account, you will not be able to make one-time payments or use AutoPay. Please contact your property manager for assistance to ensure your balance is paid on time.";
"PAYMENTS_AUTOPAY_PAUSED_WARNING" = "Your <bold>property manager has disabled</bold> your ability to make online payments. Please contact your property manager for assistance to ensure your balance is paid on time.";
"PAYMENTS_PAYMENT_PENDING_POP_UP_TITLE" = "Recent payments totaling %@ are still being processed by your financial institution. <bold>Making another payment could result in overpayment or a failed transaction if your bank lacks sufficient funds, potentially leading to fees from your property.<bold> Please contact your property manager for assistance before making any additional payments.";
"PAYMENTS_BALANCE_PENDING_POP_UP_TITLE" = "We're currently unable to update your balance, and <bold>recent payments totaling %@ have not yet been applied.<bold> Making another payment could result in overpayment or a failed transaction if your bank lacks sufficient funds, potentially leading to fees from your property. Please contact your property manager for assistance before making any additional payments.";
"PAYMENTS_PAYMENT_PENDING_IMMEDIATELY_WARNING" = "<bold>Recent payments totaling %@ are still being processed by your financial institution and have not yet been applied to your balance.</bold> Once processing is complete, your balance will be updated. Successful payments will decrease your balance, while failed ones may result in additional fees from your property. Please contact your property manager for assistance before making any additional payments.";
"PAYMENTS_PAYMENT_PENDING_NOT_IMMEDIATELY_WARNING" = "<bold>Recent payments totaling %@ are still being processed by your financial institution and have been applied to your balance, based on the assumption that the funds are available.</bold> If they fail (e.g., due to insufficient funds) in the next few days, your balance will be adjusted accordingly, and your property may apply additional fees. Please contact your property manager for assistance before making any additional payments.";
"PAYMENTS_BALANCE_PENDING_WARNING" = "We're currently unable to update your balance, and <bold>recent payments totaling %@ have not yet been applied.</bold> Making another payment could result in overpayment or a failed transaction if your bank lacks sufficient funds, potentially leading to fees from your property. Please contact your property manager for assistance before making any additional payments.";

"WIDGETS_AMENITY_VIEW_MORE" = "View more";
"WIDGETS_AMENITY_VIEW_ALL" = "View all";
"WIDGETS_AMENITY_TAKE_ADVANTAGE_OF_AMENITIES" = "Make the most of your living experience with amenities in your community.";
"WIDGETS_AMENITY_VIEW_BOOK_NOW" = "BOOK NOW";
"WIDGETS_AMENITY_VIEW_VIEW_BOOKING" = "VIEW BOOKING";
"WIDGETS_AMENITY_UPCOMING_BOOKING" = "Upcoming booking for %@ on %@";
"WIDGETS_MARKETPLACE_CONNECT" = "Connect with your community";
"WIDGETS_MARKETPLACE_FREE_SPACE" = "Are you looking to free up some space or have a service to offer? Let your neighbours know about it on Marketplace!";
"WIDGETS_MARKETPLACE_POST_ITEM" = "POST AN ITEM";
"WIDGETS_MARKETPLACE_COMMUNITY_POSTS" = "Community posts";
"WIDGETS_MARKETPLACE_OFFERS" = "Offers";
"WIDGETS_MARKETPLACE_HOME_INSURANCE" = "Home Insurance";
"WIDGETS_MARKETPLACE_FREE_QUOTE" = "FREE QUOTE";
"WIDGETS_MARKETPLACE_POSTED_BY" = "Posted by: %@";
"WIDGETS_DIGITAL_ACCESS_ENTRY" = "Digital Entry";
"WIDGETS_DIGITAL_ACCESS_MY_DOORS" = "My doors";
"WIDGETS_DIGITAL_ACCESS_ACCESS_ON_MOVE_IN" = "You’ll have access on move-in day";
"WIDGETS_DIGITAL_ACCESS_ACCESS_SHORTLY" = "You’ll have access shortly";
"WIDGETS_DIGITAL_ACCESS_DIGITAL_FOB" = "Digital Fob";
"WIDGETS_DIGITAL_ACCESS_REMOTE_UNLOCK" = "Remote unlock";
"WIDGETS_WELCOME_HOME_PAY" = "Pay";
"WIDGETS_WELCOME_HOME_SEND_MESSAGE" = "Message";
"WIDGETS_WELCOME_HOME_START_REQUEST" = "Request";
"WIDGETS_WELCOME_HOME_POST_ITEM" = "Post";
"WIDGETS_WELCOME_HOME_GREETING" = "Hi %@!";
"WIDGETS_WELCOME_HOME_DAYS_UNTIL_MOVIN" = "%@ until move-in!";
"WIDGETS_WELCOME_HOME_MOVIN_TODAY" = "Your move-in is today!";
"WIDGETS_WELCOME_HOME_SUITE_NUMBER" = "Suite %@";
"WIDGETS_WELCOME_HOME_GUEST_ACCESS" = "Guests";
"WIDGETS_WELCOME_HOME_DELIVERIES" = "Deliveries";
"WIDGETS_WELCOME_HOME_MY_ORDERS" = "Orders";
"WIDGETS_WELCOME_HOME_BALANCE" = "Balance";
"WIDGETS_WELCOME_HOME_NOTIFICATION_BANNER" = "All app notifications are currently disabled. You will need to enable them to receive updates.";
"WIDGETS_WELCOME_DOOR_SENSOR_ALERT" = "Alert";
"WIDGETS_ACTIVITY_FEED_DELIVERY_FOR_PICK_UP" = "You have a delivery for pick-up";
"WIDGETS_ACTIVITY_FEED_VIEW_CODE" = "View Code";
"WIDGETS_ACTIVITY_FEED_MARK_RETRIEVED" = "MARK AS RETRIEVED";
"WIDGETS_ACTIVITY_FEED_ACTIVITY" = "Activity";
"WIDGETS_ACTIVITY_FEED_CLEAR_ALL" = "Clear all";
"WIDGETS_ACTIVITY_FEED_ALL_CAUGHT_UP" = "You’re all caught up!";
"WIDGETS_ACTIVITY_FEED_NEW_NOTIFICATIONS_APPEAR_HERE" = "New notifications, alerts, and updates will appear here.";
"WIDGETS_ACTIVITY_FEED_LAST_DAY" = "Last 24 hours";
"WIDGETS_ACTIVITY_FEED_PREVIOUS_NOTIFICATIONS" = "Previous notifications";
"WIDGETS_ACTIVITY_FEED_SIT_BACK" = "You have no notifications\nso sit back and relax :)";
"WIDGETS_ACTIVITY_FEED_ALL_NOTIFICATIONS" = "All notifications, alerts, and updates will appear here.";
"WIDGETS_ACTIVITY_FEED_SMALL_NEW_NOTIFICATIONS_APPEAR_HERE" = "New notifications and updates will\nappear here.";
"WIDGETS_ACTIVITY_FEED_TODAY" = "Today";
"WIDGETS_ACTIVITY_FEED_YESTERDAY" = "Yesterday";
"WIDGETS_GUEST_ACCESS_CONTINUOUS_ACCESS" = "Continuous access";
"WIDGETS_GUEST_ACCESS_ADD_SUITE" = "Add a suite";
"WIDGETS_GUEST_ACCESS_NO_ACCESS_TITLE" = "No access at this time";
"WIDGETS_GUEST_ACCESS_NO_ACCESS_SUBTITLE" = "Access information will appear here when a resident grants building access.";
"WIDGETS_GUEST_ACCESS_ERROR_TITLE" = "Unable to retrieve access info";
"WIDGETS_GUEST_ACCESS_ERROR_SUBTITLE" = "Please try again later.";
"WIDGETS_ACTIVITY_FEED_READ_ON" = "Read on %@";
"WIDGETS_ACTIVITY_FEED_RECEIVED_ON" = "Received on %@";
"WIDGETS_ACTIVITY_FEED_VIEW_BALANCE" = "View Balance";
"WIDGETS_ACTIVITY_FEED_VIEW_BALANCE_CTA" = "View Balance";
"WIDGETS_ACTIVITY_FEED_VIEW_POST" = "View Post";
"WIDGETS_ACTIVITY_FEED_ACCOUNT_INFO" = "Account Info";
"WIDGETS_ACTIVITY_FEED_PAYMENTS_SETTINGS" = "Payment Settings";
"WIDGETS_ACTIVITY_FEED_VIEW_BOOKING" = "View Booking";
"WIDGETS_ACTIVITY_FEED_REPLY" = "Reply";
"WIDGETS_ACTIVITY_FEED_VIEW_MORE" = "View More";
"WIDGETS_ACTIVITY_FEED_ALL" = "All";
"WIDGETS_ACTIVITY_FEED_MY_ACTIVITY" = "My Activity";
"WIDGETS_ACTIVITY_FEED_UNREAD" = "Unread";
"WIDGETS_ACTIVITY_FEED_MARK_ALL_AS_READ" = "Mark all as read";
"WIDGETS_ACTIVITY_FEED_MARK_ALL_AS_READ_POP_UP" = "This action will clear all %@ notifications from loading in your activity feed on the home screen. Are you sure you want to continue?";
"WIDGETS_ACTIVITY_FEED_VIEW_REQUEST" = "View Request";
"WIDGETS_ACTIVITY_FEED_VIEW_ORDER" = "View order";
"WIDGETS_ACTIVITY_FEED_VIEW" = "View";
"WIDGETS_ACTIVITY_FEED_PARCEL_REMINDER_TITLE" = "Parcel Reminder";
"WIDGETS_ACTIVITY_FEED_REVIEW" = "Review";
"WIDGETS_ACTIVITY_FEED_PARCEL_REQUEST_EXPIRED" = "This request for access has expired. You can ignore this notification.";

"PARCELS_NEW_DELIVERIES_APPEAR_HERE" = "New deliveries will appear here.";
"PARCELS_NO_PICKED_UP" = "Picked up deliveries will appear here.";
"PARCELS_PICKED_UP" = "Picked-up";
"PARCELS_DELIVERIES" = "Deliveries";
"PARCELS_MARK_AS_RETRIEVED" = "Mark as Retrieved";
"PARCELS_SHARE_CODE" = "Share Code";
"PARCELS_MARK_AS_RETRIEVED_POP_UP" = "Once you mark this as retrieved you will no longer be able to access the numeric or QR code to get into the package room. Do you want to proceed?";
"PARCELS_DELIVERY_INFO" = "Delivery info";
"PARCELS_AWAITING_PICKUP" = "Awaiting Pick-up %@";
"PARCELS_DELIVERED_RECENT" = "Delivered %@";
"PARCELS_DELIVERED_ON" = "Delivered on %@";
"PARCELS_PICKED_UP_RECENT" = "\nPicked up %@";
"PARCELS_PICKEDUP_ON" = "\nPicked up on %@";
"PARCELS_DELIVERY_DATE" = "Delivery date: %@";
"PARCELS_PICK_UP_DATE" = "Pick-up date: %@";
"PARCELS_EMPTY_QR_CODE_TITLE" = "Don't see a delivery that should be here?";
"PARCELS_EMPTY_QR_CODE_DESCRIPTION" = "Scan this QR code at the entry system next to the parcel room door to gain access.";
"PARCELS_MARK_AS_RETRIEVED_CARD_DESCRIPTION" = "Move it to the \"Picked-up\" tab by marking it as retrieved.";
"PARCELS_MARK_AS_RETRIEVED_CARD_TITLE" = "Already picked up this delivery?";
"PARCELS_SCAN_PARCEL_ROOM" = "Scan this QR code at the entry system next to the parcel room door to gain access.";
"PARCELS_SCAN_SMART_LOCKER" = "Scan this QR code at the smart locker to gain access.";
"PARCELS_QR_CODE_ACCESS" = "QR Access Code";
"PARCELS_QR_ACCESS" = "QR Access";
"PARCELS_ROOM_EMPTY_TAP_BELOW" = "Tap on the \"QR Code\" icon below, and scan the QR code at the entry system next to the parcel room door to gain access.";
"PARCELS_REFRESH_QR_CODE" = "Refresh QR Code";

"DATA_CONNECTING_BANNER" = "Connecting";

"WIDGETS_DIGITAL_ACCESS_SUITE_NUMBER" = "SUITE #%@";
"WIDGETS_DIGITAL_ACCESS_SIGN_IN" = "Please sign into the 1VALET App to access your digital entry cards.";
"WIDGETS_DIGITAL_ACCESS_ACCESS_LATER" = "Access later today";
"WIDGETS_DIGITAL_ACCESS_ACCESS_IN_DAYS" = "Access in %@";
"WIDGETS_DIGITAL_ACCESS_OPEN_BUILDING_DOOR" = "Open your building’s doors with just one tap.";
"WIDGETS_DIGITAL_ACCESS_NO_DOORS" = "You currently don't have any digital entry cards on the 1VALET App.";
"WIDGETS_DIGITAL_ACCESS_OPEN_APP" = "OPEN APP";
"WIDGETS_DIGITAL_ACCESS_DID_YOU_KNOW" = "Did you know...";
"WIDGETS_DIGITAL_ACCESS_ADD_WIDGET" = "You can now add a widget to unlock your doors right from your device home screen!";
"WIDGETS_DIGITAL_ACCESS_LEARN_HOW" = "Learn how";
"WIDGETS_DIGITAL_ACCESS_NO_DOORS_SMALL" = "No\ndoors";
"WIDGETS_DIGITAL_ACCESS_SIGN_IN_SMALL" = "Please sign in";

"CARPLAY_LOGIN_FIRST" = "In order to use CarPlay you need to first login to your 1VALET app";
"CARPLAY_SELECT_DOOR" = "Select a door";
"CARPLAY_OPEN_DOOR" = "Open %@";

"AMENITY_BOOKING_BOOK_AGAIN" = "Book again";
"AMENITY_BOOKING_VIEW_ALL_BOOKINGS" = "View bookings";
"AMENITY_BOOKING_FUTURE_BOOKINGS_HERE" = "Future bookings will appear here";
"AMENITY_BOOKING_LAST_BOOKED" = "Last booked on ";
"AMENITY_BOOKING_CHOOSE_CATEGORY" = "Choose a category";
"AMENITY_BOOKING_PAST_BOOKINGS" = "Past bookings";
"AMENITY_BOOKING_QUESTIONS" = "Questions?";
"AMENITY_BOOKING_CONTACT_PREFERRED_METHOD" = "Your building’s Property Management has turned off the ability for residents to start a conversation through the 1VALET app. Instead, please use your building’s preferred method of communication:";
"AMENITY_BOOKING_CONTACT_PREFERRED_METHOD_SECOND" = "Please note that you will still be able to respond to conversations started by Property Management.";
"AMENITY_BOOKING_CONTACT_MESSAGE" = "If you have questions or concerns regarding your booking, please contact property management.";
"AMENITY_BOOKING_CONTACT_MANAGEMENT" = "Contact Management";
"AMENITY_BOOKING_NO_BOOKINGS" = "No amenity bookings";
"AMENITY_BOOKING_UPCOMING_HERE" = "Upcoming and past amenity bookings will appear here.";
"AMENITY_BOOKING_CANCELLED" = "Cancelled";
"AMENITY_BOOKING_CUSTOM_USE" = "Custom use";
"AMENITY_BOOKING_SOME_AMENITIES_HAVE_LIMITS" = "Some amenities have limits for how many bookings can be made within a certain time period";
"AMENITY_BOOKING_DURATION" = "Duration";
"AMENITY_BOOKING_CAPACITY" = "Capacity";
"AMENITY_BOOKING_HOURS" = "Hours";
"AMENITY_BOOKING_USAGE_TYPE" = "Usage type";
"AMENITY_BOOKING_LIMIT" = "Booking limit";
"AMENITY_BOOKING_UPCOMING" = "Upcoming bookings";
"AMENITY_BOOKING_SHOW_LESS" = "Show less";
"AMENITY_BOOKING_SHOW_MORE" = "Show more";
"AMENITY_BOOKING_AMENITY_DETAILS" = "Amenity details";
"AMENITY_BOOKING_REQUIRE_APPROVAL" = "Requires approval";
"AMENITY_BOOKING_AUTOMATIC_APPROVAL" = "Automatic approval";
"AMENITY_BOOKING_NO_LIMIT" = "No booking limit\n";
"AMENITY_BOOKING_AGREE" = "I agree";
"AMENITY_BOOKING_CHOOSE_DATE" = "Choose date";
"AMENITY_BOOKING_SOME_AMENITIES_CONSECUTIVE_SLOTS" = "Some amenities allow you to select consecutive time slots to extend your booking";
"AMENITY_BOOKING_AVALILABLE_SLOTS" = "Available time slots";
"AMENITY_BOOKING_MORE_THAN_ONE_SLOT" = "You can select more than 1 time slot";
"AMENITY_BOOKING_UP_TO_SLOTS" = "You can select up to %d %@";
"AMENITY_BOOKING_NEXT_DATE" = "Based on your selection, the next available date is ";
"AMENITY_BOOKING_NO_SLOTS" = "Based on your selection, there are no available time slots.";
"AMENITY_BOOKING_CHARGED_FULL_CAPACITY" = "Booking for private use prevents others from using the amenity during the same time slot. As a result, you will be charged for the full capacity.";
"AMENITY_BOOKING_BOOK_PRIVATE_USE_POPUP" = "Booking for private use prevents others from using the amenity during the same time slot. If there are booking fees for this amenity, the price per time slot will reflect the maximum capacity for this amenity.";
"AMENITY_BOOKING_APPLY" = "Apply";
"AMENITY_BOOKING_CLEAR_FIELD" = "Clear field";
"AMENITY_BOOKING_CONFIRMED" = "Booking Confirmed!";
"AMENITY_BOOKING_REQUEST_SENT" = "Request Sent!";
"AMENITY_BOOKING_NOTIFIED_AFTER_REVIEW" = "You will be notified once management has reviewed your booking.";
"AMENITY_BOOKING_SUPPORT" = "Amenity Support";
"AMENITY_BOOKING_YOUR_BOOKING" = "Your Booking";
"AMENITY_BOOKING_MESSAGE" = "Message management";
"AMENITY_BOOKING_CANCEL" = "Cancel booking";
"AMENITY_BOOKING_PROCEED" = "Proceed";
"AMENITY_BOOKING_BACK" = "Go back";
"AMENITY_BOOKING_BOOKING_CANCELED" = "Booking cancelled";
"AMENITY_BOOKING_ALL" = "All amenities";
"AMENITY_BOOKING_UP_TO_PER_BOOKING" = "Up to %@ %@ per booking";
"AMENITY_BOOKING_DATE_AND_CAPACITY" = "Date and %@";
"AMENITY_BOOKING_BOOKINGS_REMAINING" = "%d Bookings remaining";
"AMENITY_BOOKING_BOOKING_CONFIRMED" = "Your booking for %@ on %@ at %@ is confirmed.";
"AMENITY_BOOKING_BOOKING_CANCELLED" = "Your booking for %@ on %@ at %@ has been cancelled!";
"AMENITY_BOOKING_CANCEL_CONFIRMATION" = "Are you sure you want to cancel %@ booking?";
"AMENITY_BOOKING_BOOK_NOW" = "Book Now";
"AMENITY_BOOKING_VIEW_POLICY" = "View amenity policy";
"AMENITY_BOOKING_RETURN_TO_BOOKING" = "Return to booking";
"AMENITY_BOOKING_NEXT_POLICY" = "Next policy";
"AMENITY_BOOKING_PREV_POLICY" = "Previous policy";
"AMENITY_BOOKING_SEARCH_AMENITIES" = "Search amenities";
"AMENITY_BOOKING_PAYMENT_OPTIONS" = "Payment Options";
"AMENITY_BOOKING_DEPOSIT_AMOUNT" = "The deposit amount (%@) will be collected in the event of any incidents or damage and will be charged to your selected payment method.";
"AMENITY_BOOKING_CANCEL_NOW" = "If you cancel now, you will need to contact your property manager for refund information.";
"AMENITY_BOOKING_REFUNDS_TAKE_DAYS" = "Refunds will take a few days to process after cancellation. If you have questions, contact property management.";
"AMENITY_BOOKING_BOOKING_OUTSIDE_CANCELLATION_WINDOW" = "Sorry, your booking is outside the cancellation window, and your payment cannot be refunded. Do you still wish to cancel?";
"AMENITY_BOOKING_REFUND_IN_DAYS" = "\n\nYou will receive a refund within a few business days.";
"AMENITY_BOOKING_NO_REFUND" = "\n\nDue to the cancellation policy, you will not receive a refund.";
"AMENITY_BOOKING_VIEW_BOOKING_RECEIPT" = "View booking receipt";
"AMENITY_BOOKING_VIEW_DEPOSIT_RECEIPT" = "View deposit receipt";
"AMENITY_BOOKING_PROBLEM_PAYMENT_METHOD" = "There was a problem with your payment method. Please try again after resolving the issue with your card.";
"AMENITY_BOOKING_AMENITY_UNAVAILABLE" = "Sorry, this amenity is no longer available.";
"AMENITY_BOOKING_CHARGES_UPON_APPROVAL" = "You'll only be charged if management approves your booking request.";
"AMENITY_BOOKING_MANAGEMENT_COLLECTED" = "Management has collected %@ from your deposit on %@.";
"AMENITY_BOOKING_BOOKING_DENIED_NO_CHARGE" = "Your booking was denied by property management. Your card has not been charged.";
"AMENITY_BOOKING_BOOKING_DENIED_NO_PAYMENT" = "Your booking was denied by property management.";
"AMENITY_BOOKING_BOOKING_CANCELLED_NO_CHARGE" = "You have cancelled the booking. Your card has not been charged.";
"AMENITY_BOOKING_BOOKING_CANCELLED_FREE" = "You have cancelled the booking.";
"AMENITY_BOOKING_BOOKING_CANCELLED_REFUND" = "You have cancelled the booking. A refund was issued and will be processed within a few business days.";
"AMENITY_BOOKING_BOOKING_CANCELLED_NO_REFUND" = "You have cancelled the booking. Due to the cancellation policy, you will not receive a refund.";
"AMENITY_BOOKING_MANAGEMENT_CANCELLED_BOOKING" = "Your booking was cancelled by property management. A refund was issued and will be processed within a few business days.";
"AMENITY_BOOKING_MANAGEMENT_CANCELLED_BOOKING_NO_PAYMENT" = "Your booking was cancelled by property management.";
"AMENITY_BOOKING_MANAGEMENT_CANCELLED_BOOKING_NO_REFUND" = "Your booking was cancelled by property management. Due to the cancellation policy, you will not receive a refund.";
"AMENITY_BOOKING_CANCELLATION_POLICY" = "Cancellation Policy";
"AMENITY_BOOKING_AT" = "at";
"AMENITY_BOOKING_CANCELLATION_NON_REFUNDABLE" = "Cancellation of your booking is non-refundable by %@ If you are outside of the allowable refund period, you will not be refunded for any amenity fees already paid.";
"AMENITY_BOOKING_CANCELLATION_BEYOND_REFUND_WINDOW" = "Your booking is beyond the refund window of %@";
"AMENITY_BOOKING_CANCELLATION_DETAILS" = "Please check the amenity policy or contact management for cancellation details.";
"AMENITY_BOOKING_PAYMENT_INFO" = "Payment Info";
"AMENITY_BOOKING_AMENITY_FEE" = "Amenity fee";
"AMENITY_BOOKING_TOTAL_DUE" = "Total Due";
"AMENITY_BOOKING_TOTAL_DUE_TODAY" = "Total due today";
"AMENITY_BOOKING_TOTAL" = "Total";
"AMENITY_BOOKING_TOTAL_PAID" = "Total Paid";
"AMENITY_BOOKING_DEPOSIT" = "Deposit";
"AMENITY_BOOKING_AMENITY_CHARGES" = "Amenity charges";
"AMENITY_BOOKING_FREE" = "Free";
"AMENITY_BOOKING_AMENITY_PAYMENT" = "Amenity payment";
"AMENITY_BOOKING_AMENITY_DEPOSIT" = "Amenity deposit";
"AMENITY_BOOKING_ARRENGEMENTS" = "Please make arrangements with your property management to pay for your booking.\n\nYou will be notified once your booking has been approved or denied by management.";
"AMENITY_BOOKING_PAYMENT_WILL_BE_CHARGED" = "Your payment of <bold>%@ on %@ **** %@<bold> will be charged if your booking is approved. You will be notified once management has reviewed your booking.";
"AMENITY_BOOKING_NO_EMAIL_ON_FILE" = "\n\nWe currently do not have an email on file. If you would like email receipts, please add an email address.";
"AMENITY_BOOKING_PER_TIME_SLOT" = "per time slot";
"AMENITY_BOOKING_PER_NIGHT" = "per night";
"AMENITY_BOOKING_PER" = "per";
"AMENITY_REFUND_ERROR" = "Refund error";
"AMENITY_CONTACT_LATER" = "Contact Later";
"AMENITY_BOOKING_CANCELLED_REFUND_ERROR" = "Your booking for %@ on %@ at %@ has been cancelled, but there was an issue processing your refund. Please contact your property manager.";
"AMENITY_BOOKING_NO_RESULTS" = "No results found";
"AMENITY_BOOKING_AVAILABLE_AMENITIES" = "Available amenities";
"AMENITY_BOOKING_NO_AVAILABLE_AMENITIES" = "There are no available dates for these amenities";
"AMENITY_BOOKING_NEXT_AMENITY_DATE" = "The next available date for an amenity is ";
"AMENITY_BOOKING_NO_PRIVATE_USE_SLOTS" = "No time slots are available for private use";
"AMENITY_BOOKING_PRICE" = "Price";
"AMENITY_BOOKING_GUEST_DETAILS" = "Guest Details";
"AMENITY_BOOKING_FIRST_NAME" = "First name";
"AMENITY_BOOKING_LAST_NAME" = "Last name";
"AMENITY_BOOKING_PHONE_NUMBER" = "Phone number";
"AMENITY_BOOKING_EMAIL" = "Email";
"AMENITY_BOOKING_DAYS_WITH_BOOKING_RESTRICTIONS" = "Days with booking limit restrictions are in red";
"AMENITY_BOOKING_CHECK_IN_OUT_WITH_DATE" = "Check-in: %@ at %@\nCheck-out: %@ at %@\n(%@)";
"AMENITY_BOOKING_NIGHT_LIMIT_ERROR" = "There is a limit of %@ per booking";
"AMENITY_BOOKING_END_DATE_ONLY" = "%@ can only be selected as an end date.";
"AMENITY_BOOKING_INVALID_FIELD" = "Not a valid %@";
"AMENITY_BOOKING_CHECK_IN_OUT_WITHOUT_DATE" = "Check-in: %@\nCheck-out: %@";
"AMENITY_BOOKING_NIGHT_LIMIT" = "Up to %@ per booking";
"AMENITY_BOOKING_END_DATE_ERROR" = "%@ can only be selected as an end date.";
"AMENITY_BOOKING_MIN_ADVANCE_ERROR" = "Booking must be made at least %@ in advance.";
"AMENITY_BOOKING_MAX_ADVANCE_ERROR" = "Bookings can only be made %@ in advance.";
"AMENITY_BOOKING_PERIOD_LIMIT_ERROR" = "%@ is unavailable because you have exceeded your limit of %@.";
"AMENITY_BOOKING_VEHICLE_INFORMATION" = "Vehicle information";
"AMENITY_BOOKING_PLATE" = "License plate";
"AMENITY_BOOKING_MAKE" = "Vehicle make";
"AMENITY_BOOKING_MODEL" = "Vehicle model";
"AMENITY_BOOKING_COLOUR" = "Vehicle colour";
"AMENITY_BOOKING_ENTER_PLATE" = "Enter plate";
"AMENITY_BOOKING_ENTER_MAKE" = "Enter make";
"AMENITY_BOOKING_ENTER_MODEL" = "Enter model";
"AMENITY_BOOKING_ENTER_COLOUR" = "Enter colour";
"AMENITY_BOOKING_ALL_VEHICLE_INFO_REQUIRED" = "All vehicle information is required to complete this booking. If you are unable to continue, please ";
"AMENITY_BOOKING_CONTACT_MANAGEMENT_LINK" = "contact management";
"AMENITY_BOOKING_FOR_ASSISTANCE" = " for assistance.";
"AMENITY_BOOKING_VEHICLE_INFO_MESSAGE_TITLE" = "%@ - Unknown vehicle";

"CALL_BY_SUITE_TOP_LABEL" = "Your building's Entry System has the call-by-suite function enabled.\n\nThis means that the person selected below will the one to receive calls when visitors choose the \"suite\" option from the Entry System's Directory.\n\nYou can change the recipient below at any time.";
"CALL_BY_SUITE_BOTTOM_LABEL" = "Please note that regardless of who is selected as the recipient for call-by-suite, residents will always be able to receive calls to their phone when visitors specifically select their name from the Directory’s alphabetical list (if their name is listed on the Entry System), or enter their buzzer code.";
"CALL_BY_SUITE_RECIPIENT" = "Call-by-suite recipient";
"CALL_BY_SUITE_POP_UP_BODY" = "You are currently the recipient when visitors use the Entry System's call-by-suite option to contact your suite. You can change it from the \"Call Settings\" tab in the \"More menu\".";
"CALL_BY_SUITE_CALL_OPTION" = "Call option";

"APP_FEEDBACK_CLOSE_POP_UP" = "Got it! We'll ask you again sometime later. You can always provide feedback from the main menu under the \"My Account\" tab.";
"APP_FEEDBACK_TITLE" = "App Feedback";
"APP_FEEDBACK_DISCOVER_CARD_LONG" = "We’re always working to make 1VALET the best it can be. Your feedback helps us determine which features to build, and what improvements should be made to our app.\n\nWhether it’s good news or something we can do better, we’d like to know.\n\nSincerely,\nThe Product Team at 1VALET";
"APP_FEEDBACK_PROVIDE" = "Provide Feedback";
"APP_FEEDBACK_HEAR_YOUR_THOUGHTS" = "We want to hear your thoughts on our app";
"APP_FEEDBACK_SEND" = "Send";
"APP_FEEDBACK_THANKS" = "Thanks for your feedback!";
"APP_FEEDBACK_ERROR" = "Oops, something went wrong.";
"APP_FEEDBACK_CATEGORIES" = "Categories";
"APP_FEEDBACK_SELECT_CATEGORY_BODY" = "Please select a category below and let us know your feedback";
"APP_FEEDBACK_SELECT_CATEGORY_TITLE" = "Select category";
"APP_FEEDBACK_ENTER_FEEDBACK" = "Please enter your feedback here";
"APP_FEEDBACK_HOW_IMPROVE" = "How could we improve?";
"APP_FEEDBACK_AGREE" = "I agree to be contacted by 1VALET if more details are needed";
"APP_FEEDBACK_ENJOYING_APP" = "Are you enjoying 1VALET?";
"APP_FEEDBACK_QUESTION_PAYMENTS" = "How was your experience with using Payments?";
"APP_FEEDBACK_QUESTION_DELIVERIES" = "How was your experience with using Deliveries?";
"APP_FEEDBACK_QUESTION_CALLS" = "What has your experience with videos calls been like?";
"APP_FEEDBACK_QUESTION_DIGITAL_ENTRY" = "How was your experience with using Digital Entry?";
"APP_FEEDBACK_QUESTION_GUEST_ACCESS" = "How was your experience with using Guest Access?";
"APP_FEEDBACK_QUESTION_AMENITIES" = "How was your experience with using Amenities?";
"APP_FEEDBACK_QUESTION_MARKETPLACE" = "How was your experience with using the Marketplace?";
"APP_FEEDBACK_QUESTION_MAINTENANCE" = "How was your experience with using Maintenance Requests?";
"APP_FEEDBACK_QUESTION_INBOX" = "How was your experience with using the Inbox?";

"LEASE_OFFERS" = "Lease offers";
"LEASE_OFFERS_DISCOUNTS_AVAILABLE" = "Lease offers - Early bird discount available";
"LEASE_MONTH_TO_MONTH" = "Month-to-month";
"LEASE_AMOUNT_PER_MONTH_TOTAL" = "%@ total/month";
"LEASE_AMOUNT_PER_MONTH" = "%@/month";
"LEASE_ACCEPT_OFFERS" = "Accept Offer";
"LEASE_ACCEPTED_OFFERS" = "Accepted Offer";
"LEASE_HAVE_QUESTIONS" = "Have questions about these offers?";
"LEASE_HAVE_QUESTIONS_YOUR_OFFER" = "Have questions about your offer?";
"LEASE_CHAT_AGENT" = "Chat with leasing agent";
"LEASE_SEND_NO_RENEWAL_NOTICE" = "Send notice of non-renewal";
"LEASE_NO_RENEWAL" = "Notice of non-renewal";
"LEASE_NO_RENEWAL_BODY" = "I am writing to inform you that I will not be renewing my lease. Thank you.";
"LEASE_SUCCESS_ACCEPT_BODY" = "Management will be notified about your interest in renewal and will contact you for the next steps.";
"LEASE_EARLY_BIRD_DESCRIPTION" = "Sign by <bold>%1$@</bold> and save %2$@/month!";
"LEASE_EARLY_BIRD_ONE_TIME_DESCRIPTION" = "Sign by <bold>%1$@</bold> and enjoy a one-time discount of %2$@!";
"LEASE_DURATION_MONTHS" = "%d-Month Lease";
"LEASE_DURATION_MONTHS_CHOICE" = "%d - %d Month Lease";
"LEASE_MONTHS" = "%d months";
"LEASE_LENGTH" = "Length of lease";
"LEASE_ACCEPT_OFFER_DIALOG" = "Are you sure you want to accept this offer?";
"LEASE_UNIT" = "Unit: ";
"LEASE_EARLY_BIRD_OFFER" = "EARLY BIRD OFFER";
"LEASE_NON_RENEWALL_SUCCESS_DESCRIPTION" = "Management will be notified about your notice of non-renewal. The lease offers banner will remain until confirmation by building management.";
"LEASE_VIEW_OFFERS" = "VIEW OFFERS";

"SECURITY_FAILED_DIALOG" = "CAUTION: Your device security is below the recommended standards. Proceeding with this setup may compromise your data and information. Please make sure you understand the risks before continuing.";

"STORE" = "Store";
"STORE_YOUR_ORDERS" = "Your orders";
"STORE_MY_ORDERS" = "My Orders";
"STORE_VIEW_MY_ORDERS" = "View my orders";
"STORE_NO_PENDING_ORDERS_YET" = "No active orders at the moment. New orders that are active will appear here.";
"STORE_ITEMS_FOR_SALE" = "Items for sale";
"STORE_NO_ITEMS_TITLE" = "There are no items for sale yet";
"STORE_NO_ITEMS_DESCRIPTION" = "When products are added, they will appear here.";
"STORE_NO_SEARCH_RESULTS_TITLE" = "No results found";
"STORE_NO_SEARCH_RESULTS_DESCRIPTION" = "Sorry, we couldn't find any results. Check for any misspelled words and try different terms to describe what you want to find.";
"STORE_OUT_OF_STOCK" = "Out of stock";
"STORE_IN_STOCK" = "In stock";
"STORE_PENDING" = "Pending";
"STORE_IN_PROGRESS" = "In Progress";
"STORE_READY_FOR_PICKUP" = "Ready for Pickup";
"STORE_READY_FOR_DELIVERY" = "Ready for Delivery";
"STORE_PICKED_UP" = "Picked Up";
"STORE_DELIVERED" = "Delivered";
"STORE_CANCELLED" = "Cancelled";
"STORE_CONTACT_MANAGER" = "Contact Manager";
"STORE_POLICY" = "Policy";
"STORE_PLACEHOLDER" = "placeholder";
"STORE_I_AGREE" = "I agree";
"STORE_RETURN_TO_ORDER" = "Return to order";
"STORE_NEXT_POLICY" = "Next policy";
"STORE_PREVIOUS_POLICY" = "Previous policy";
"STORE_CHECKOUT" = "Checkout";
"STORE_FIELD_REQUIRED" = "This information is required";
"STORE_PAYMENT_OPTIONS" = "Payment Options";
"STORE_REVIEW" = "Review";
"STORE_TOTAL_CHARGES" = "Total charges";
"STORE_TOTAL_DUE" = "Total due";
"STORE_PAYMENT_INFO" = "Payment info";
"STORE_PAYMENT_INFO_BANNER" = "You will be charged immediately. Please contact building management for help with your order.";
"STORE_ORDER_COMMENTS" = "Order Comments";
"STORE_MAX_CHARACTERS" = "Max 200 Characters";
"STORE_I_AGREE_TO" = "I agree to ";
"STORE_TERMS_AND_CONDITIONS" = "1VALET’s Terms & Conditions";
"STORE_PLACE_ORDER" = "Place order";
"STORE_MAX_QUANTITY_REACHED" = "You’ve reached the maximum quantity available for this item.";
"STORE_ORDER_CONFIRMED_TITLE" = "Order Confirmed!";
"STORE_NEW_ORDERS" = "New orders";
"STORE_COMPLETED_ORDERS" = "Completed";
"STORE_YOUR_ORDER" = "Your Order";
"STORE_ORDER_REFUNDED" = "Your order was cancelled by property management. A refund was issued and will be processed within a few business days.";
"STORE_ITEM_NAME" = "Item name";
"STORE_YOUR_ORDER_COMMENTS" = "Your order comments";
"STORE_ORDER_AGAIN" = "Order again";
"STORE_MESSAGE_MANAGEMENT" = "Message management";
"STORE_VIEW_STORE_POLICY" = "View store policy";
"STORE_TOTAL_PAID" = "Total paid";
"STORE_TOTAL_REFUNDED" = "Total refunded";
"STORE_VIEW_ORDER_RECEIPT" = "View order receipt";
"STORE_CANCELLATION_POLICY" = "Cancellation policy";
"STORE_CONTACT_MANAGEMENT_TO_CANCEL" = "Please contact management if you need to cancel your order";
"STORE_GENERIC_ERROR" = "Something went wrong. Please try again.";
"STORE_ITEM_NO_LONGER_AVAILABLE" = "Sorry, this item is no longer available";
"STORE_PAYMENT" = "Store Payment";
"STORE_POLICY_TITLE" = "Policy %d/%d";
"STORE_OUT_OF_STOCK_AMENITY_TITLE" = "Out of stock - %@";
"STORE_ORDER_NUMBER" = "Order #%@";
"STORE_IN_STOCK_INVENTORY" = "In stock: %d";
"STORE_CONTINUE_WITH_PRICE" = "Continue (%@)";
"STORE_STORE_ORDER_NUMBER" = "Store order #%@";
"STORE_QUANTITY" = "Quantity: %d";
"STORE_REQUEST_SENT_PAYMENT_PAY_IN_PERSON" = "Thank you, for your order. Please make arrangements with your property management to pay for your order. Once your order has been paid, you will be notified of any order updates.";
"STORE_REQUEST_SENT_PAYMENT_WITH_EMAIL" = "Thank you, your payment of <bold>%@ on %@<bold> is complete. A receipt will be sent to %@ shortly. You’ll be notified of any order updates.";
"STORE_REQUEST_SENT_PAYMENT_NO_EMAIL" = "Thank you, your payment of <bold>%@ on %@<bold> is complete. You’ll be notified of any order updates.\n\nWe currently <bold>do not have an email on file<bold>. If you would like receipts, please add an email address.";
"STORE_PURCHASE_FROM_BUILDING" = "Purchase items from your building";
"STORE_DISCOVER_LONG_DESCRIPTION" = "Welcome to your <bold>building’s Store</bold>, a space where <bold>you can purchase items available</bold> for sale in your building, all through the 1VALET app.\n\nAccess the Store by selecting it from the My Building tab on the main center navigation button screen.";
"STORE_NO_BANK_ACCOUNTS" = "Bank accounts cannot be used for store payments. Please select a debit or credit card instead.";
"STORE_FEEDBACK_QUESTION" = "How was your experience with using the Store?";
"STORE_NOTIFICATION_BODY_NEW" = "Order #%@ was created by management on your behalf";
"STORE_NOTIFICATION_BODY_OTHER" = "Order #%@ is now %@.";
"STORE_NO_ORDERS_YET" = "No orders yet";
"STORE_ORDERS_WILL_APPEAR_HERE" = "When you place an order, they all will appear here.";
"STORE_ORDER_ACTIVITY" = "Order activity";
"STORE_ORDER_MESSAGES" = "View order messages";
"STORE_QUANTITY_TITLE" = "Quantity";
"STORE_ITEM_PRICE" = "Item price";
