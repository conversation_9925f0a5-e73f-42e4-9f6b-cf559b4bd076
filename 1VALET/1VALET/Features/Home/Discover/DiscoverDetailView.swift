//
//  DiscoverDetailView.swift
//  1VALET
//
//  Created by <PERSON> on 2024-11-28.
//  Copyright © 2024 1Valet. All rights reserved.
//

import SwiftUI

struct DiscoverDetailView: View {
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    private typealias Limits = HomeWidgetsConstants.Limits
    
    @Binding var type: HomeDiscoverType?
    @Binding var destination: DiscoverDestination?
    
    var body: some View {
        GeometryReader { proxy in
            BaseScrollView(.vertical) {
                VStack(alignment: .leading, spacing: 0) {
                    if let data = type?.getDetailData() {
                        ZStack(alignment: .topTrailing) {
                            Image(data.imageName)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: proxy.size.width, height: Dimensions.TOP_PHOTO_HEIGHT)
                                .clipped()
                            
                            Button {
                                self.type = nil
                            } label: {
                                ZStack {
                                    Circle()
                                        .fill(Color.cloudyGrey)
                                        .frame(width: Dimensions.GENERIC_SQUARE_ICON, height: Dimensions.GENERIC_SQUARE_ICON)
                                    
                                    Image("close")
                                        .resizable()
                                        .renderingMode(.template)
                                        .foregroundColor(Color.slateGrey)
                                        .frame(width: Dimensions.ICON_SMALL, height: Dimensions.ICON_SMALL)
                                }
                            }
                            .padding(.trailing, Sizes.largePadding)
                            .padding(.top, Sizes.extraLargePadding)
                        }
                        
                        Text(data.title)
                            .font(Font.h2(.bold))
                            .foregroundColor(.coolGrey)
                            .padding(.horizontal, Sizes.largePadding)
                            .padding(.top, Sizes.extraLargePadding)
                        
                        UIKLabel {
                            $0.textColor = TextStyle.h3.color
                            $0.attributedText = data.description.styled(
                                with: TextStyle.bold(
                                    normalFont: TextStyle.body.font,
                                    boldFont: TextStyle.bodyBold.font
                                )
                            )
                        }
                        .padding(.horizontal, Sizes.largePadding)
                        .padding(.top, Sizes.extraLargePadding)
                        
                        Spacer(minLength: Sizes.mediumPadding)
                            .layoutPriority(1)
                        
                        if let primaryTextButton = data.primaryButtonText {
                            Button(primaryTextButton) {
                                if let tab = data.primaryButtonTab {
                                    self.type = nil
                                    AppState.sharedInstance.set(tab: tab)
                                    
                                    return
                                }
                                
                                if let nextView = data.primaryButtonDestination {
                                    self.type = nil
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                        destination = nextView
                                    }
                                }
                            }
                            .buttonStyle(PrimaryButtonStyle())
                            .padding(.horizontal, Sizes.largePadding)
                            .padding(.bottom, Sizes.extraLargePadding)
                            
                        }
                    }
                }
                .frame(minHeight: proxy.size.height)
            }
            
        }
        .background(Color.appBackground)
        .ignoresSafeArea(edges: .all)
        
    }
}

enum DiscoverDestination {
    case docBox
    case facialEntry
    case guestAccess
    case entrySystemCalls
    case maintenanceRequests
    case payments
    case appFeedback
    case store
}
