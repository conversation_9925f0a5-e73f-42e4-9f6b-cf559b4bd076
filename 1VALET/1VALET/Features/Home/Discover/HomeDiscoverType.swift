//
//  HomeDiscoverType.swift
//  1VALET
//
//  Created by <PERSON> on 2021-08-19.
//  Copyright © 2021 1Valet. All rights reserved.
//

import Foundation
import NetworkKit

enum HomeDiscoverType: Equatable, Identifiable {
    case docbox
    case facialEntry
    case guestAccessCode
    case guestAccessApp
    case doorSensor
    case entrySystemCalls
    case conversations
    case marketplace
    case maintenanceRequests
    case payments(PaymentCapability)
    case marketplaceShops
    case marketplaceShopsRecommend
    case appFeedback
    case store
//    case theme
    
    var id: String {
        switch self {
        case .docbox:
            return "docbox"
        case .facialEntry:
            return "facialEntry"
        case .guestAccessCode:
            return "guestAccessCode"
        case .guestAccessApp:
            return "guestAccessApp"
        case .doorSensor:
            return "doorSensor"
        case .entrySystemCalls:
            return "entrySystemCalls"
        case .conversations:
            return "conversations"
        case .marketplace:
            return "marketplace"
        case .maintenanceRequests:
            return "maintenanceRequests"
        case .payments:
            return "payments"
        case .marketplaceShops:
            return "marketplaceShops"
        case .marketplaceShopsRecommend:
            return "marketplaceShopsRecommend"
        case .appFeedback:
            return "appFeedback"
        case .store:
            return "store"
//        case .theme:
//            return "theme"
        }
    }
    
    func getPreviewData() -> HomeDiscoverModel {
        switch self {
        case .docbox:
            return HomeDiscoverModel(imageName: "image_docbox", title: "MORE_DOCBOX".localized(), description: "DISCOVER_DOCBOX_PREVIEW".localized())
        case .facialEntry:
            return HomeDiscoverModel(imageName: "image_facial_entry", title: "MORE_FACIAL_ENTRY".localized(), description: "DISCOVER_FACIAL_PREVIEW".localized())
        case .guestAccessCode, .guestAccessApp:
            return HomeDiscoverModel(imageName: "image_guest_access", title: "GUEST_ACCESS".localized(), description: "DISCOVER_GUEST_ACCESS_PREVIEW".localized())
        case .doorSensor:
            return HomeDiscoverModel(imageName: "image_door_sensor", title: "DISCOVER_DOOR_SENSOR".localized(), description: "DISCOVER_DOOR_SENSOR_PREVIEW".localized())
        case .entrySystemCalls:
            return HomeDiscoverModel(imageName: "image_entry_system", title: "DISCOVER_ENTRY_SYSTEM_CALLS".localized(), description: "DISCOVER_ENTRY_SYSTEM_PREVIEW".localized())
        case .conversations:
            return HomeDiscoverModel(imageName: "image_conversations", title: "DISCOVER_CONVERSATIONS".localized(), description: "DISCOVER_CONVERSATIONS_PREVIEW".localized())
        case .marketplace:
            return HomeDiscoverModel(imageName: "image_marketplace", title: "DISCOVER_MARKETPLACE".localized(), description: "DISCOVER_MARKETPLACE_PREVIEW".localized())
        case .maintenanceRequests:
            return HomeDiscoverModel(imageName: "image_maintenance_request", title: "DISCOVER_MAINTENANCE_REQUESTS".localized(), description: "DISCOVER_MAINTENANCE_REQUESTS_PREVIEW".localized())
        case .payments(let paymentCapability):
            if paymentCapability.contains(.amenity) && !paymentCapability.contains(.suiteBalance) {
                return HomeDiscoverModel(imageName: "image_payments", title: "DISCOVER_PAYMENTS".localized(), description: "DISCOVER_PAYMENTS_AMENITY_PREVIEW".localized())
            } else if !paymentCapability.contains(.amenity) && paymentCapability.contains(.suiteBalance) {
                return HomeDiscoverModel(imageName: "image_payments", title: "DISCOVER_PAYMENTS".localized(), description: "DISCOVER_PAYMENTS_RENT_PREVIEW".localized())
            } else {
                return HomeDiscoverModel(imageName: "image_payments", title: "DISCOVER_PAYMENTS".localized(), description: "DISCOVER_PAYMENTS_AMENITY_RENT_PREVIEW".localized())
            }
        case .marketplaceShops, .marketplaceShopsRecommend:
            return HomeDiscoverModel(imageName: "image_marketplace_services", title: "DISCOVER_LOCAL_SERVICES".localized(), description: "DISCOVER_LOCAL_SERVICES_PREVIEW".localized())
        case .appFeedback:
            return HomeDiscoverModel(imageName: "image_app_feedback", title: FeedbackConstants.Strings.appFeedback, description: FeedbackConstants.Strings.discoverCardShortDescription)
        case .store:
            return HomeDiscoverModel(imageName: "image_store_discover", title: "STORE".localized(), description: "STORE_PURCHASE_FROM_BUILDING".localized())
            // TODO: jngo theme
//        case .theme:
//            return HomeDiscoverModel(imageName: "image_rogers", title: "Xfinity Internet", description: "Activate now!\n")
        }
    }
    
    func getDetailData() -> HomeDiscoverDetailModel? {
        switch self {
        case .docbox:
            return HomeDiscoverDetailModel(
                imageName: "image_docbox",
                title: "MORE_DOCBOX".localized(),
                description: "DISCOVER_DOCBOX_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .docBox
            )
        case .facialEntry:
            return HomeDiscoverDetailModel(
                imageName: "image_facial_entry",
                title: "MORE_FACIAL_ENTRY".localized(),
                description: "DISCOVER_FACIAL_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .facialEntry
            )
        case .guestAccessCode:
            return HomeDiscoverDetailModel(
                imageName: "image_guest_access",
                title: "GUEST_ACCESS".localized(),
                description: "DISCOVER_GUEST_ACCESS_CODE_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .guestAccess
            )
            
        case .guestAccessApp:
            return HomeDiscoverDetailModel(
                imageName: "image_guest_access",
                title: "GUEST_ACCESS".localized(),
                description: "DISCOVER_GUEST_ACCESS_APP_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .guestAccess
            )
            
        case .doorSensor:
            return HomeDiscoverDetailModel(
                imageName: "image_door_sensor",
                title: "DISCOVER_DOOR_SENSOR".localized(),
                description: "DISCOVER_DOOR_SENSOR_DESCRIPTION".localized()
            )
        case .entrySystemCalls:
            return HomeDiscoverDetailModel(
                imageName: "image_entry_system",
                title: "DISCOVER_ENTRY_SYSTEM_CALLS".localized(),
                description: "DISCOVER_ENTRY_SYSTEM_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .entrySystemCalls
            )
        case .conversations:
            return HomeDiscoverDetailModel(
                imageName: "image_conversations",
                title: "DISCOVER_CONVERSATIONS".localized(),
                description: "DISCOVER_CONVERSATIONS_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonTab: .inbox
            )
        case .marketplace:
            return HomeDiscoverDetailModel(
                imageName: "image_marketplace",
                title: "DISCOVER_MARKETPLACE".localized(),
                description: "DISCOVER_MARKETPLACE_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonTab: .marketplace
            )
            
        case .maintenanceRequests:
            return HomeDiscoverDetailModel(
                imageName: "image_maintenance_request",
                title: "DISCOVER_MAINTENANCE_REQUESTS".localized(),
                description: "DISCOVER_MAINTENANCE_REQUESTS_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .maintenanceRequests
            )
            
        case .payments(let paymentCapability):
            if paymentCapability.contains(.amenity) && !paymentCapability.contains(.suiteBalance) {
                return HomeDiscoverDetailModel(
                    imageName: "image_payments",
                    title: "DISCOVER_PAYMENTS".localized(),
                    description: "DISCOVER_PAYMENTS_AMENITY_DESCRIPTION".localized(),
                    primaryButtonText: "DISCOVER_GO_NOW".localized(),
                    primaryButtonDestination: .payments
                )
            } else if !paymentCapability.contains(.amenity) && paymentCapability.contains(.suiteBalance) {
                return HomeDiscoverDetailModel(
                    imageName: "image_payments",
                    title: "DISCOVER_PAYMENTS".localized(),
                    description: "DISCOVER_PAYMENTS_RENT_DESCRIPTION".localized(),
                    primaryButtonText: "DISCOVER_GO_NOW".localized(),
                    primaryButtonDestination: .payments
                )
            } else {
                return HomeDiscoverDetailModel(
                    imageName: "image_payments",
                    title: "DISCOVER_PAYMENTS".localized(),
                    description: "DISCOVER_PAYMENTS_AMENITY_RENT_DESCRIPTION".localized(),
                    primaryButtonText: "DISCOVER_GO_NOW".localized(),
                    primaryButtonDestination: .payments
                )
            }
        case .marketplaceShops:
            return HomeDiscoverDetailModel(
                imageName: "image_marketplace_recommend",
                title: "DISCOVER_LOCAL_SERVICES".localized(),
                description: "DISCOVER_LOCAL_SERVICES_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonTab: .marketplace
            )
            
        case .appFeedback:
            return HomeDiscoverDetailModel(
                imageName: "image_app_feedback",
                title: FeedbackConstants.Strings.appFeedback,
                description: FeedbackConstants.Strings.discoverCardLongDescription,
                primaryButtonText: FeedbackConstants.Strings.provideFeedback,
                primaryButtonDestination: .appFeedback
            )
            
        case .marketplaceShopsRecommend:
            return nil
            
        case .store:
            return HomeDiscoverDetailModel(
                imageName: "image_store_discover",
                title: "STORE".localized(),
                description: "STORE_DISCOVER_LONG_DESCRIPTION".localized(),
                primaryButtonText: "DISCOVER_GO_NOW".localized(),
                primaryButtonDestination: .store
            )
            
//        case .theme:
//            return nil
        }
    }
}

extension HomeDiscoverType {
    func toAnalyticsType() -> AnalyticsScreen {
        switch self {
        case .docbox:
            return .discoveryDocbox
        case .facialEntry:
            return .discoveryFacialEntry
        case .guestAccessCode:
            return .discoveryGuestAccessCode
        case .guestAccessApp:
            return .discoveryGuestAccessApp
        case .doorSensor:
            return .discoveryDoorSensor
        case .entrySystemCalls:
            return .discoveryEntrySystemCalls
        case .conversations:
            return .discoveryConversations
        case .marketplace:
            return .discoveryMarketplace
        case .maintenanceRequests:
            return .discoveryMaintenanceRequests
        case .payments:
            return .discoveryPayments
        case .marketplaceShops, .marketplaceShopsRecommend:
            return .discoveryMarketplaceShops
        case .appFeedback:
            return .discoveryAppFeedback
        case .store:
            return .discoveryStore
//        case .theme:
//            return .discoveryTheme
        }
    }
}
