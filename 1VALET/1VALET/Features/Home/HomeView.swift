//
//  HomeView.swift
//  1VALET
//
//  Created by <PERSON> on 22/03/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import UIKit
import NetworkKit
import InjectionKit
import UtilitiesKit
import Combine

struct HomeView: View {
    
    private typealias Strings = HomeWidgetsConstants.Strings
    
    @ObservedViewModel var viewModel: HomeViewModelProtocol = DependencyResolver.resolve()
    @Injected private var persistenceManager: PersistenceManager
    var configuration: TabsView.DelegateConfiguration
    var isFromLogin: Bool = false
    
    @State var isLoading: Bool = false
    @State var popUp: PopUpAlert?
    @State var destination: HomeViewDestination?
    @State var residentType: ResidentType = .none {
        didSet {
            didChange(residentType: residentType)
            sections.removeAll(where: { $0 == .welcomeHome })
            sections = residentType.homescreenSections
        }
    }
    @State var sections: [HomeViewSection] = []
    @State var currentSuiteId: String? = UserUtil<PERSON>.sharedInstance.loggedInUser?.defaultSuite?.id
    var activityFeedSettings = ActivityFeedWidgetSettings()
    @State var showCallSettings: Bool = false
    
    @State private var fetchInsuite: Task<Void, Error>?
    @State private var showSuiteChangedToast = false
    @State private var showOneTimeAccessCodeToast = false
    @State private var canShowLoadingBanner = false
    @State private var hasNeverLoaded = true
    @State private var lastTimeReloaded: TimeInterval?
    @State private var notificationsAuthorized: Bool = false
    @State private var showDiscoverRecommendToast = false
    
    @State private var showDiscoverSheet: Bool = false
    @State private var discoverScreenType: HomeDiscoverType?
    @State private var discoverDestination: DiscoverDestination?
    
    init(configuration: TabsView.DelegateConfiguration, isFromLogin: Bool = false) {
        self.configuration = configuration
        self.isFromLogin = isFromLogin
        
        if let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId {
            UserDefaults.standard.set(buildingId, forKey: UserDefaultsConstants.LATEST_BUILDING_KEY)
        }

        DispatchQueue.main.async {
            NavigationUtil.currentNavigationController()?.isNavigationBarHidden = true
            NavigationUtil.currentNavigationController()?.setNavigationBarHidden(true, animated: false)
        }
    }
    
    var body: some View {
        baseView()
            .environment(\.homeViewDestination, $destination)
            .popUpAlert(popUp: $popUp)
            .loading(show: $isLoading)
            .navigationDestination(for: $destination) { destination in
                switch destination {
                case .suiteInspections:
                    SuiteInspectionMessageView()
                case .doorPins:
                    if InSuiteDoor.getInSuiteDoors().count > 1 {
                        DoorsListView()
                    } else if let door = InSuiteDoor.getInSuiteDoors().first {
                        DoorPinsView(door: door)
                    }
                case .leaseRenewal:
                    LeaseRenewalsView()
                }
            }
        .onReceive(
            Publishers.CombineLatest3(
                NotificationCenter.default.publisher(for: Notification.Name(NotificationName.staffRequestedSuiteAccess.rawValue)),
                NotificationCenter.default.publisher(for: Notification.Name(NotificationName.staffEndedSuiteAccess.rawValue)),
                NotificationCenter.default.publisher(for: Notification.Name(NotificationName.staffCancelledSuiteAccess.rawValue))
        )) { _ in
            fetchStaffRequests()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.staffMovedIn.rawValue))) { _ in
            reloadView()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.accessPermissionModified.rawValue))) { _ in
            reloadView()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.newLeaseOffer.rawValue))) { _ in
            reloadBanners()
        }
        .onChange(of: BrandingUtils.sharedInstance.theme) { _ in
            NotificationUtils.postNotification(name: NotificationName.reloadHomeViewUI.rawValue, userInfo: nil)
        }
        .onReceive(NotificationCenter.default.publisher(for: UIScene.willEnterForegroundNotification)) { _ in
            fetchInsuite = Task.delayed(byTimeInterval: 1) {
                await self.fetchInsuiteDetails()
            }
        }
        .onReceive(UserUtils.sharedInstance.$loggedInUser) { user in
            if let currentSuiteId = currentSuiteId,
               let newSuiteId = user?.defaultSuite?.id,
               currentSuiteId != newSuiteId {
                self.currentSuiteId = newSuiteId
                fetchInsuiteDetails()
            }
        }
        .onChange(of: viewModel.isLoading) { value in
            if value && canShowLoadingBanner {
                AppState.sharedInstance.banners.insert(Banner(type: .loading), at: 0)
            } else {
                AppState.sharedInstance.banners.removeAll { banner in
                    banner.type == .loading
                }
            }
        }
        .onAppear {
            showSecurityDialog()
            if residentType == .none {
                reloadView()
            }

            NotificationUtils.sharedInstance.getAuthorizationStatus { status in
                notificationsAuthorized = status
            }
            UserDefaults.standard.set(nil, forKey: UserDefaultsConstants.FIRST_TIME_LOGGED_OUT_PROX_CARD_USE)
            
            // Loading banner timer, it can only show after 0.5s
            if hasNeverLoaded && !isFromLogin {
                Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
                    canShowLoadingBanner = true
                }
                hasNeverLoaded = false
            } else {
                canShowLoadingBanner = false
            }
            
            NavigationUtil.currentNavigationController()?.setNavigationBarHidden(true, animated: false)
            Task {
                await viewModel.getProfile()
                
                if NotificationUtils.sharedInstance.presentingNotificationState == .notPresenting {
                    reloadView() {
                        fetchInsuiteDetails()
                    }
                    
                } else if NotificationUtils.sharedInstance.presentingNotificationState == .didPresent {
                    if sections.isEmpty {
                        reloadView {
                            DispatchQueue.main.async {
                                Logger.print("shouldFetchData 1")
                                viewModel.shouldFetchData = true
                            }
                        }
                    } else {
                        fetchInsuiteDetails()
                    }
                    
                    NotificationUtils.sharedInstance.presentingNotificationState = .notPresenting
                } else {
                    NotificationUtils.sharedInstance.presentingNotificationState = .didPresent
                }
            }
        }
        .onDisappear {
            fetchInsuite?.cancel()
        }
        .toast(
            message: Strings.switchSuiteToast(suiteNumber: UserUtils.sharedInstance.loggedInUser?.defaultSuite?.number ?? ""),
            isShowing: $showSuiteChangedToast,
            config: ToastModifier.Config(extraTabPadding: true)
        )
        .toast(
            message: Strings.codeCopiedToast(code: AppState.sharedInstance.oneTimeAccessCode ?? ""),
            isShowing: $showOneTimeAccessCodeToast,
            config: ToastModifier.Config(extraTabPadding: true)
        )
        .analytics(screen: .home)
        .fullScreenCover(isPresented: $showCallSettings) {
            NavigationView {
                EntrySystemCallsModalRepresentable(isSuiteSwitching: true)
                    .ignoresSafeArea(.all)
            }
        }
        .fullScreenCover(item: $discoverScreenType) { item in
            /*if item == .theme {
                if let url = BrandingUtils.sharedInstance.theme?.themeUrl {
                    WebView(url: url, showLoading: $isLoading)
                }
            } else*/ if item == .marketplaceShopsRecommend {
                DiscoverMarketplaceRecommendView(
                    type: $discoverScreenType,
                    showDiscoverRecommendToast: $showDiscoverRecommendToast
                )
            } else {
                DiscoverDetailView(
                    type: $discoverScreenType,
                    destination: $discoverDestination
                )
            }
        }
        .navigationDestination(for: $discoverDestination) { destination in
            switch destination {
            case .docBox:
                DocBoxView()
            case .facialEntry:
                FacialRecognitionHomeView()
            case .guestAccess:
                GuestAccessHomeView()
            case .entrySystemCalls:
                CallSettingsView()
            case .maintenanceRequests:
                MaintenanceRequestView()
            case .payments:
                PaymentsRouterView()
            case .appFeedback:
                FeedbackView()
            case .store:
                BuildingStoreView()
            }
        }
    }
    
    func baseView() -> some View {
        if #available(iOS 15.0, *) {
            return BaseTopLevelView(shouldShowConnectionBannerOnly: false) {
                GeometryReader { proxy in
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(alignment: .leading, spacing: Spacing.LARGE) {           
                            ForEach(sections) { section in
                                widget(for: section)
                            }
                            
                            Spacer(minLength: 0)
                        }
                        .padding(.bottom, TabsView.bottomPadding)
                        .environment(\.residentType, residentType)
                        .environmentObject(activityFeedSettings)
                        .padding(.top, Dimensions.BANNER_HEIGHT)
                        .frame(minHeight: proxy.size.height)
                    }
                    .refreshable(action: {
                        pullToRefresh()
                    })
                    .padding(.top, 1)
                }
            }
        } else {
            return BaseTopLevelView(shouldShowConnectionBannerOnly: false) {
                GeometryReader { proxy in
                    ScrollView(.vertical, showsIndicators: false) {
                        VStack(alignment: .leading, spacing: Spacing.LARGE) {
                            ForEach(sections) { section in
                                widget(for: section)
                            }
                            
                            Spacer(minLength: 0)
                        }
                        .padding(.bottom, TabsView.bottomPadding)
                        .environment(\.residentType, residentType)
                        .environmentObject(activityFeedSettings)
                        .padding(.top, Dimensions.BANNER_HEIGHT)
                        .frame(minHeight: proxy.size.height)
                    }
                    .onRefresh {
                        pullToRefresh()
                    }
                    .padding(.top, 1)
                }
            }
        }
    }
    
    private func pullToRefresh() {
        canShowLoadingBanner = false
        if Date().timeIntervalSince1970 - (lastTimeReloaded ?? 0) > 3 {
            NotificationUtils.postNotification(name: NotificationName.reloadHomeView.rawValue, userInfo: nil)
            fetchInsuiteDetails()
            lastTimeReloaded = Date().timeIntervalSince1970
        }
    }
}

private extension HomeView {
    @ViewBuilder
    func widget(for section: HomeViewSection) -> some View {
        switch section {
        case .welcomeHome:
            WelcomeHomeWidgetView(
                popUp: $popUp,
                showOneTimeAccessCodeToast: $showOneTimeAccessCodeToast,
                notificationsAuthorized: $notificationsAuthorized
            )
        case .guestAccessInfo:
            GuestAccessInfoView(
                shouldFetchData: $viewModel.shouldFetchData,
                showRemoteUnlockView: configuration.openDoorsDelegate?.showRemoteUnlockView(remoteDoor:),
                showProximityUnlockView: configuration.openDoorsDelegate?.showProximityUnlockView(proximityKey:),
                showToast: $showSuiteChangedToast
            )
        case .activityFeed:
            ActivityFeedWidgetView(isParentLoading: $isLoading, popUp: $popUp)
        case .digitalAccess:
            DigitalAccessWidgetView(
                viewModel: viewModel.digitalAccessWidgetViewModel,
                shouldFetchData: $viewModel.shouldFetchData,
                showRemoteUnlockView: configuration.openDoorsDelegate?.showRemoteUnlockView(remoteDoor:),
                showProximityUnlockView: configuration.openDoorsDelegate?.showProximityUnlockView(proximityKey:)
            )
        case .amenities:
            AmenityWidgetView(viewModel: viewModel.amenityWidgetViewModel)
        case .marketplace:
            MarketplaceWidgetView(
                viewModel: viewModel.marketplaceWidgetViewModel,
                popUp: $popUp
            )
        case .discover(let dataList):
            DiscoverWidgetView(
                discoverItems: dataList,
                selectedType: $discoverScreenType,
                isLoading: $isLoading
            )
        }
    }
}

private extension HomeView {
    func showLoggedInMessages() {
        if UserDefaults.standard.bool(forKey: UserDefaultsConstants.JUST_LOGGED_IN_KEY) {
            showFirstTimeLoginMessages()
            viewModel.inSuiteDoorsSubscriber {
                seedPersistanceIfNeeded(for: .resetSuiteDoorLock)
            }
            
            UserDefaults.standard.set(false, forKey: UserDefaultsConstants.JUST_LOGGED_IN_KEY)
        }
    }
    
    func showFirstTimeLoginMessages() {
        if !UserDefaults.standard.bool(forKey: UserDefaultsConstants.NOT_FIRST_TIME_LOGGED_IN_KEY) {
            seedPersistanceIfNeeded(for: .checkAccountDetails)
        }
        
        UserDefaults.standard.set(true, forKey: UserDefaultsConstants.NOT_FIRST_TIME_LOGGED_IN_KEY)
    }
    
    func seedPersistanceIfNeeded(for type: NotificationEvent) {
        Task {
            if type == .resetSuiteDoorLock {
                guard CapabilityUtils.hasInSuiteDoor() else { return }
                
                let insuiteDoors = InSuiteDoor.getInSuiteDoors()
                
                let pinsCount = insuiteDoors.reduce(0) { partialResult, insuiteDoor in
                    return partialResult + (insuiteDoor.pins.count)
                }
                
                if pinsCount > 0 {
                    if insuiteDoors.count > 1 {
                        await persistenceManager.seedPersistanceIfNeeded(type: type)
                    } else if let door = InSuiteDoor.getInSuiteDoors().first {
                        await persistenceManager.seedPersistanceIfNeeded(type: type, userInfo: [NotificationConstants.DOOR_ID_KEY: .string(door.id)])
                    }
                }
            } else {
                await persistenceManager.seedPersistanceIfNeeded(type: type)
            }
        }
    }
    
    func showSecurityDialog() {
        if !SecurityUtils.isAppSafe && !SecurityUtils.sharedInstance.hasBeenChecked {
            let okAction = PopUpAlertButton(title: "OK".localized(), action: {
                SecurityUtils.sharedInstance.hasBeenChecked = true
            })
            
            popUp = PopUpAlert(
                title: "ONE_VALET_APP".localized(),
                description: "SECURITY_FAILED_DIALOG".localized(),
                buttons: [okAction]
            )
        }
    }
    
    func showEntrySystemOptions() {
        guard let occupantId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.occupantId else { return }
        if !EntrySystemCallsUtils.getEntrySystemOptionSeen(occupantId: occupantId) {
            showCallSettings = true
        }
    }
    
    func reloadView(completion: (() -> Void)? = nil) {
        if let suites = UserUtils.sharedInstance.loggedInUser?.profile?.suites, suites.isEmpty {
            residentType = .guest
            completion?()
        } else {
            
            let suiteAssociationType = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType
            
            // MARK: Owner non-resident
            if suiteAssociationType == .ownerNonResident {
                residentType = .ownerNonResident
                completion?()
            } else if suiteAssociationType == .guest {
                residentType = .guest
                completion?()
            } else {
                if viewModel.isCurrentResident {
                    // MARK: Current Resident
                    residentType = .currentResident
                    
                    Task {
                        Logger.print("HomeView - Guest Devices 1")
                        await viewModel.fetchGuestDevices()
                        Logger.print("HomeView - Guest Devices 2")
                        residentType = .currentResident
                        completion?()
                    }
                    
                } else {
                    // MARK: Future Resident
                    residentType = .futureResident
                    completion?()
                }
            }
        }
    }
    
    func reloadBanners() {
        if viewModel.isCurrentResident {
            if !UserUtils.sharedInstance.isGuest {
                fetchStaffRequests()
            }
            
            if CapabilityUtils.hasSuiteInspections(),
               let isPrimaryContact = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.isPrimaryContact,
               isPrimaryContact {
                fetchSuiteInspections()
            } else {
                DispatchQueue.main.async {
                    AppState.sharedInstance.banners.removeAll(where: { $0.type == .suiteInspections })
                }
            }
                        
            if UserUtils.sharedInstance.loggedInUser?.defaultSuite?.isPrimaryContact == true, InsuiteUtils.sharedInstance.insuiteDetails?.hasLeaseRenewals == true {
                fetchLeaseRenewalMetadata()
            } else {
                AppState.sharedInstance.banners.removeAll(where: { $0.type == .leaseRenewal(hasEarlyBirdDiscounts: true) })
                AppState.sharedInstance.banners.removeAll(where: { $0.type == .leaseRenewal(hasEarlyBirdDiscounts: false) })
            }
        }
    }
    
    func didChange(residentType: ResidentType) {
        switch residentType {
        case .ownerNonResident:
#if !DEBUG
            // Ask here for app update, because the other ones are residents and wait for insuiteDetails call which requires occupantId
            Task {
                await fetchAppUpdate()
            }
#endif
            UserDefaults.standard.set(true, forKey: UserDefaultsConstants.NOT_FIRST_TIME_LOGGED_IN_KEY)
        case .futureResident:
            UserDefaults.standard.set(true, forKey: UserDefaultsConstants.NOT_FIRST_TIME_LOGGED_IN_KEY)
            
            NotificationUtils.sharedInstance.requestAuthorization(options: [.alert, .badge, .sound], completionHandler: { status in
                notificationsAuthorized = status
            })
        case .currentResident:
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                showEntrySystemOptions()
            }
            showLoggedInMessages()
            NotificationUtils.sharedInstance.requestAuthorization(options: [.alert, .badge, .sound], completionHandler: { status in
                notificationsAuthorized = status
            })
        case .guest:
            UserDefaults.standard.set(true, forKey: UserDefaultsConstants.NOT_FIRST_TIME_LOGGED_IN_KEY)
            NotificationUtils.sharedInstance.requestAuthorization(options: [.alert, .badge, .sound], completionHandler: { status in
                notificationsAuthorized = status
            })
        default:
            break
        }
    }
    
    func fetchLeaseRenewalMetadata() {
        Task {
            if let metadata = await viewModel.fetchLeaseRenewalsMetadata(), metadata.hasLeaseOffers {
                let banner = Banner(type: .leaseRenewal(hasEarlyBirdDiscounts: metadata.hasEarlyBirdDiscount), possibleDestination: .leaseRenewal)
                AppState.sharedInstance.banners.removeAll(where: { $0.type == .leaseRenewal(hasEarlyBirdDiscounts: true) })
                AppState.sharedInstance.banners.removeAll(where: { $0.type == .leaseRenewal(hasEarlyBirdDiscounts: false) })
                
                AppState.sharedInstance.banners.insert(banner, at: 0)
            } else {
                AppState.sharedInstance.banners.removeAll(where: { $0.type == .leaseRenewal(hasEarlyBirdDiscounts: true) })
                AppState.sharedInstance.banners.removeAll(where: { $0.type == .leaseRenewal(hasEarlyBirdDiscounts: false) })
            }
        }
    }
    
    func fetchSuiteInspections() {
        Task {
            let hasInspections = await viewModel.fetchSuiteInspection()
            DispatchQueue.main.async {
                if hasInspections {
                    if !AppState.sharedInstance.banners.contains(where: { $0.type == .suiteInspections }) {
                        
                        let banner = Banner(type: .suiteInspections, possibleDestination: .suiteInspections)
                        
                        AppState.sharedInstance.banners.insert(banner, at: 0)
                    }
                } else {
                    AppState.sharedInstance.banners.removeAll(where: { $0.type == .suiteInspections })
                }
            }
        }
    }
    
    func fetchInsuiteDetails() {
        Task {
            let success = await viewModel.fetchInsuiteDetails()
            
            if success {
                // Get app version from backend
#if !DEBUG
                await fetchAppUpdate()
#endif
                reloadBanners()
            }
            reloadView()
        }
    }
    
    func fetchAppUpdate() async {
        if let updateType = await viewModel.fetchAppUpdate() {
            switch updateType {
            case .critical:
                let negativeButton = PopUpAlertButton(title: "APP_UPDATE_URGENT_DIALOG_NEGATIVE_BUTTON".localized(), style: .cancel)
                let positiveButton = PopUpAlertButton(title: "APP_UPDATE_POSITIVE_BUTTON".localized(), action: {
                    OpenAppUtils.openAppStore()
                })
                
                popUp = PopUpAlert(
                    title: "APP_UPDATE_URGENT_DIALOG_DESCRIPTION".localized(),
                    buttonArrangementLayout: .vertical,
                    buttons: [negativeButton, positiveButton])
                
            case .old:
                let negativeButton = PopUpAlertButton(title: "APP_UPDATE_OLD_DIALOG_NEGATIVE_BUTTON".localized(), style: .cancel)
                let positiveButton = PopUpAlertButton(title: "APP_UPDATE_POSITIVE_BUTTON".localized(), action: {
                    OpenAppUtils.openAppStore()
                })
                
                popUp = PopUpAlert(
                    title: "APP_UPDATE_OLD_DIALOG_DESCRIPTION".localized(),
                    buttonArrangementLayout: .vertical,
                    buttons: [negativeButton, positiveButton])
                
            case .nonCritical:
                let negativeButton = PopUpAlertButton(title: "APP_UPDATE_NON_CRITICAL_DIALOG_NEGATIVE_BUTTON".localized(), style: .cancel)
                let positiveButton = PopUpAlertButton(title: "APP_UPDATE_POSITIVE_BUTTON".localized(), action: {
                    OpenAppUtils.openAppStore()
                })
                
                popUp = PopUpAlert(
                    title: "APP_UPDATE_NON_CRITICAL_DIALOG_DESCRIPTION".localized(),
                    buttonArrangementLayout: .vertical,
                    buttons: [negativeButton, positiveButton])
                
            }
        }
    }
    
    func fetchStaffRequests() {
        Task {
            let staffRequests = await viewModel.fetchStaffRequest()
            DispatchQueue.main.async {
                AppState.sharedInstance.banners.removeAll(where: {
                    [
                        .pendingStaffRequest,
                        .approvedStaffRequest(isLinkedAccount: true),
                        .approvedStaffRequest(isLinkedAccount: false)
                    ].contains($0.type)
                })
                
                if !staffRequests.isEmpty {
                    let requested = staffRequests.contains { request in
                        request.status == .requested
                    }
                    
                    if requested {
                        let firstRequested = staffRequests
                            .filter { $0.status == .requested }
                            .sorted { firstRequest, secondRequest in
                                Date.fromISO8601String(firstRequest.requestTime ?? "") ?? Date() <
                                    Date.fromISO8601String(secondRequest.requestTime ?? "") ?? Date()
                            }.first
                        
                        if let request = firstRequested {
                            let banner = Banner(type: .pendingStaffRequest) { _ in
                                if let statusViewController = ViewControllerRouter.technicianAccessStatusViewController(request: request) {
                                    NavigationUtil.currentNavigationController()?.pushViewController(statusViewController, animated: true)
                                }
                            }
                            
                            AppState.sharedInstance.banners.insert(banner, at: 0)
                        }
                    } else {
                        let firstApproved = staffRequests
                            .filter { $0.status == .approved }
                            .sorted { firstRequest, secondRequest in
                                Date.fromISO8601String(firstRequest.approvalTime ?? "") ?? Date() <
                                    Date.fromISO8601String(secondRequest.approvalTime ?? "") ?? Date()
                            }.first
                        
                        if let request = firstApproved {
                            let banner = Banner(type: .approvedStaffRequest(isLinkedAccount: UserUtils.sharedInstance.loggedInUser?.defaultSuite?.isLinkedAccount == true)) { _ in
                                if let statusViewController = ViewControllerRouter.technicianAccessStatusViewController(request: request) {
                                    NavigationUtil.currentNavigationController()?.pushViewController(statusViewController, animated: true)
                                }
                            }
                            
                            AppState.sharedInstance.banners.insert(banner, at: 0)
                        }
                    }
                }
            }
        }
    }
}

enum HomeViewDestination {
    case suiteInspections
    case doorPins
    case leaseRenewal
}

extension Task where Failure == Error {
    static func delayed(
        byTimeInterval delayInterval: TimeInterval,
        priority: TaskPriority? = nil,
        operation: @escaping @Sendable () async throws -> Success
    ) -> Task {
        Task(priority: priority) {
            let delay = UInt64(delayInterval * 1_000_000_000)
            try await Task<Never, Never>.sleep(nanoseconds: delay)
            return try await operation()
        }
    }
}
