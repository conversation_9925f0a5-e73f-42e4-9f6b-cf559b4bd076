//
//  HomeViewSection.swift
//  1VALET
//
//  Created by <PERSON> on 2023-09-20.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import UtilitiesKit
import NetworkKit

enum HomeViewSection: Identifiable, Equatable {
    
    case welcomeHome
    case activityFeed
    case digitalAccess
    case amenities
    case marketplace
    case discover([HomeDiscoverType])
    case guestAccessInfo
    
    var id: String {
        switch self {
        case .welcomeHome:
            return "welcomeHome"
        case .activityFeed:
            return "activityFeed"
        case .digitalAccess:
            return "digitalAccess"
        case .amenities:
            return "amenities"
        case .marketplace:
            return "marketplace"
        case .discover(let array):
            return "discover-\(array.map({ $0.id }).joined(separator: "-"))"
        case .guestAccessInfo:
            return "guestAccessInfo"
        }
    }
    
    var canShow: Bool {
        switch self {
        case .welcomeHome:
            return true
        case .activityFeed:
            return true
        case .digitalAccess:
            return true
        case .amenities:
            return CapabilityUtils.hasAmenityBooking()
        case .marketplace:
            return CapabilityUtils.hasMarketplaceCommunityAccess()
        case .discover:
            return true
        case .guestAccessInfo:
            return true
        }
    }
}

enum ResidentType {
    case ownerNonResident
    case futureResident
    case currentResident
    case guest
    case none
    
    var homescreenSections: [HomeViewSection] {
        switch self {
        case .ownerNonResident:
            return [.welcomeHome, .activityFeed, .marketplace, .discover(discoverSections)].filter { $0.canShow }
        case .futureResident,
                .currentResident:
            return [.welcomeHome, .activityFeed, .digitalAccess, .amenities, .marketplace, .discover(discoverSections)].filter { $0.canShow }
        case .guest:
            return [.welcomeHome, .guestAccessInfo].filter { $0.canShow }
        case .none:
            return []
        }
    }
    
    private var discoverSections: [HomeDiscoverType] {
        switch self {
        case .ownerNonResident:
            return [.docbox, .store, .marketplace, .marketplaceShops, .appFeedback].filter { $0.canShow }
        case .futureResident:
            var sections: [HomeDiscoverType] = []
            if let paymentCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.paymentCapabilities {
                sections.append(.payments(paymentCapabilities))
            }
            
            sections.append(contentsOf: [.maintenanceRequests, .conversations, .store, .marketplaceShops, .marketplace, .entrySystemCalls, .docbox, .appFeedback])
            
            sections = sections.filter { $0.canShow }
            return sections
        case .currentResident:
            var sections: [HomeDiscoverType] = []

            if let paymentCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.paymentCapabilities {
                sections.append(.payments(paymentCapabilities))
            }
            
//            if BrandingUtils.sharedInstance.theme?.themeUrl != nil {
//                sections.append(.theme)
//            }
            
            sections.append(contentsOf: [.maintenanceRequests, .conversations, .store, .marketplaceShops, .marketplace, .entrySystemCalls, .doorSensor, .docbox, .guestAccessCode, .guestAccessApp, .facialEntry, .appFeedback])
            
            sections = sections.filter { $0.canShow }
            return sections
        case .guest, .none:
            return []
        }
    }
}

extension HomeDiscoverType {
    var canShow: Bool {
        switch self {
        case .docbox:
            return true
        case .facialEntry:
            if let isFacialRecognitionSupported = InsuiteUtils.sharedInstance.insuiteDetails?.isFacialRecognitionSupported, isFacialRecognitionSupported {
                return true
            }
            return false
        case .guestAccessCode:
            if CapabilityUtils.hasGuestAccess() {
                let accessCodeFlow = GuestAccessUtils.guestAccessDevices?.guestAccessDevices.allSatisfy({ device in
                    device.allowedFlows == .accessCode
                })
                return accessCodeFlow == true
            }
            return false
        case .guestAccessApp:
            if CapabilityUtils.hasGuestAccess() {
                let accessCodeFlow = GuestAccessUtils.guestAccessDevices?.guestAccessDevices.allSatisfy({ device in
                    device.allowedFlows == .accessCode
                })
                return accessCodeFlow == false
            }
            return false
        case .doorSensor:
            return CapabilityUtils.hasDoorSensor()
        case .entrySystemCalls:
            return InsuiteUtils.sharedInstance.insuiteDetails?.hasEntryConsole ?? false
        case .conversations:
            return true
        case .marketplace:
            return CapabilityUtils.hasMarketplaceCommunityAccess()
        case .maintenanceRequests:
            return CapabilityUtils.hasTicketingService()
        case .payments:
            return CapabilityUtils.hasPaymentsDiscoveryCardAccess()
        case .marketplaceShops, .marketplaceShopsRecommend:
            return CapabilityUtils.hasMarketplaceShopsAndServicesAccess()
        case .appFeedback:
            return true
        case .store:
            return CapabilityUtils.hasBuildingStoreAccess()
//        case .theme:
//            return true
        }
    }
}
