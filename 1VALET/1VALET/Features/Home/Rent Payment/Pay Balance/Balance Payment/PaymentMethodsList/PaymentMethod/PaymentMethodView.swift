//
//  PaymentMethodView.swift
//  1VALET
//
//  Created by <PERSON> on 09/08/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import SwiftUI
import Stripe

struct PaymentMethodView: View {
    // MARK: - Typealias
    
    private typealias Colors = RentPaymentConstants.Colors
    private typealias Strings = RentPaymentConstants.Strings
    private typealias Sizes = RentPaymentConstants.Sizes
    private typealias Fonts = RentPaymentConstants.Fonts
    private typealias ImageNames = RentPaymentConstants.ImageNames
    
    // MARK: - Public Properties
    
    var paymentMethod: PaymentMethod
    var textFont: Font = Fonts.body
    var miniCardIcon: Bool = false
    var accessoryImageName: String? = ImageNames.dotMenu
    var showOptions: Bool = true
    
    var warningAction: ((String) -> Void)?
    var optionsAction: (() -> Void)?
    
    var body: some View {
        if paymentMethod.type == .payInPerson {
            HStack(spacing: Sizes.mediumPadding) {
                if miniCardIcon {
                    Image(ImageNames.payment)
                        .renderingMode(.template)
                        .resizable()
                        .scaledToFit()
                        .frame(width: Sizes.cardIconHeight, height: Sizes.cardIconHeight)
                        .foregroundColor(Colors.icon)
                }
                
                Text(Strings.payInPerson)
                    .font(textFont)
                    .foregroundColor(Colors.baseText)
                
                Spacer()
                
                if let accessoryImageName = accessoryImageName, miniCardIcon, showOptions {
                    Image(accessoryImageName)
                        .renderingMode(.template)
                        .resizable()
                        .scaledToFit()
                        .frame(width: Sizes.cardIconHeight, height: Sizes.cardIconHeight)
                        .foregroundColor(Colors.icon)
                        .onTapGesture {
                            optionsAction?()
                        }
                        .cornerRadius(Sizes.baseCornerRadius)
                }
            }
        } else {
            HStack(spacing: Sizes.mediumPadding) {
                Group {
                    if let cardBrand = paymentMethod.cardBrand {
                        Image(uiImage: STPImageLibrary.cardBrandImage(for: cardBrand))
                            .resizable()
                            .scaledToFit()
                    } else {
                        // TODO: jngo - two tone
                        Image(ImageNames.bank)
                            .resizable()
                            .scaledToFit()
                    }
                }
                .frame(width: miniCardIcon ? Sizes.miniCardIconWidth : Sizes.cardIconWidth, height: miniCardIcon ? Sizes.miniCardIconHeight : Sizes.cardIconHeight)
                
                Text(Strings.cardDescription(for: paymentMethod.lastFourDigits))
                    .font(textFont)
                    .foregroundColor(Colors.baseText)
                
                if let warning = paymentMethod.warning, let warningAction = warningAction {
                    Image(ImageNames.urgent)
                        .resizable()
                        .renderingMode(.template)
                        .scaledToFit()
                        .foregroundColor(Colors.alert)
                        .frame(width: Sizes.cardNavigationIcon, height: Sizes.cardNavigationIcon)
                        .padding(Sizes.smallPadding)
                        .onTapGesture {
                            warningAction(warning.title)
                        }
                }
                
                if let optionsAction = optionsAction, let accessoryImageName = accessoryImageName {
                    Spacer()
                    
                    if showOptions {
                        Image(accessoryImageName)
                            .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
                            .frame(width: Sizes.cardIconHeight, height: Sizes.cardIconHeight)
                            .foregroundColor(Colors.icon)
                            .onTapGesture {
                                optionsAction()
                            }
                            .cornerRadius(Sizes.baseCornerRadius)
                    }
                }
            }
        }
    }
}
