//
//  PaymentMethodsListViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 29/11/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import Combine
import SwiftUI
import Stripe
import UtilitiesKit
import InjectionKit

enum PaymentFlowType {
    case rent
    case amenity
    case store
    case settings
    
    func toPaymentCategory(for type: PaymentMethodType) -> PaymentCategory? {
        return switch self {
        case .rent:
            .suiteBalance
        case .amenity:
            .amenityBooking
        case .store:
            .store
        case .settings:
            PaymentUtils.shared.getGeneralCapability(for: type)
        }
    }
}

class PaymentMethodsListViewModel: ObservableObject, PaymentMethodsListViewModelProtocol {
    // MARK: - Typealias
    
    typealias Strings = RentPaymentConstants.Strings
    
    // MARK: - Injected Properties
    
    @Injected var rentPaymentService: RentPaymentRepositoryProtocol
    
    // MARK: - Public Porperties
    
    @Published var popUp: PopUpAlert?
    @Published var chosenPaymentMethod: PaymentMethod?
    @Published var isLoading: Bool = true
    @Published var showActionSheet: Bool = false
    @Published var paymentMethods: [PaymentMethod] = [
        PaymentMethod(id: UUID().uuidString, lastFourDigits: "0000", name: "Placeholder")
    ]
    
    private(set) var flowType: PaymentFlowType
    private(set) var preselectedPaymentMethodId: String?
    private(set) var allowedPaymentMethods: [PaymentMethod.PaymentMethodType]
    
    init(flowType: PaymentFlowType, allowedPaymentMethods: [PaymentMethod.PaymentMethodType] = [.bank, .card], preselectedPaymentMethodId: String? = nil) {
        self.flowType = flowType
        self.allowedPaymentMethods = allowedPaymentMethods
        self.preselectedPaymentMethodId = preselectedPaymentMethodId
        
        NotificationCenter.default
            .publisher(for: Notification.Name(NotificationName.paymentMethodSetupStatusChangedReceived.rawValue))
            .sink { [weak self] _ in
                self?.fetchPaymentMethods()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Porperties
    
    var cancellables: [AnyCancellable] = [AnyCancellable]()
    var paymentMethodOptions: PaymentMethod?
    
    // MARK: - Public Methods
    
    func send(action: PaymentMethodActions) {
        switch action {
        case .showOptions(let method):
            paymentMethodOptions = method
            showActionSheet = true
        case .showWarningInfo(let message):
            popUp = PopUpAlert(title: message, type: .ok)
        case .optionChosen(let option):
            self.action(for: option)
        }
    }
    
    func fetchPaymentMethods() {
        let isDummyData = prepareForFetchingPaymentMethods()
        
        self.paymentMethodsPublisher().sink { [weak self] completion in
            self?.isLoading = false
            switch completion {
            case let .failure(error):
                ExceptionLoggingUtils.report(error)
                self?.handlePaymentMethods(error: error, isDummyData: isDummyData)
            case .finished:
                Logger.print("\(#function) finished")
            }
        } receiveValue: { [weak self] response in
            self?.handlePaymentMethods(response: response, isDummyData: isDummyData)
            
            Logger.print("\(#function) success")
        }.store(in: &cancellables)
    }
    
    func prepareForFetchingPaymentMethods() -> Bool {
        isLoading = true
        if paymentMethods.isEmpty {
            paymentMethods = paymentMethodsPlaceholder
        }
        return paymentMethods.first?.name == "Placeholder" || paymentMethods == [PaymentMethod.inPersonPayment]
    }
    
    func handlePaymentMethods(error: Error, isDummyData: Bool) {
        Logger.print("error: \(error)")
        if isDummyData {
            self.paymentMethods.removeAll()
            if allowedPaymentMethods.contains(.payInPerson) {
                self.paymentMethods = [PaymentMethod.inPersonPayment]
            }
        }
        self.popUp = .genericError(error: error)
    }
    
    func handlePaymentMethods(response: RentPaymentGetPaymentMethodsResponse, isDummyData: Bool) {
        guard let methods = response.paymentMethods else { return }
        let mappedPaymentMethods = methods.compactMap { method -> PaymentMethod? in
            guard let id = method.paymentMethodId,
                  let lastFourDigits = method.lastFourDigits,
                  let paymentMethodName = method.paymentMethodName else { return nil }
            
            return PaymentMethod(id: id,
                                 lastFourDigits: lastFourDigits,
                                 name: paymentMethodName,
                                 isExpired: method.isExpired,
                                 isOriginInternational: method.isOriginInternational,
                                 isVerificationRequired: method.isVerificationRequired,
                                 isUsedInAutoPayment: method.isUsedInAutoPayment,
                                 isMandateInactive: method.isMandateInactive,
                                 isCardBrandInvalid: method.isCardBrandInvalid,
                                 fundingType: method.fundingType)
        }
        
        if isDummyData {
            self.paymentMethods.removeAll()
        }
        
        let newPaymentMethods = mappedPaymentMethods.subtract(other: paymentMethods)
        
        self.paymentMethods = mappedPaymentMethods
        
        if allowedPaymentMethods.contains(.payInPerson) == true {
            self.paymentMethods.append(PaymentMethod.inPersonPayment)
        }
        
        if chosenPaymentMethod == nil {
            if let preselectedPaymentMethodId = preselectedPaymentMethodId {
                self.chosenPaymentMethod = self.paymentMethods.first(where: { $0.id == preselectedPaymentMethodId }) ?? self.paymentMethods.first
            } else {
                self.chosenPaymentMethod = self.paymentMethods.first(where: { self.allowedPaymentMethods.contains($0.type) })
            }
        } else if newPaymentMethods.count == 1 {
            self.chosenPaymentMethod = self.paymentMethods.first(where: { self.allowedPaymentMethods.contains($0.type) })
        }
    }
    
    let paymentMethodsPlaceholder: [PaymentMethod] = [
        PaymentMethod(id: UUID().uuidString, lastFourDigits: "0000", name: "Placeholder")
    ]
}

extension PaymentMethodsListViewModel {
    func action(for option: PaymentMethodOptions, reloadAutoPayCompletion: ((Bool) -> Void)? = nil) {
        switch option {
        case .delete:
            if let paymentMethodOptions = paymentMethodOptions {
                if case .unverified = paymentMethodOptions.warning {
                    self.popUp = PopUpAlert(title: Strings.unverifiedPaymentMethodCannotBeDeleted, type: .ok)
                    
                    return
                }
                
                if paymentMethodOptions.isUsedInAutoPayment == true {
                    self.showDeletePopUp(message: Strings.deleteAutoPayPaymentMethod(paymentMethodOptions.description), reloadAutoPayCompletion: reloadAutoPayCompletion)
                    
                    return
                }
                
                self.showDeletePopUp(message: Strings.deleteConfirmation(paymentMethodDescription: paymentMethodOptions.description.capitalized))
            }
        }
    }
    
    // PopUps
    func showDeletePopUp(message: String, reloadAutoPayCompletion: ((Bool) -> Void)? = nil) {
        let deleteButton = PopUpAlertButton(title: Strings.delete, action: {
            if let paymentMethodOptions = self.paymentMethodOptions {
                withAnimation {
                    self.delete(paymentMethod: paymentMethodOptions, reloadAutoPayCompletion: reloadAutoPayCompletion)
                    self.paymentMethodOptions = nil
                }
            }
        })
        
        let cancelButton = PopUpAlertButton(title: Strings.cancelLabel, style: .cancel)
        
        self.popUp = PopUpAlert(title: message, buttonArrangementLayout: .horizontal, buttons: [deleteButton, cancelButton])
    }
}

private extension PaymentMethodsListViewModel {
    func delete(paymentMethod: PaymentMethod, reloadAutoPayCompletion: ((Bool) -> Void)? = nil) {
        guard let index = self.paymentMethods.firstIndex(where: { $0.id == paymentMethod.id }) else { return }
        
        self.paymentMethods.remove(at: index)
        if paymentMethod.id == chosenPaymentMethod?.id {
            chosenPaymentMethod = paymentMethods.first
        }
        self.deletePublisher(paymentMethodId: paymentMethod.id)
            .receive(on: DispatchQueue.global(qos: .background))
            .sink { [weak self] completion in
                switch completion {
                case let .failure(error):
                    Logger.print("error: \(error)")
                    self?.paymentMethods.insert(paymentMethod, at: index)
                    reloadAutoPayCompletion?(false)
                case .finished:
                    Logger.print("\(#function) finished")
                    reloadAutoPayCompletion?(true)
                }
            } receiveValue: { _ in Logger.print("\(#function) success") }
            .store(in: &cancellables)
    }
}

extension PaymentMethodsListViewModel {
    func paymentMethodsPublisher() -> AnyPublisher<RentPaymentGetPaymentMethodsResponse, Error> {
        guard let occupantId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.occupantId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return rentPaymentService.getPaymentMethods(occupantId: occupantId)
    }
}

private extension PaymentMethodsListViewModel {
    func deletePublisher(paymentMethodId: String) -> AnyPublisher<Bool, Error> {
        guard let occupantId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.occupantId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return rentPaymentService.delete(occupantId: occupantId, paymentMethodId: paymentMethodId)
    }
}

struct PaymentMethod: Identifiable, Equatable, Hashable {
    // MARK: - Typealias
    
    private typealias Strings = RentPaymentConstants.Strings
    
    var id: String
    var lastFourDigits: String
    var name: String
    var isExpired: Bool?
    var isOriginInternational: Bool?
    var isVerificationRequired: Bool?
    var isUsedInAutoPayment: Bool?
    var isMandateInactive: Bool?
    var isCardBrandInvalid: Bool?
    var fundingType: RentPaymentPaymentMethodFundingType?
    
    var description: String {
        "\(name.uppercased()) \(Strings.cardDescription(for: lastFourDigits))"
    }
    
    var cardBrand: STPCardBrand? {
        switch name.uppercased() {
        case "AMEX":
            return .amex
        case "DINERS":
            return .dinersClub
        case "DISCOVER":
            return .discover
        case "JCB":
            return .JCB
        case "UNIONPAY":
            return .unionPay
        case "VISA":
            return .visa
        case "UNKNOWN":
            return .unknown
        case "MASTERCARD":
            return .mastercard
        default:
            return nil
        }
    }
    
    var type: Self.PaymentMethodType {
        if Self.inPersonPayment == self {
            return .payInPerson
        } else {
            return cardBrand == nil ? .bank : .card
        }
    }
    
    var warning: Warning? {
        switch type {
        case .card:
            if let isExpired = isExpired, isExpired {
                return .expiredCreditCard
            }
            
            if let isOriginInternational = isOriginInternational, isOriginInternational {
                return .internationalCard
            }
            
            if let isCardBrandInvalid = isCardBrandInvalid, isCardBrandInvalid {
                return .cardBrandInvalid(brandName: name.uppercased())
            }
        case .bank:
            if let isVerificationRequired = isVerificationRequired, isVerificationRequired {
                return .unverified
            }
            
            if let isMandateInactive = isMandateInactive,  isMandateInactive {
                return .mandateInactive
            }
        default:
            return nil
        }
        return nil
    }
    
    enum Warning {
        case unverified
        case expiredCreditCard
        case internationalCard
        case mandateInactive
        case cardBrandInvalid(brandName: String)
        
        var title: String {
            switch self {
            case .unverified:
                return Strings.unverifiedBankAccountWarning(with: UserUtils.sharedInstance.loggedInUser?.profile?.email)
            case .expiredCreditCard:
                return Strings.expiredCardWarning
            case .internationalCard:
                return Strings.internationalCardsNotAllowedWarning
            case .mandateInactive:
                return Strings.mandateInactive
            case .cardBrandInvalid(let brandName):
                return Strings.invalid(cardBrand: brandName)
            }
        }
    }
    
    enum PaymentMethodType: CaseIterable {
        case card
        case bank
        case payInPerson
    }
    
    static let inPersonPayment = PaymentMethod(id: "inPerson", lastFourDigits: "0000", name: "inPerson")
    
    static func getPayments() async throws -> [PaymentMethod] {
        guard let occupantId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.occupantId else { return [] }
        
        @Injected var repository: RentPaymentRepositoryProtocol

        let response = try await repository.getPaymentMethods(occupantId: occupantId).async().paymentMethods ?? []
        
        return from(paymentResponse: response)
        
    }
    
    static func from(paymentResponse: [RentPaymentPaymentMethodResponse]) -> [PaymentMethod] {
        return paymentResponse.compactMap { response in
            guard let id = response.paymentMethodId else { return nil }
            return PaymentMethod(
                id: id,
                lastFourDigits: response.lastFourDigits ?? "",
                name: response.paymentMethodName ?? "",
                isExpired: response.isExpired ?? true,
                isOriginInternational: response.isOriginInternational ?? false,
                isVerificationRequired: response.isVerificationRequired ?? true,
                isUsedInAutoPayment: response.isUsedInAutoPayment ?? false,
                isMandateInactive: response.isMandateInactive ?? false,
                isCardBrandInvalid: response.isCardBrandInvalid ?? false,
                fundingType: response.fundingType ?? .none
            )
        }
    }
    
}

enum PaymentMethodActions {
    case showOptions(method: PaymentMethod)
    case showWarningInfo(warning: String)
    case optionChosen(PaymentMethodOptions)
}

enum PaymentMethodOptions: Int, ActionSheetOption {
    case delete = 1
    
    var id: Int {
        self.rawValue
    }
    
    var title: String {
        switch self {
        case .delete:
            return RentPaymentConstants.Strings.delete
        }
    }
}

extension Array where Element: Hashable {
    func subtract(other: [Element]) -> [Element] {
        let thisSet = Set(self)
        let otherSet = Set(other)
        return Array(thisSet.subtracting(otherSet))
    }
}
