//
//  CreateBankPaymentMethodViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 16/11/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import Combine
import Stripe
import NetworkKit
import SwiftUI
import UtilitiesKit
import InjectionKit

final class CreateBankPaymentMethodViewModel: ObservableObject, CreateBankPaymentMethodViewModelProtocol {
    // MARK: - Typealias
            
    private typealias Strings = RentPaymentConstants.Strings
    
    // MARK: - Injected Properties
    
    @Injected private var rentPaymentService: RentPaymentRepositoryProtocol
    
    // MARK: - Public Porperties
    
    @Published var popUp: PopUpAlert?
    @Published var showWebView: Bool = false
    
    var name: String {
        if let firstName = UserUtils.sharedInstance.loggedInUser?.profile?.firstName,
           let lastName = UserUtils.sharedInstance.loggedInUser?.profile?.lastName {
            return "\(firstName) \(lastName)"
        }
        
        return ""
    }
    
    var email: String {
        UserUtils.sharedInstance.loggedInUser?.profile?.email ?? ""
    }
    
    private var cancellables: [AnyCancellable] = [AnyCancellable]()
    
    func send(action: CreateBankPaymentMethodActions) { 
        switch action {
        case .showErrorPopUp:
            let button = PopUpAlertButton(title: Strings.ok, style: .standard, action: {
                NavigationUtil.pop()
            })
            popUp = PopUpAlert(title: Strings.apiError, buttons: [button])
        }
    }
    
    func setupIntent(flowType: PaymentFlowType) async -> BankSetupIntentType? {
        guard let intentType = BankSetupIntentType.getIntentType(),
              let paymentCategory = flowType.toPaymentCategory(for: intentType.paymentMethodType) else { return nil }
        
        do {
            let setupIntent = try await self.setupIntentPublisher(type: intentType.paymentMethodType, paymentCategory: paymentCategory).async()
            
            switch intentType {
            case .ach:
                return BankSetupIntentType.ach(
                    clientSecret: setupIntent.setupIntentClientSecret,
                    publishableKey: setupIntent.publishableKey,
                    statementDescriptor: setupIntent.statementDescriptor
                )
            case .pad:
                if let setupIntentClientSecret = setupIntent.setupIntentClientSecret,
                   let publishableKey = setupIntent.publishableKey,
                   let (acssPath, strHTMLContent) = prepareHTML(with: setupIntentClientSecret, publishableKey: publishableKey) {
                    
                    return .pad(
                        acssPath: acssPath,
                        htmlString: strHTMLContent
                    )
                }
            }
        } catch {
            if let badRequest = error as? BadRequestResponse {
                if let code = badRequest.code {
                    if code == .bankPaymentInvalid {
                        let button = PopUpAlertButton(title: Strings.ok, style: .standard, action: NavigationUtil.pop)
                        popUp = PopUpAlert(title: Strings.bankAccountsNotAllowed, buttons: [button])
                    } else {
                        popUp = .genericError(error: error)
                    }
                } else {
                    popUp = .genericError(error: error)
                }
            }
            Logger.print("error: \(error)")
        }
        
        return nil
    }
}

// MARK: - Private Methods
private extension CreateBankPaymentMethodViewModel {
    func prepareHTML(with clientSecret: String, publishableKey: String) -> (String, String)? {
        do {
            var acssPath = Bundle.main.path(forResource: "acss-setup", ofType: "html")!
            let returnPath = Bundle.main.path(forResource: "acss-return", ofType: "html")!
            let errorPath = Bundle.main.path(forResource: "acss-error", ofType: "html")!
            let closePath = Bundle.main.path(forResource: "acss-close", ofType: "html")!
            
            var strHTMLContent = try String(contentsOfFile: acssPath)
            
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.clientSecretPlaceholder, with: clientSecret)
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.publishableKeyPlaceholder, with: publishableKey)
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.namePlaceholder, with: name)
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.userEmailPlaceholder, with: email)
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.returnUrlPlaceholder, with: URL(fileURLWithPath: returnPath).absoluteString)
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.errorUrlPlaceholder, with: URL(fileURLWithPath: errorPath).absoluteString)
            strHTMLContent = strHTMLContent.replacingOccurrences(of: Strings.userClosedUrlPlaceholder, with: URL(fileURLWithPath: closePath).absoluteString)
            
            return (acssPath, strHTMLContent)
        } catch {
            self.popUp = .genericError(error: error)
            return nil
        }
    }
}

// MARK: - Repository calls
private extension CreateBankPaymentMethodViewModel {
    func setupIntentPublisher(type: PaymentMethodType, paymentCategory: PaymentCategory) -> AnyPublisher<RentPaymentSetupIntentResponse, Error> {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return rentPaymentService.setupIntent(suiteId: suiteId, type: type, paymentCategory: paymentCategory)
    }
}
