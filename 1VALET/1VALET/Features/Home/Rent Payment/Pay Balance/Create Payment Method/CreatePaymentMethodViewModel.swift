//
//  CreatePaymentMethodViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 15/08/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import Combine
import StripePaymentSheet
import NetworkKit
import SwiftUI
import UtilitiesKit
import InjectionKit

final class CreatePaymentMethodViewModel: ObservableObject, CreatePaymentMethodViewModelProtocol {
    // MARK: - Typealias
            
    private typealias Strings = RentPaymentConstants.Strings
    
    // MARK: - Injected Properties
    
    @Injected private var rentPaymentService: RentPaymentRepositoryProtocol
    @Injected private var authenticationService: AuthenticationRepository
    
    // MARK: - Public Porperties
    
    @Published var popUp: PopUpAlert?
    @Published var isLoading: Bool = false
    @Published var showBankAccountFlow: Bool = false
    @Published var paymentSheet: PaymentSheet?
    @Published var showPaymentSheet: Bool = false
    @Published var canShowCardButton: Bool?
    @Published var canShowBankButton: Bool?
    
    // MARK: - Private Porperties
    
    private var cancellables: [AnyCancellable] = [AnyCancellable]()
    
    func send(action: CreatePaymentMethodActions) {
        switch action {
        case .createBankAccount:
            updateProfile()
        case .createCard(let flowType):
            setupIntent(flowType: flowType)
        case .updatePaymentConfiguration(let flowType):
            fetchBalanceAndConfiguration(flowType: flowType)
        }
    }
}

// MARK: - Private Methods
private extension CreatePaymentMethodViewModel {
    func setupIntent(flowType: PaymentFlowType) {
        isLoading = true
        self.setupIntentPublisher(flowType: flowType).sink { [weak self] completion in
            self?.isLoading = false
            switch completion {
            case let .failure(error):
                if let badRequest = error as? BadRequestResponse {
                    if let code = badRequest.code {
                        if code == .cardPaymentInvalid {
                            self?.popUp = PopUpAlert(title: Strings.cardsNotAllowed, type: .ok)
                        } else {
                            self?.popUp = .genericError(error: error)
                        }
                    } else {
                        self?.popUp = .genericError(error: error)
                    }
                }
                Logger.print("error: \(error)")
            case .finished:
                Logger.print("\(#function) finished")
            }
        } receiveValue: { [weak self] response in
            guard let clientSecret = response.setupIntentClientSecret,
                  let publishableKey = response.publishableKey else { return }
            
            STPAPIClient.shared.publishableKey = publishableKey
            
            var configuration = PaymentUtils.shared.standardSheetConfiguration
            configuration.merchantDisplayName = "1Valet"
              
            DispatchQueue.main.async {
                self?.paymentSheet = PaymentSheet(setupIntentClientSecret: clientSecret, configuration: configuration)
                self?.showPaymentSheet = true
            }
            
            Logger.print("\(#function) success")
        }.store(in: &cancellables)
    }
    
    func updateProfile() {
        isLoading = true
        self.authenticationService.getProfileCombine()
            .sink { [weak self] completion in
                self?.isLoading = false
                switch completion {
                case let .failure(error):
                    self?.popUp = .genericError(error: error)
                    Logger.print("error: \(error)")
                    ExceptionLoggingUtils.report(error)
                case .finished:
                    self?.showBankAccountFlow = true
                    Logger.print("\(#function) finished")
                }
            } receiveValue: { UserUtils.sharedInstance.updateProfile($0) }.store(in: &cancellables)
    }
    
    func fetchBalanceAndConfiguration(flowType: PaymentFlowType) {
        isLoading = true
        
        Task {
            do {
                try await PaymentUtils.shared.fetchPaymentConfiguration()
            } catch {
                Logger.print("error: \(error)")
            }
            
            await MainActor.run {
                self.isLoading = false
            }
            
            self.canShowBankButton = PaymentUtils.shared.isAvailable(paymentMethodType: .electronicFundsTransfer, for: flowType)
            self.canShowCardButton = PaymentUtils.shared.isAvailable(paymentMethodType: .card, for: flowType)
        }
    }
}

// MARK: - Repository calls
private extension CreatePaymentMethodViewModel {
    func setupIntentPublisher(flowType: PaymentFlowType) -> AnyPublisher<RentPaymentSetupIntentResponse, Error> {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id,
              let paymentCategory = flowType.toPaymentCategory(for: .card) else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return rentPaymentService.setupIntent(suiteId: suiteId, type: .card, paymentCategory: paymentCategory)
    }
}
