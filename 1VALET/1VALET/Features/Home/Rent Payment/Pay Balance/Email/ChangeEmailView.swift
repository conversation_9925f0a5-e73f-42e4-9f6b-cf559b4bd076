//
//  ChangeEmailView.swift
//  1VALET
//
//  Created by <PERSON> on 23/09/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit

struct ChangeEmailView<Content: View>: View {
    // MARK: - Typealias
    
    private typealias Colors = RentPaymentConstants.Colors
    private typealias Strings = RentPaymentConstants.Strings
    private typealias Sizes = RentPaymentConstants.Sizes
    private typealias Fonts = RentPaymentConstants.Fonts
    
    // MARK: - View Model
    
    @ObservedViewModel var viewModel: ChangeEmailViewModelProtocol
    
    // MARK: Initializer
    
    init(viewModel: ChangeEmailViewModelProtocol, nextContent: @escaping () -> Content) {
        self._viewModel = ObservedViewModel(wrappedValue: viewModel)
        self.nextContent = nextContent
    }
    
    // MARK: Public Properties
    
    var nextContent: (() -> Content)?
    
    var body: some View {
        GeometryReader { proxy in
            BaseScrollView(.vertical) {
                VStack(alignment: .leading, spacing: Sizes.largePadding) {
                    Text(viewModel.title)
                        .font(Fonts.body)
                        .foregroundColor(Colors.baseText)
                        .padding(.horizontal, Sizes.largePadding)
                        .padding(.top, Sizes.extraLargePadding)
                    
                    TextField(Strings.emailPlaceholder, text: $viewModel.email)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                        .showDoneOnKeyboard()
                        .textFieldStyle(StandardTextFieldStyle())
                        .validation(viewModel.emailValidation, shouldDisplayErrors: viewModel.shouldShowErrors, showErrorMessage: true)
                        .padding(.horizontal, Sizes.largePadding)
                    
                    Spacer(minLength: 0)
                    
                    NavigationLink(isActive: $viewModel.emailAddedSuccessfully, destination: {
                        NavigationLazyView(destination)
                    }) {
                        EmptyView()
                    }
                    
                    Button(viewModel.primaryButtonTitle) {
                        KeyboardUtils.endEditing()
                        viewModel.send(action: .saveEmail)
                    }
                    .disabled(viewModel.email.isEmpty)
                    .buttonStyle(PrimaryButtonStyle())
                    .padding(.horizontal, Sizes.largePadding)
                    .padding(.bottom, Sizes.extraExtraLargePadding)
                }
                .frame(minHeight: proxy.size.height)
            }
        }
        .standardNavigationStyle(Strings.addEmail)
        .loading(show: $viewModel.isLoading)
        .popUpAlert(popUp: $viewModel.popUp)
        .analytics(screen: .accountSettingsChangeEmail)
    }
}

extension ChangeEmailView {
    @ViewBuilder
    var destination: some View {
        if let nextContent = nextContent {
            nextContent()
        } else {
            EmailAddedSuccessView(email: viewModel.email)
        }
    }
}

extension ChangeEmailView where Content == EmptyView {
    init(viewModel: ChangeEmailViewModelProtocol) {
        _viewModel = ObservedViewModel(wrappedValue: viewModel)
    }
}
