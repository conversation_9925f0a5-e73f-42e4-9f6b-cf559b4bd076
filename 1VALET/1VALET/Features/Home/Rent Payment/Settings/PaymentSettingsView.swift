//
//  PaymentSettingsView.swift
//  1VALET
//
//  Created by <PERSON> on 30/08/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit

struct PaymentSettingsView: View {
    // MARK: - Typealias
            
    private typealias Colors = RentPaymentConstants.Colors
    private typealias Strings = RentPaymentConstants.Strings
    private typealias Sizes = RentPaymentConstants.Sizes
    private typealias Fonts = RentPaymentConstants.Fonts
    private typealias ImageNames = RentPaymentConstants.ImageNames
    
    // MARK: - Injected Properties
    
    @ObservedViewModel var viewModel: PaymentSettingsViewModelProtocol
    
    // MARK: - Initializer
    
    init(fromFirstTimeWarning: Bool = false) {
        self.fromFirstTimeWarning = fromFirstTimeWarning
        self._viewModel = ObservedViewModel(wrappedValue: PaymentSettingsViewModel(flowType: .settings))
    }
    
    // MARK: - Private Properties
    
    private var fromFirstTimeWarning: Bool
    @State private var hasRentPayment = CapabilityUtils.hasRentPayment()
    @State private var fullScreenLoading = false
    
    var body: some View {
        GeometryReader { proxy in
            BaseScrollView(.vertical) {
                VStack(alignment: .leading, spacing: Sizes.largePadding) {
                    if let paymentsBlockedType = viewModel.paymentsBlockedType {
                        PaymentsBlockedAlertView(
                            isLoading: $fullScreenLoading,
                            popUp: $viewModel.popUp,
                            paymentsBlockedType: paymentsBlockedType
                        )
                    }
                    
                    PaymentMethodsListView(viewModel: viewModel, showSelector: false)
                        .padding(.top, viewModel.paymentsBlockedType?.blocksPayments == true ? 0 : Sizes.extraLargePadding)
                    
                    if hasRentPayment {
                        balanceAutoPay
                            .padding(.top, Sizes.mediumPadding)
                    }
                    Spacer()
                }
                .frame(minHeight: proxy.size.height)
            }
        }
        .navigationBarTitle(Strings.paymentSettings) // Will not display, only used as a identifier for notifications
        .standardNavigationStyle(Strings.paymentSettings) {
            NavigationUtil.pop(fromFirstTimeWarning ? 2 : 1)
        }
        .customActionSheet(showingActionSheet: $viewModel.showActionSheet,
                           actionSheetOptions: PaymentMethodOptions.allCases,
                           chosenAction: { viewModel.paymentMethod(option: $0) })
        .popUpAlert(popUp: $viewModel.popUp)
        .loading(show: $fullScreenLoading)
        .onAppear {
            viewModel.fetchPaymentInformation()
        }
        .navigationDestination(for: $viewModel.destination) { destination in
            switch destination {
            case .autoPayOn:
                BalanceTurnOnAutoPayView(previousScreen: .paymentSettings, viewModel: viewModel.autoPayViewModel)
            case .serviceFees(let amount):
                NavigationLazyView(ServiceFeesView(viewModel: ServiceFeesViewModel(amount: amount)))
            case .switchToAutoPay:
                NavigationLazyView(SwitchToAutoPayView(viewModel: viewModel.switchToAutoPayViewModel))
            }
        }
        .analytics(screen: .paymentSettings)
    }
}

private extension PaymentSettingsView {
    var isExternalPayment: Bool {
        if case .external = viewModel.autoPayStatus {
            return true
        }
        
        return false
    }
    
    var balanceAutoPay: some View {
        VStack(alignment: .center, spacing: 0) {
            DividerView()
            
            HStack(alignment: .center) {
                Text(Strings.oneValetAutoPay)
                    .foregroundColor(Color.coolGrey)
                    .font(Fonts.listHeader)
                
                Spacer()
                
                AutoPayStatusView(status: viewModel.autoPayStatus)
            }
            .padding(.horizontal, Sizes.largePadding)
            .padding(.top, Sizes.mediumLargePadding)

            Group {
                if let autoPayConfiguration = viewModel.autoPayStatus.autoPayConfiguration {
                    HStack(alignment: .center, spacing: Sizes.smallPadding) {
                        Text(Strings.nextPayment(on: autoPayConfiguration.nextPaymentDate, isPaused: viewModel.autoPayStatus.isPaused))
                            .font(Fonts.nextPaymentOnDay)
                            .foregroundColor(Colors.baseText)
                        
                        Spacer()
                        
                        PaymentMethodView(paymentMethod: autoPayConfiguration.paymentMethod, textFont: Fonts.listHeader)
                    }
                    .padding(.horizontal, Sizes.largePadding)
                    .padding(.top, Sizes.largePadding)

                    StyledText(text: Strings.serviceFeeLabel(
                        fullAmount: autoPayConfiguration.autoPayAmount,
                        showFees: autoPayConfiguration.needsToPayFees,
                        for: autoPayConfiguration.serviceFeeAmount))
                        .fixedSize(horizontal: false, vertical: true)
                        .lineLimit(3)
                        .frame(maxWidth: .infinity)
                        .font(Fonts.body)
                        .foregroundColor(Colors.baseText)
                        .onTapGesture {
                            viewModel.destination = .serviceFees(autoPayConfiguration.autoPayAmount ?? 200000)
                        }
                        .padding(.horizontal, Sizes.largePadding)
                        .padding(.vertical, Sizes.largePadding)
                        .background(Colors.noteBackground)
                        .padding(.top, Sizes.largePadding)
                    
                    VStack(alignment: .center, spacing: Sizes.mediumLargePadding) {
                        NavigationLink(Strings.editAutoPay) {
                            NavigationLazyView(BalanceTurnOnAutoPayView(previousScreen: .paymentSettings, viewModel: viewModel.autoPayViewModel))
                        }
                        .buttonStyle(PrimaryButtonStyle(backgroundColor: Colors.icon))
                        .padding(.horizontal, Sizes.largePadding)
                        
                        NavigationLink(destination: {
                            TurnOffAutoPayView()
                        }) {
                            Text(Strings.turnOffAutoPay)
                                .underline()
                        }
                        .buttonStyle(
                            LinkButtonStyle(font: Font.textLinkMedium(.bold))
                        )
                        .padding(.horizontal, Sizes.largePadding)
                    }
                    .padding(.top, Sizes.largePadding + Sizes.smallPadding)
                } else {
                    Group {
                        if isExternalPayment {
                            Button(Strings.switchToAutoPay) {
                                if viewModel.canTurnOnAutoPay() {
                                    viewModel.destination = .switchToAutoPay
                                }
                            }
                        } else {
                            Button(Strings.turnOnAutoPay) {
                                if viewModel.canTurnOnAutoPay() {
                                    viewModel.destination = .autoPayOn
                                }
                            }
                        }
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .padding(.horizontal, Sizes.largePadding)
                    .padding(.top, Sizes.mediumLargePadding)
                    .disabled(viewModel.paymentsBlockedType?.blocksPayments ?? false)
                }
            }
            .disabled(viewModel.isLoading || viewModel.isLoadingAutoPayAfterDelete)
            
            DividerView()
                .padding(.top, Sizes.largePadding + Sizes.extraSmallPadding)
        }
        .background(Colors.cardBackground)
    }
}
