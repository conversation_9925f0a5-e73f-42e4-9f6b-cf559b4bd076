//
//  ActivityFeedBaseItemView.swift
//  1VALET
//
//  Created by <PERSON> on 30/06/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI

struct ActivityFeedBaseItemView: View {
    // MARK: - Typealias
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    private typealias ImageNames = HomeWidgetsConstants.ImageNames
    private typealias Limits = HomeWidgetsConstants.Limits
    
    // MARK: - State
    
    var item: any ActivityFeedBaseItemProtocol
    var showCloseIcon: Bool = true
    var isUnread: Bool = false
    var dateLineLimit: Int = Limits.notificationDateLineLimit
    private(set) var action: (ActivityFeedBaseItemAction) -> Void
    
    // MARK: - Private Properties
    
    var body: some View {
        ZStack(alignment: .trailing) {
            VStack(alignment: .trailing, spacing: 0) {
                if showCloseIcon || isUnread {
                    VStack(alignment: .trailing, spacing: 0) {
                        Group {
                            if showCloseIcon {
                                Image(ImageNames.close)
                                    .renderingMode(.template)
                                    .resizable()
                                    .scaledToFit()
                                    .foregroundColor(Colors.baseText)
                                    .frame(width: Sizes.notificationIconSize, height: Sizes.notificationIconSize)
                            } else if isUnread {
                                Circle()
                                    .fill(Colors.readDot)
                                    .frame(width: Sizes.mediumPadding, height: Sizes.mediumPadding)
                                    .padding(Sizes.smallPadding)
                            }
                        }
                        .padding(.trailing, Sizes.smallPadding)
                        .padding(.top, Sizes.smallPadding)
                        
                        Spacer(minLength: 0)
                    }
                    .frame(
                        width: Sizes.activityFeedItemHeight / 2,
                        height: Sizes.activityFeedItemHeight / 2,
                        alignment: .topTrailing
                    )
                    .background(item.type.backgroundColor)
                    .onTapGesture {
                        if showCloseIcon {
                            action(.hide)
                        }
                    }
                } else {
                    Spacer()
                }
                
                if let widgetButtonTitle = item.type.widgetButtonTitle,
                   let widgetButtonStyle = item.type.widgetButtonStyle,
                   let widgetButtonIconType = item.type.widgetButtonIconType {
                    VStack(alignment: .trailing, spacing: 0) {
                        Spacer(minLength: 0)
                        
                        WidgetActionButton(
                            title: widgetButtonTitle,
                            style: widgetButtonStyle,
                            iconType: widgetButtonIconType
                        )
                        .padding(.vertical, Sizes.smallPadding)
                        .padding(.horizontal, Sizes.mediumPadding)
                    }
                    .frame(
                        height: Sizes.activityFeedItemHeight / 2,
                        alignment: .bottomTrailing
                    )
                    .background(item.type.backgroundColor)
                    .onTapGesture {
                        action(.actionButton)
                    }
                } else {
                    Spacer()
                }
            }
            
            HStack(alignment: .top, spacing: 0) {
                Image(item.type.icon)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(item.type.iconColor)
                    .frame(width: Sizes.notificationIconSize, height: Sizes.notificationIconSize)
                    .padding(.trailing, Sizes.smallPadding)
                
                VStack(alignment: .leading, spacing: Sizes.extraSmallPadding / 2) {
                    Text(item.body.removingHtml ?? "")
                        .lineLimit(Limits.notificationTitleLineLimit)
                        .fixedSize(horizontal: false, vertical: true)
                        .font(Fonts.notificationTitle)
                        .foregroundColor(item.type.labelsColor)
                    
                    Text(item.dateString)
                        .font(Fonts.notificationDate)
                        .foregroundColor(item.type.labelsColor)
                        .lineLimit(dateLineLimit)
                    
                    Spacer(minLength: 0)
                }
                
                Spacer(minLength: 0)
            }
            .padding(.top, Sizes.smallPadding)
            .padding(.trailing, showCloseIcon || isUnread ? Sizes.activityFeedItemHeight / 2 : Sizes.mediumPadding)
        }
        .padding(.leading, Sizes.mediumPadding)
        .frame(height: Sizes.activityFeedItemHeight)
        .cardify(backgroundColor: item.type.backgroundColor) {
            action(.visualize)
        }
    }
}

enum ActivityFeedBaseItemAction {
    case visualize
    case actionButton
    case hide
}

protocol ActivityFeedBaseItemProtocol: Hashable, Equatable {
    associatedtype ItemType: ActivityFeedBaseItemTypeProtocol
    
    var id: String { get }
    var type: ItemType { get }
    var body: String { get }
    var dateString: String { get }
}

protocol ActivityFeedBaseItemTypeProtocol: Hashable, Equatable {
    associatedtype DestinationType
    
    var icon: String { get }
    var iconColor: Color { get }
    var backgroundColor: Color { get }
    var labelsColor: Color { get }
    var widgetButtonStyle: WidgetButtonStyle? { get }
    var widgetButtonIconType: WidgetButtonIconType? { get }
    var widgetButtonTitle: String? { get }
    
    func navigationAction() -> DestinationType
}
