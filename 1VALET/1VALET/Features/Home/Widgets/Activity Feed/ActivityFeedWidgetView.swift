//
//  ActivityFeedWidgetView.swift
//  1VALET
//
//  Created by <PERSON> on 21/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import NetworkKit
import UtilitiesKit
import Popovers

struct ActivityFeedWidgetView: View {
    // MARK: - Typealias
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    private typealias ImageNames = HomeWidgetsConstants.ImageNames
    private typealias Limits = HomeWidgetsConstants.Limits
    
    // MARK: - Private Properties
    
    @StateObject private var welcomeHomeSettings = WelcomeHomeWidgetSettings.sharedInstance
    @EnvironmentObject private var activityFeedSettings: ActivityFeedWidgetSettings
    @SwiftUI.Environment(\.colorScheme) var colorScheme
    @Injected private var persistenceManager: PersistenceManager
    
    // MARK: - Properties
    @State var nextView: ActivityFeedWidgetItemDestination?
    @State var isLoading: Bool = false
    @State var destination: ActivityFeedDestination?
    @State var showEmptyStateComplete: Bool = false
    @State var totalUnread: Int = 0
    @State private var showPopOverMenu: Bool = false
    @State var unreadItems: [ActivityFeedItem] = [
        ActivityFeedItem(
            id: "placeholder",
            type: .parcelDelivery(id: "placeholder"),
            title: "Parcel Delivery",
            body: "You have a delivery for pick-up",
            readOn: nil,
            date: Date(),
            priority: 0,
            alternativeDetailsBody: nil
        )
    ]
    @Binding var isParentLoading: Bool
    @Binding var popUp: PopUpAlert?
    @State private var reloadView: Bool = false
    // MARK: - Public Properties
    
    var body: some View {
        ActivityFeedItemBaseNavigationView(nextView: $nextView) {
            VStack(alignment: .leading, spacing: 0) {
                HStack(alignment: .center, spacing: 0) {
                    Text(Strings.activity)
                        .font(Fonts.sectionTitle)
                        .foregroundColor(Colors.baseText)
                    
                    Spacer()
                    
                    myActivityButton
                }
                
                if unreadItems.isEmpty {
                    if showEmptyStateComplete {
                        VStack(alignment: .center, spacing: Sizes.extraSmallPadding) {
                            Image(ImageNames.checkCircle)
                                .resizable()
                                .renderingMode(.template)
                                .foregroundColor(Colors.checkmarkTint)
                                .scaledToFit()
                                .frame(width: Sizes.noNewNotificationsIconSize, height: Sizes.noNewNotificationsIconSize)
                            
                            Text(Strings.noNewNotificationsTitle)
                                .font(Font.h4(.semiBold))
                                .foregroundColor(Colors.baseText)
                            
                            Text(Strings.noNewNotificationsSubtitle)
                                .lineLimit(Limits.noNotificationsSubtitleLineLimit)
                                .multilineTextAlignment(.center)
                                .font(Font.h5(.regular))
                                .foregroundColor(Colors.baseText)
                                .frame(maxWidth: .infinity, alignment: .center)
                        }
                        .padding(.vertical, Sizes.mediumPadding)
                        .onAppear {
                            UserDefaults.standard.set(false, forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED)
                        }
                    } else {
                        HStack(alignment: .center, spacing: Sizes.smallPadding) {
                            Image(ImageNames.checkCircle)
                                .resizable()
                                .renderingMode(.template)
                                .scaledToFit()
                                .foregroundColor(Colors.checkmarkTint)
                                .frame(width: Sizes.noNewNotificationsSmallIconSize, height: Sizes.noNewNotificationsSmallIconSize)
                            
                            Text(Strings.noNewNotificationsSmallTitle)
                                .lineLimit(2)
                                .multilineTextAlignment(.leading)
                                .font(Font.h4(.regular))
                                .foregroundColor(Colors.baseText)
                                .fixedSize()
                        }
                        .padding(.vertical, Sizes.mediumPadding)
                    }
                } else {
                    VStack(alignment: .center, spacing: Sizes.smallPadding) {
                        ForEach(Array(unreadItems.prefix(Limits.maxNotifications(if: activityFeedSettings.canUseExtraSpace)))) { item in
                            ActivityFeedBaseItemView(
                                item: item,
                                showCloseIcon: true,
                                isUnread: item.readOn == nil && item.updateReadOnTo == nil) { action in
                                    let readOn = Date()
                                    
                                    Task {
                                        await markAsRead(with: item.id, readOn: readOn)
                                    }
                                    
                                    switch action {
                                    case .visualize:
                                        var updatedItem = item
                                        updatedItem.readOn = readOn
                                        nextView = .details(item: updatedItem)
                                    case .actionButton:
                                        if let asyncNavigationAction = item.type.asyncNavigationAction() {
                                            Task {
                                                isParentLoading = true
                                                
                                                do {
                                                    let action = try await asyncNavigationAction()
                                                    isParentLoading = false
                                                    DispatchQueue.main.async {
                                                        if let destination = action.destination?() {
                                                            NavigationUtil.currentNavigationController()?.pushViewController(destination, animated: true)
                                                        } else if let popUpTitle = action.popUpTitle {
                                                            popUp = PopUpAlert(title: popUpTitle, type: .ok)
                                                        }
                                                    }
                                                } catch {
                                                    isParentLoading = false
                                                    popUp = .genericError(error: error)
                                                }
                                            }
                                        } else {
                                            nextView = item.type.navigationAction()
                                        }
                                    default:
                                        break
                                    }
                                }
                                .redacted(when: isLoading || item.id == "placeholder", redactionType: .customPlaceholder)
                                .tag(item.id)
                                .id(item.id)
                                .transition(.opacity)
                        }
                    }
                    .animation(.easeInOut(duration: 0.2), value: unreadItems)
                    .padding(.vertical, Sizes.mediumPadding)
                    .disabled(isLoading || unreadItems.first?.id == "placeholder")
                }
            }
            .padding(.horizontal, Sizes.largePadding)
            .onAppear {
                let needsMerge = unreadItems.filter({ $0.id != "placeholder" }).isEmpty
                Logger.print("ActivityFeedWidgetView - onAppear - needsMerge: \(needsMerge)")
                
                loadMoreItems()
                
                showEmptyStateComplete = UserDefaults.standard.bool(forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED)
            }
            .onDisappear {
                showEmptyStateComplete = UserDefaults.standard.bool(forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED)
            }
            .onReceive(NotificationCenter.default.publisher(for: UIScene.willEnterForegroundNotification)) { _ in
                reloadData()
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeView.rawValue))) { _ in
                reloadData()
            }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeViewUI.rawValue))) { _ in
                reloadView.toggle()
            }
            .id(reloadView)
            .onChange(of: unreadItems) { welcomeHomeSettings.unreadItems = $0 }
            .onChange(of: totalUnread) { welcomeHomeSettings.totalUnreadItems = $0 }
            .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.activityFeedNotificationReceived.rawValue))) { _ in
                guard !isLoading else { return }
                Logger.print("ActivityFeedWidgetView - onReceive")
                
                loadMoreItems()
            }
            .navigationDestination(for: $destination) { destination in
                switch destination {
                case .viewAll:
                    NavigationLazyView(ActivityFeedWidgetViewAllView(unreadItems: unreadItems, totalUnread: totalUnread))
                }
            }
        }
    }
}

private extension ActivityFeedWidgetView {
    
    private func reloadData() {
        unreadItems = []
        totalUnread = 0
        loadMoreItems()
        showEmptyStateComplete = UserDefaults.standard.bool(forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED)
    }
    
    @ViewBuilder
    var myActivityButton: some View {
        if totalUnread > 0 {
            Button {
                showPopOverMenu = true
            } label: {
                Text("\(totalUnread > 99 ? "99+" : String(totalUnread)) \(Strings.unread.lowercased())")
                    .underline()
                    .foregroundColor(Color.coolGrey)
                    .font(Font.textLinkSmall(.bold))
            }
            .popover(present: $showPopOverMenu) {
                popOverMenu
            }
        } else {
            Button(action: {
                AnalyticsUtil.sendEvent(
                    event: .buttonPressed(
                        action: AnalyticsHomeAction.activityFeedViewAll,
                        screen: .home
                    )
                )
                destination = .viewAll
            }) {
                Text(Strings.myActivity)
                    .underline()
            }
            .buttonStyle(WidgetViewAllButtonStyle())
        }
    }
    
    @ViewBuilder
    var popOverMenu: some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Button(Strings.markAllAsRead) {
                showPopOverMenu = false
                if totalUnread > Limits.maxNotifications(if: activityFeedSettings.canUseExtraSpace) {
                    markAllAsReadPopUp()
                } else {
                    Task {
                        await clearAll()
                    }
                }
            }
            
            Button(Strings.viewAll) {
                showPopOverMenu = false
                AnalyticsUtil.sendEvent(
                    event: .buttonPressed(
                        action: AnalyticsHomeAction.activityFeedViewAll,
                        screen: .home
                    )
                )
                destination = .viewAll
            }
        }
        .popoverStyle()
    }
}

private extension ActivityFeedWidgetView {
    func markAllAsReadPopUp() {
        // TODO: Change to SwiftUI once HomeViewController changes to SwiftUI
        let cancelAction = PopUpAlertButton(title: "CANCEL".localized(), style: .cancel)
        let okAction = PopUpAlertButton(title: "OK".localized(), style: .standard, action: {
            Task {
                await clearAll()
            }
        })
        
        AppWideState.sharedInstance.popUp = PopUpAlert(
            title: Strings.markAllAsReadPopUpTitle(totalUnread: totalUnread),
            buttonArrangementLayout: .horizontal,
            buttons: [cancelAction, okAction]
        )
    }
    
    func loadMoreItems() {
        Logger.print("ActivityFeedWidgetView - \(#function)")
        isLoading = unreadItems.first?.id == "placeholder"
        showPopOverMenu = false
        
        Task {
            do {
                _ = try await persistenceManager.updatePriority()
                
                let (count, items) = try await ActivityFeedItem.fetch(unread: true, page: 1)
                unreadItems = items
                
                Logger.print("ActivityFeedWidgetView - \(#function) - newItems added")
                
                if let count = count {
                    totalUnread = count
                    
                    AppState.sharedInstance.setBadge(count: count)
                }
                
                UserDefaults.standard.set(!unreadItems.isEmpty, forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED)
                if UserDefaults.standard.bool(forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED) {
                    showEmptyStateComplete = UserDefaults.standard.bool(forKey: UserDefaultsConstants.SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED)
                }
            } catch {
                Logger.print("error: \(error)")
                ExceptionLoggingUtils.report(error)
            }
            
            isLoading = false
        }
    }
    
    func markAsRead(with id: String, readOn: Date) async {
        if let index = unreadItems.firstIndex(where: { id == $0.id }) {
            if index < unreadItems.endIndex - 1 {
                if welcomeHomeSettings.lastSeenItem == id {
                    welcomeHomeSettings.lastSeenItem = unreadItems[index + 1].id
                }
            }
            
            let item = unreadItems.remove(at: index)
            await item.markAsRead(on: readOn)
            
            loadMoreItems()
        }
    }
    
    func clearAll() async {
        do {
            try await persistenceManager.safeMarkAllAsRead()
            
            unreadItems.removeAll()
            totalUnread = 0
            AppState.sharedInstance.setBadge(count: 0)
        } catch {
            Logger.print("error: \(error)")
            ExceptionLoggingUtils.report(error)
        }
    }
}

class ActivityFeedWidgetSettings: ObservableObject {
    @Published var canUseExtraSpace: Bool = !(CapabilityUtils.hasDoorAccess() || CapabilityUtils.hasProximityKey())
}

enum ActivityFeedDestination {
    case viewAll
}
