//
//  DigitalAccessWidgetView.swift
//  1VALET
//
//  Created by <PERSON> on 13/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import NetworkKit
import UtilitiesKit

struct DigitalAccessWidgetView: View {
    // MARK: - Typealias
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    private typealias ImageNames = HomeWidgetsConstants.ImageNames
    private typealias Limits = HomeWidgetsConstants.Limits
    
    // MARK: - Private Properties
    
    @EnvironmentObject private var activityFeedSettings: ActivityFeedWidgetSettings
    @StateObject private var welcomeHomeSettings = WelcomeHomeWidgetSettings.sharedInstance
    
    // MARK: - View Model
    
    @ObservedViewModel var viewModel: DigitalAccessWidgetViewModelProtocol
    @State var destination: DigitalAccessDestination?
    
    // MARK: - Private Properties
    @SwiftUI.Environment(\.residentType) private var residentType
    @Binding private(set) var shouldFetchData: Bool

    private(set) var showRemoteUnlockView: ((RemoteDoor) -> Void)?
    private(set) var showProximityUnlockView: ((ProximityKeyResponse) -> Void)?
    private(set) var isEnabled: Bool = true
    
    @State private var shouldShowWidget: Bool = true
    @State private var doNotShowInfoCard: Bool = UserDefaultsUtils.sharedInstance.defaults.bool(forKey: UserDefaultsConstants.DO_NOT_SHOW_WIDGETS_INFO_BANNER) {
        didSet {
            UserDefaultsUtils.sharedInstance.defaults.set(doNotShowInfoCard, forKey: UserDefaultsConstants.DO_NOT_SHOW_WIDGETS_INFO_BANNER)
        }
    }
    @State private var reloadView: Bool = false
    
    private var isCurrentResident: Bool {
        residentType == .currentResident || residentType == .guest
    }
    
    // MARK: - Public Properties
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(alignment: .center, spacing: 0) {
                Text(Strings.digitalEntry)
                    .font(Fonts.sectionTitle)
                    .foregroundColor(Colors.baseText)
                
                Spacer()
                
                if isEnabled && isCurrentResident,
                   (viewModel.proximityKey != nil && viewModel.doors.count > Limits.maxRemoteDoorsWithProxCard) || (viewModel.doors.count >= Limits.maxRemoteDoorsWithoutProxCard) {
                    Button(action: {
                        AnalyticsUtil.sendEvent(
                            event: .buttonPressed(
                                action: AnalyticsHomeAction.myDoors,
                                screen: .home
                            )
                        )
                        
                        destination = .myDoors
                    }) {
                        Text(Strings.myDoors)
                            .underline()
                    }
                    .buttonStyle(WidgetViewAllButtonStyle())
                }
            }
            
            if !doNotShowInfoCard {
                InfoCardView(message: Strings.addWidget, closeAction: {
                    doNotShowInfoCard = true
                }, buttonAction: {
                    if let url = URL(string: Strings.learnHowURL) {
                        UIApplication.shared.open(url)
                    }
                })
                .padding(.top, Sizes.smallPadding)
            }
            
            if !isCurrentResident {
                Text(viewModel.futureResidentSubtitle)
                    .font(Fonts.marketplaceSectionTitle)
                    .foregroundColor(Colors.baseText)
                    .padding(.top, Sizes.smallPadding)
            }
            
            if !isEnabled {
                HStack(spacing: 0) {
                    Image(ImageNames.alert)
                        .resizable()
                        .renderingMode(.template)
                        .foregroundColor(Colors.errorWarning)
                        .frame(
                            width: Sizes.digitalAccessIconSize,
                            height: Sizes.digitalAccessIconSize
                        )
                        .scaledToFit()
                    
                    Text(Strings.guestAccessDoorsUnavailable)
                        .font(Fonts.digitalAccessUnavailableSubtitle)
                        .foregroundColor(Colors.baseText)
                        .padding(.leading, Sizes.smallPadding)
                    
                    Spacer()
                }
                .padding(Sizes.mediumLargePadding)
                .frame(maxWidth: .infinity)
                .cardify()
                .padding(.top, Sizes.mediumPadding)    
            }
            
            HStack(alignment: .center, spacing: Sizes.mediumPadding) {
                if let proxCard = viewModel.proximityKey {
                    DigitalAccessWidgetItemView(type: .proxCard,
                                                logoURL: proxCard.brandedProximityKeyUrl,
                                                subtitle: Strings.digitalFob,
                                                icon: ImageNames.wireless) {
                                                    showProximityUnlockView?(proxCard.proximityCard)
                                                }
                                                .frame(maxWidth: !viewModel.doors.isEmpty ? nil : Sizes.digitalAccessItemMaxWidth)
                                                .layoutPriority(1)
                }
                
                if !viewModel.doors.isEmpty {
                    remoteDoors(hasProxCard: viewModel.proximityKey != nil)
                }
            }
            .padding(.vertical, Sizes.mediumPadding)
            .disabled(!isCurrentResident || !isEnabled)
        }
        .padding(.horizontal, Sizes.largePadding)
        .hideFromParent(shouldHide: !shouldShowWidget)
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeView.rawValue))) { _ in
            Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { _ in
                viewModel.initializeKeys(shouldFetchData: true)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeViewUI.rawValue))) { _ in
            reloadView.toggle()
        }
        .id(reloadView)
        .onAppear {
            viewModel.initializeKeys(shouldFetchData: false)
        }
        .onChange(of: shouldFetchData) { shouldFetchData in
            viewModel.initializeKeys(shouldFetchData: shouldFetchData)
        }
        .onReceive(viewModel.shouldShow) { shouldShow in
            shouldShowWidget = shouldShow
            activityFeedSettings.canUseExtraSpace = !shouldShow
        }
        .onReceive(AppState.sharedInstance.$needsRefreshProxKey) { needsRefresh in
            if needsRefresh {
                viewModel.initializeKeys(shouldFetchData: false)
                AppState.sharedInstance.needsRefreshProxKey = false
            }
        }
        .onChange(of: viewModel.doors) { doors in
            welcomeHomeSettings.reloadItems = true
            
            welcomeHomeSettings.canShowGuestAccess = !doors.isEmpty
        }
        .navigationDestination(for: $destination) { destination in
            switch destination {
            case .myDoors:
                DigitalAccessViewControllerRepresentable()
                    .edgesIgnoringSafeArea(.all)
                    .navigationBarHidden(true)
            }
        }
    }
}

private extension DigitalAccessWidgetView {
    @ViewBuilder
    func remoteDoors(hasProxCard: Bool) -> some View {
        if hasProxCard {
            if viewModel.doors.count <= Limits.maxRemoteDoorsWithProxCard {
                ForEach(viewModel.doors) { remoteDoor in
                    DigitalAccessWidgetItemView(remoteDoor: remoteDoor.remoteDoor,
                                                type: .remoteDoor,
                                                title: remoteDoor.title,
                                                subtitle: Strings.remoteUnlock,
                                                icon: ImageNames.lock) {
                                                    showRemoteUnlockView?(remoteDoor.remoteDoor)
                                                }
                                                .layoutPriority(1)
                }
            } else {
                DigitalAccessWidgetMultipleItemsView(doors: viewModel.doors) {
                    showRemoteUnlockView?($0)
                }
                .layoutPriority(1)
            }
        } else {
            if viewModel.doors.count <= Limits.maxRemoteDoorsWithoutProxCard {
                ForEach(viewModel.doors) { remoteDoor in
                    DigitalAccessWidgetItemView(remoteDoor: remoteDoor.remoteDoor,
                                                type: .remoteDoor,
                                                title: remoteDoor.title,
                                                subtitle: Strings.remoteUnlock,
                                                icon: ImageNames.lock) {
                                                    showRemoteUnlockView?(remoteDoor.remoteDoor)
                                                }
                                                .frame(maxWidth: viewModel.doors.count > 1 ? nil : Sizes.digitalAccessItemMaxWidth)
                                                .layoutPriority(1)
                }
            } else {
                Group {
                    if let firstDoor = viewModel.doors.first {
                        DigitalAccessWidgetItemView(remoteDoor: firstDoor.remoteDoor,
                                                    type: .remoteDoor,
                                                    title: firstDoor.title,
                                                    subtitle: Strings.remoteUnlock,
                                                    icon: ImageNames.lock) {
                            showRemoteUnlockView?(firstDoor.remoteDoor)
                        }
                        .layoutPriority(1)
                    }
                    
                    DigitalAccessWidgetMultipleItemsView(doors: Array(viewModel.doors[1..<3])) {
                        showRemoteUnlockView?($0)
                    }
                    .layoutPriority(1)
                }
            }
        }
    }
}
