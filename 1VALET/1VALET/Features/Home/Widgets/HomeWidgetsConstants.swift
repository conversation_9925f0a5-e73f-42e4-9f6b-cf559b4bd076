//
//  HomeWidgetsConstants.swift
//  1VALET
//
//  Created by <PERSON> on 07/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import Foundation
import SwiftUI

public enum HomeWidgetsConstants {
    enum Colors {
        static var background: Color { Color.appBackground }
        static var icon: Color { Color.slateGrey }
        static var baseText: Color { Color.coolGrey }
        
        static var navigationIcon: Color { Color.coolGrey }
        static var cardBackground: Color { Color.pearlWhite }
        static var cardShadow: Color { Color.shadow }
        
        static var myBookingBackground: Color { Color.primaryBackground }
        static var startBookingBackground: Color { Color.aqua }
        static var myBookingTitle: Color { Color.alwaysWhite }
        static var myBookingicon: Color { Color.secondaryLabel }
        
        static var widgetActionButtonRedStyleBackground: Color { Color.errorWarning }
        static var widgetActionButtonGreenStyleBackground: Color { Color.aqua }
        static var widgetActionButtonGreyStyleBackground: Color { Color.secondaryBackground }
        static var widgetActionButtonStandardStyleTitle: Color { Color.alwaysWhite }
        static var widgetActionButtonGreyStyleTitle: Color { Color.secondaryLabel }
        static var widgetActionButtonSecondSubtitle: Color { Color.slateGrey }
        
        static var noImageMarketplaceBackgroundColor: Color { Color.alwaysCloudyGrey }
        static var marketplacePrice: Color { Color.alwaysWhite }
        static var marketplaceOrange: Color { Color.warmOrange }
        static var marketplaceCreatePostBackground: Color { Color.coolBlue }
        
        static var proximityCardForeground: Color { Color.alwaysPearlWhite }
        static var proximityCardTitleForeground: Color { Color.proximityKeySubtitle }
        static var proximityCardBackground: Color { Color.alwaysNavyBlue }
        static var remoteDoorCardBackground: Color { Color.pearlWhite }
        static var proximityCardItemIconForeground: Color { Color.aqua }
        static var remoteDoorCardItemIconForeground: Color { Color.coolBlue }
        static var didYouKnowIconBackground: Color { Color.alwaysWarmYellow }
        
        static var welcomeHomeProfileInitials: Color { Color.alwaysWhite }
        static var welcomeHomeProfileInitialsBackground: Color { Color.coolBlue }
        static var welcomeHomeBadge: Color { Color.alwaysWhite }
        static var welcomeHomeItemIcon: Color { Color.coolBlue }
        
        static var noNotificationsIconBackgroundBackground: Color { Color.aqua }
        
        static var activityFeedErrorForeground: Color { Color.errorLabel }
        static var activityFeedErrorBackground: Color { Color.errorBackground }
        static var activityFeedBlueForeground: Color { Color.secondaryLabel }
        static var activityFeedBlueBackground: Color { Color.secondaryBackground }
        
        static var activityFeedDetailsIcon: Color { Color.alwaysWhite }
        static var activityFeedDetailsIconBackground: Color { Color.aqua }
        static var activityFeedDetailsDateIcon: Color { Color.secondaryLabel }
        static var readDot: Color { Color.warmOrange }

        static var addSuiteIconTint: Color { Color.coolBlue }
        static var checkmarkTint: Color { Color.aqua }

        static var noticeBackground: Color { Color.errorWarning }
        static var noticeTextForeground: Color { Color.alwaysWhite }
        
        static var errorWarning: Color { Color.errorWarning }
        
        static var placeholder: Color { Color.placeholderText }
        
        static var backgroundRectangleDescription: Color { Color.pearlWhite }
        
        static var coffeeTint: Color { Color.pearlWhite }
        
        
        static var noImageMarketplaceForegroundColor: Color { Color.alwaysSlateGrey }
        
        static let autoPayStatusAutoPayPausedForeground = Color.almostGrey
    }
    
    enum Fonts {
        static let sectionTitle = Font.h3(.bold)
        static let body = Font.body(.regular)
        static let bodyBold = Font.body(.bold)
        static let bodySemiBold = Font.body(.semiBold)
        
        static let messageContentFontFamily = TextStyle.body.font.familyName
        static let messageContentFontSize = "\(TextStyle.body.font.pointSize)"
        
        static let amenityTitle = Font.h3(.semiBold)
        static let amenityBookingTitle = Font.h4(.semiBold)
        static let takeAdvantageOfAmenities = Font.h4(.semiBold)
        static let amenitySubtitle = Font.h5(.regular)
        static let amenityViewMoreCard = Font.footnote(.semiBold)
        static let myBookingTitle = Font.h4(.bold)
        
        static func widgetActionButtonTitle(for size: CGFloat) -> Font {
            Font.defaultFont(size: size, style: .bold)
        }
        
        static let marketplaceTitle = Font.h3(.semiBold)
        static let marketplacePrice = Font.h4(.bold)
        static let marketplaceSectionTitle = Font.h4(.regular)
        static let freeSomeSpace = Font.h4(.semiBold)
        
        static let digitalAccessSubtitle = Font.defaultFont(size: 12, style: .bold)
        static let digitalAccessUnavailableSubtitle = Font.h5(.semiBold)
        
        static let multipleRemoteDoorsTitle = Font.h4(.regular)
        
        static let welcomeHomeTitle = Font.h5(.regular)
        static let welcomeHomeSubtitle = Font.h3(.semiBold)
        static let welcomeHomeSmallSubtitle = Font.footnote(.semiBold)
        static let welcomeHomeImageText = Font.h1(.bold)
        static let welcomeHomeFutureResidentMessage = Font.h4(.bold)
        static let welcomeHomeSuiteNumberLabel = Font.h4(.regular)
        static let welcomeHomeBadge = Font.h5(.bold)
        
        static let notificationTitle = Font.h4(.semiBold)
        static let notificationDate = Font.h5(.regular)
        static let noNotificationsTitle = Font.h3(.semiBold)
        static let noNotificationsSubtitle = Font.h5(.regular)
        
        static let accessInfoDateTitle = Font.h4(.semiBold)
        static let accessInfoDateSubtitle = Font.h4(.regular)
        static let accessInfoAccessErrorTitle = Font.h5(.semiBold)
        static let accessInfoAccessErrorSubtitle = Font.h5(.regular)
        
        static let addSuiteButton = Font.h4(.semiBold)
        
        static let activityFeedDetailsDateLabel = Font.h4(.regular)
        
        static let noticeText = Font.h5(.semiBold)
        static let noticeButtonText = Font.defaultFont(size: 12, style: .bold)
        
        static let placeholder = Font.placeholder

    }
    
    enum Strings {
        static let amenities = "TAB_AMENITY_BOOKING".localized()
        static let viewMore = "WIDGETS_AMENITY_VIEW_MORE".localized()
        static let viewAll = "WIDGETS_AMENITY_VIEW_ALL".localized()
        static let takeAdvantageOfAmenities = "WIDGETS_AMENITY_TAKE_ADVANTAGE_OF_AMENITIES".localized()
        static let bookNow = "WIDGETS_AMENITY_VIEW_BOOK_NOW".localized()
        static let viewBooking = "WIDGETS_AMENITY_VIEW_VIEW_BOOKING".localized()
        static let upcomingBookingDateFormat = "MMMM dd"
        static let upcomingHourWithoutMinutesFormat = "h a"
        static let upcomingHourWithMinutesFormat = "h:mm a"
        
        static let freeTitle = "MARKETPLACE_FREE".localized().uppercased()
        static let marketplaceTitle = "MARKETPLACE".localized()
        static let connectWithCommunity = "WIDGETS_MARKETPLACE_CONNECT".localized()
        static let freeSomeSpace = "WIDGETS_MARKETPLACE_FREE_SPACE".localized()
        static let postItem = "WIDGETS_MARKETPLACE_POST_ITEM".localized().uppercased()
        static let communityPosts = "WIDGETS_MARKETPLACE_COMMUNITY_POSTS".localized()
        static let you = "YOU".localized()
        static let offers = "WIDGETS_MARKETPLACE_OFFERS".localized()
        static let homeInsurance = "WIDGETS_MARKETPLACE_HOME_INSURANCE".localized()
        static let freeQuote = "WIDGETS_MARKETPLACE_FREE_QUOTE".localized()
        static let myCommunityTitle = "MARKETPLACE_COMMUNITY".localized()
        
        static let digitalEntry = "WIDGETS_DIGITAL_ACCESS_ENTRY".localized()
        static let myDoors = "WIDGETS_DIGITAL_ACCESS_MY_DOORS".localized()
        static let accessToDoorsOnMoveIn = "WIDGETS_DIGITAL_ACCESS_ACCESS_ON_MOVE_IN".localized()
        static let digitalFob = "WIDGETS_DIGITAL_ACCESS_DIGITAL_FOB".localized().uppercased()
        static let remoteUnlock = "WIDGETS_DIGITAL_ACCESS_REMOTE_UNLOCK".localized().uppercased()
        
        static let doorSensorToast = "DOOR_SENSOR_TOAST".localized()
        static let off = "OFF".localized().uppercased()
        static let on = "ON".localized().uppercased()
        static let doorSensors = "DOOR_SENSOR_QUICK_ACTION_TITLE".localized()
        static let thermostat = "THERMOSTAT".localized()
        static let pay = "WIDGETS_WELCOME_HOME_PAY".localized()
        static let sendMessage = "WIDGETS_WELCOME_HOME_SEND_MESSAGE".localized()
        static let startRequest = "WIDGETS_WELCOME_HOME_START_REQUEST".localized()
        static let postItemLabel = "WIDGETS_WELCOME_HOME_POST_ITEM".localized()
        static let guestAccess = "WIDGETS_WELCOME_HOME_GUEST_ACCESS".localized()
        static let balance = "WIDGETS_WELCOME_HOME_BALANCE".localized()
        static let alert = "WIDGETS_WELCOME_DOOR_SENSOR_ALERT".localized()
        static let deliveries = "WIDGETS_WELCOME_HOME_DELIVERIES".localized()
        static let myOrders = "WIDGETS_WELCOME_HOME_MY_ORDERS".localized()
        
        static let deliveryForPickup = "WIDGETS_ACTIVITY_FEED_DELIVERY_FOR_PICK_UP".localized()
        static let notificationDateFormat = "MMM dd, YYYY, h:mm a"
        static let notificationTimeFormat = "h:mm a"
        static let notificationWeekdayFormat = "EEEE, h:mm a"
        static let today = "WIDGETS_ACTIVITY_FEED_TODAY".localized()
        static let yesterday = "WIDGETS_ACTIVITY_FEED_YESTERDAY".localized()
        static let viewCode = "WIDGETS_ACTIVITY_FEED_VIEW_CODE".localized()
        static let markAsRetrieved = "WIDGETS_ACTIVITY_FEED_MARK_RETRIEVED".localized()
        static let activity = "WIDGETS_ACTIVITY_FEED_ACTIVITY".localized()
        static let clearAll = "WIDGETS_ACTIVITY_FEED_CLEAR_ALL".localized()
        static let noNewNotificationsTitle = "WIDGETS_ACTIVITY_FEED_ALL_CAUGHT_UP".localized()
        static let noNewNotificationsSubtitle = "WIDGETS_ACTIVITY_FEED_NEW_NOTIFICATIONS_APPEAR_HERE".localized()
        static let lastDay = "WIDGETS_ACTIVITY_FEED_LAST_DAY".localized()
        static let previousNotifications = "WIDGETS_ACTIVITY_FEED_PREVIOUS_NOTIFICATIONS".localized()
        static let noNotificationsTitle = "WIDGETS_ACTIVITY_FEED_SIT_BACK".localized()
        static let noNotificationsSubtitle = "WIDGETS_ACTIVITY_FEED_ALL_NOTIFICATIONS".localized()
        static let noNewNotificationsSmallTitle = "WIDGETS_ACTIVITY_FEED_SMALL_NEW_NOTIFICATIONS_APPEAR_HERE".localized()
        static let movinIsToday = "WIDGETS_WELCOME_HOME_MOVIN_TODAY".localized()
        static let digitalAccessShortly = "WIDGETS_DIGITAL_ACCESS_ACCESS_SHORTLY".localized()
        static let viewBalance = "WIDGETS_ACTIVITY_FEED_VIEW_BALANCE".localized()
        static let viewBalanceCTA = "WIDGETS_ACTIVITY_FEED_VIEW_BALANCE_CTA".localized()
        static let viewBookingCTA = "WIDGETS_ACTIVITY_FEED_VIEW_BOOKING".localized()
        static let replyCTA = "WIDGETS_ACTIVITY_FEED_REPLY".localized()
        static let viewMoreCTA = "WIDGETS_ACTIVITY_FEED_VIEW_MORE".localized()
        static let viewPost = "WIDGETS_ACTIVITY_FEED_VIEW_POST".localized()
        static let all = "WIDGETS_ACTIVITY_FEED_ALL".localized()
        static let myActivity = "WIDGETS_ACTIVITY_FEED_MY_ACTIVITY".localized()
        static let unread = "WIDGETS_ACTIVITY_FEED_UNREAD".localized()
        static let markAllAsRead = "WIDGETS_ACTIVITY_FEED_MARK_ALL_AS_READ".localized()
        static let accountInfo = "PROFILE_ACCOUNT_INFO".localized()
        static let accountInfoCTA = "WIDGETS_ACTIVITY_FEED_ACCOUNT_INFO".localized()
        static let paymentSettings = "PAYMENTS_SETTINGS".localized()
        static let paymentSettingsCTA = "WIDGETS_ACTIVITY_FEED_PAYMENTS_SETTINGS".localized()
        static let viewRequest = "WIDGETS_ACTIVITY_FEED_VIEW_REQUEST".localized()
        static let viewOrder = "WIDGETS_ACTIVITY_FEED_VIEW_ORDER".localized()
        
        static let viewOffers = "LEASE_VIEW_OFFERS".localized()
        static let view = "WIDGETS_ACTIVITY_FEED_VIEW".localized()
        static let review = "WIDGETS_ACTIVITY_FEED_REVIEW".localized()
        static let reviewStaffRequestExpired = "WIDGETS_ACTIVITY_FEED_PARCEL_REQUEST_EXPIRED".localized()
        
        static let didYouKnow = "WIDGETS_DIGITAL_ACCESS_DID_YOU_KNOW".localized()
        static let addWidget = "WIDGETS_DIGITAL_ACCESS_ADD_WIDGET".localized()
        static let learnHow = "WIDGETS_DIGITAL_ACCESS_LEARN_HOW".localized()
        
        static func upcomingBooking(for amenityName: String, on date: String) -> String {
            String(format: "WIDGETS_AMENITY_UPCOMING_BOOKING".localized(), amenityName, date)
        }
        
        static func posted(by name: String) -> String {
            String(format: "WIDGETS_MARKETPLACE_POSTED_BY".localized(), name)
        }
        
        static func greeting(for name: String) -> String {
            String(format: "WIDGETS_WELCOME_HOME_GREETING".localized(), name)
        }
        
        static func remaining(_ daysToMoveIn: Int64) -> String {
            let days = String.localizedStringWithFormat("PAYMENTS_NOTIFICATION_DAYS".localized(), Int(daysToMoveIn))
            
            return String(format: "WIDGETS_WELCOME_HOME_DAYS_UNTIL_MOVIN".localized(), days)
        }
        
        static func suite(_ number: String) -> String {
            String(format: "WIDGETS_WELCOME_HOME_SUITE_NUMBER".localized(), number)
        }
        
        static let guestAccessWidgetTitle = "GUEST_ACCESS_WIDGET_TITLE".localized()
        static let guestAccessWidgetOtherSuites = "GUEST_ACCESS_WIDGET_OTHER_SUITES".localized()
        static let guestAccessStartDate = "GUEST_ACCESS_START_DATE_SECTION".localized()
        static let guestAccessEndDate = "GUEST_ACCESS_END_DATE_SECTION".localized()
        static let guestAccessDayOfWeekFormat = "EEEE"
        static let guestAccessLimitDateFormat = "MMM d, h:mm a"
        static let guestAccessLimitDateShortFormat = "MMM d"
        static let guestAccessNoEnd = "GUEST_ACCESS_NO_END_DATE".localized()
     
        static let guestAccessWeeklyAccessFormat = "\("GUEST_ACCESS_WEEKLY_ACCESS".localized()): %@"
        static let guestAccessContinuousAccess = "WIDGETS_GUEST_ACCESS_CONTINUOUS_ACCESS".localized()
        static let guestAccessDailyAccess = "GUEST_ACCESS_DAILY_ACCESS".localized()
        static let guestAccessAddSuite = "WIDGETS_GUEST_ACCESS_ADD_SUITE".localized()
        static let guestAccessNoAccessTitle = "WIDGETS_GUEST_ACCESS_NO_ACCESS_TITLE".localized()
        static let guestAccessNoAccessSubtitle = "WIDGETS_GUEST_ACCESS_NO_ACCESS_SUBTITLE".localized()
        static let guestAccessErrorTitle = "WIDGETS_GUEST_ACCESS_ERROR_TITLE".localized()
        static let guestAccessErrorSubtitle = "WIDGETS_GUEST_ACCESS_ERROR_SUBTITLE".localized()
        static let guestAccessRefreshButton = "PAYMENTS_REFRESH_NOW".localized()
        static let guestAccessDoorsUnavailable = "GUEST_ACCESS_DOORS_UNAVAILABLE".localized()
        
        static func received(on date: Date) -> String {
            String(
                format: "WIDGETS_ACTIVITY_FEED_RECEIVED_ON".localized(),
                date.toFormat(
                    "MMM d, yyyy, hh:mm a",
                    timeZone: .current,
                    showZeros: true
                )
            )
        }
        
        static func read(on date: Date) -> String {
            String(
                format: "WIDGETS_ACTIVITY_FEED_READ_ON".localized(),
                date.toFormat(
                    "MMM d, yyyy, hh:mm a",
                    timeZone: .current,
                    showZeros: true
                )
            )
        }
        
        static func markAllAsReadPopUpTitle(totalUnread: Int) -> String {
            String(format: "WIDGETS_ACTIVITY_FEED_MARK_ALL_AS_READ_POP_UP".localized(), String(totalUnread))
        }
        
        static func switchSuiteToast(suiteNumber: String) -> String {
            String(format: "GUEST_ACCESS_CHANGE_SUITE_TOAST".localized(), suiteNumber)
        }

        static let learnHowURL = "https://help.1valet.com/en/articles/8482367-home-screen-widgets"
        
        static let notificationsDisabledBanner = "WIDGETS_WELCOME_HOME_NOTIFICATION_BANNER".localized()
        
        static let discoverTitle = "DISCOVER_HOME".localized()
        static let marketplaceShopsRecommendationToast = "MARKETPLACE_SHOPS_RECOMMENDATION_TOAST".localized()
        static let marketplaceBannedDialog = "MARKETPLACE_COMMUNITY_BANNED".localized()
        
        // Toast
        static func codeCopiedToast(code: String) -> String {
            String(format: "GUEST_ACCESS_CODE_COPIED_TOAST".localized(), code)
        }

    }
    
    enum Sizes {
        static let extraSmallPadding: CGFloat = Spacing.EXTRA_SMALL
        static let smallPadding: CGFloat = Spacing.SMALL
        static let mediumPadding: CGFloat = Spacing.MEDIUM
        static let mediumLargePadding: CGFloat = Spacing.MEDIUM_LARGE
        static let largePadding: CGFloat = Spacing.LARGE
        static let extraLargePadding: CGFloat = Spacing.EXTRA_LARGE
        static let extraExtraLargePadding: CGFloat = Spacing.EXTRA_EXTRA_LARGE
        
        static let baseCornerRadius: CGFloat = Dimensions.MEDIUM_ROUNDED_CORNER
        static let smallCornerRadius: CGFloat = Dimensions.SMALL_ROUNDED_CORNER
        static let largeCornerRadius: CGFloat = Dimensions.LARGE_ROUNDED_CORNER
        
        static let dividerWidth: CGFloat = Dimensions.GENERIC_DIVIDER_WIDTH
        
        static let cardShadowRadius: CGFloat = 8
        static let widgetActionButtonIconSize: CGFloat = 16
        
        static let amenityViewMoreIconSize: CGFloat = 48
        static let amenityViewMoreVerticalPadding: CGFloat = 12
        static let amenityItemImageWidth: CGFloat = 85
        
        static let marketplaceImageHeight: CGFloat = 109
        static let marketplaceImageWidth: CGFloat = 210
        
        static let viewMoreMarketplaceWidth: CGFloat = 168
        
        static let multipleRemoteDoorsIconSize: CGFloat = 16
        static let digitalAccessIconSize: CGFloat = 24
        static let proxCardItemLogoMaxWidth: CGFloat = 104
        static let digitalAccessBottomPadding: CGFloat = 24
        static let digitalAccessItemHeight: CGFloat = 108
        static let digitalAccessItemMaxWidth: CGFloat = 161
        
        static let welcomeHomeButtonSize: CGFloat = 64
        static let welcomeHomeButtonAreaHeight: CGFloat = 90
        static let welcomeHomeButtonIconSize: CGFloat = 30
        static let welcomeHomeGuestAccessBadgeIconSize: CGFloat = 10
        static let welcomeHomeGuestAccessBadgeAreaSize: CGFloat = 20
        static let welcomeHomeBadgeOffset: CGFloat = 22
        static let welcomeHomeBadgeAreaSize: CGFloat = 24
        static let welcomeHomeProfileImageSize: CGFloat = 44
        static let welcomeHomeBellWidth: CGFloat = 20
        static let welcomeHomeBellHeight: CGFloat = 26
        static let welcomeHomeBellOffsetX: CGFloat = 6
        static let welcomeHomeBellOffsetY: CGFloat = -7
        static let welcomeHomeBellIndicatorSize: CGFloat = 8
        static let welcomeHomeBellIndicatorPadding: CGFloat = 2
        static let welcomeHomeSmallActionImageSize: CGFloat = 32
        static let welcomeHomeSmallActionWidth: CGFloat = 80
        static let welcomeHomeLargeActionImageSize: CGFloat = 40
        static let welcomeHomeLargeActionImageLoadingRadius: CGFloat = 50
        static let welcomeHomeLargeActionMinWidth: CGFloat = 138
        static let welcomeHomeLargeActionMaxWidth: CGFloat = 180
        
        static let noNewNotificationsIconSize: CGFloat = 48
        static let notificationIconSize: CGFloat = 20
        static let noNotificationsIconSize: CGFloat = 40
        static let noNewNotificationsSmallIconSize: CGFloat = 22
    
        static let accessInfoErrorIconSize: CGFloat = 48
        
        static let activityFeedItemHeight: CGFloat = 94
        static let activityFeedDetailsIconSize: CGFloat = 168
        static let activityFeedDetailsTopSectionHeight: CGFloat = 266
        static let activityFeedDetailsDateIconSize: CGFloat = 24

    }
    
    enum Limits {
        static let maxAmenityItems = 5
        static let maxAmenityBookingItems = 3
        static let amenityTitleMaxLines = 2
        static let amenityTitleMaxWidth: CGFloat = 160
        
        static let postTitleMaxLines = 1
        static let maxPostItems = 8
        static let oldestPost = 90
        
        static let maxRemoteDoorsWithProxCard = 1
        static let maxRemoteDoorsWithoutProxCard = 2
        static let maxItemsMultiRemoteUnlock = 2
        static let multiRemoteUnlockItemTitleMaxLines = 2
        
        static let noNotificationsSubtitleLineLimit = 2
        static let notificationTitleLineLimit = 2
        static let notificationDateLineLimit = 1
        
        static func maxNotifications(if canUseExtraSpace: Bool) -> Int {
            canUseExtraSpace ? 5 : 3
        }
    }
    
    enum ImageNames {
        static let chevron = "chevron_right"
        static let calendar = "calendar"
        static let arrow = "arrow_right"
        static let check = "icon_check"
        static let guestSuitePicture = "guest_suite_amenity"
        static let guestParkingPicture = "guest_parking_amenity"
        static let meetingRoomPicture = "meeting_room_amenity"
        static let recreationPicture = "recreation_amenity"
        static let partyRoomPicture = "party_room_amenity"
        static let elevatorPicture = "elevator_amenity"
        static let laundryPicture = "laundry_amenity"
        static let pooPicture = "pool_amenity"
        static let gymPicture = "gym_amenity"
        static let moviePicture = "movie_theatre_amenity"
        static let barbecuePicture = "barbecue_amenity"
        static let insurance = "insurance_picture"
        static let wireless = "wireless"
        static let lock = "lock_filled"
        static let proxKeyWithoutImage = "image_logo_light_always"
        static let maintenance = "maintenance"
        static let message = "compose"
        static let payment = "action_payment"
        static let paymentPending = "action_pending"
        static let paymentAutoPay = "action_autopay"
        static let pushPin = "push_pin"
        static let user = "residents"
        static let doorSensorOff = "action_sensor_off"
        static let doorSensorOn = "action_sensor_on"
        static let doorSensorTriggered = "action_sensor_alert"
        static let heating = "action_heating"
        static let heatingIdle = "action_heating-idle"
        static let cooling = "action_cooling"
        static let coolingIdle = "action_cooling-idle"
        static let autoHeating = "action_auto_heating"
        static let badge_auto_cooling = "action_auto_cooling"
        static let autoIdle = "action_auto_idle"
        static let hvacOff = "action_hvac_off"
        static let bell = "bell"
        static let parcelNotification = "parcel_notification"
        static let checkCircle = "check_circle"
        static let close = "close"
        static let coffee = "coffee"
        static let clock = "clock"
        static let building = "building"
        static let alert = "alert_circle"
        static let amenities = "amenities"
        static let calendarAlert = "calendar_alert"
        static let calendarOff = "calendar_off"
        static let calendarMini = "calendar"
        static let call = "call"
        static let conversations = "conversations"
        static let docbox = "docbox"
        static let maintenanceMini = "maintenance"
        static let openedDoor = "opened_door"
        static let megaphone = "megaphone"
        static let mailOpen = "mail_open"
        static let slash = "slash_icon"
        static let marketplace = "marketplace"
        static let inspection = "suite_inspections"
        static let refresh = "refresh"
        static let stars = "stars"
        static let signature = "signature"
        static let notificationBellOff = "notification_off"
        static let cart = "cart"
    }
}
