//
//  MarketplaceWidgetView.swift
//  1VALET
//
//  Created by <PERSON> on 09/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import UtilitiesKit

struct MarketplaceWidgetView: View {
    // MARK: - Typealias
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    private typealias ImageNames = HomeWidgetsConstants.ImageNames
    private typealias Limits = HomeWidgetsConstants.Limits
    
    // MARK: - View Model
    
    @SwiftUI.Environment(\.residentType) private var residentType
    @ObservedViewModel var viewModel: MarketplaceWidgetViewModelProtocol
    @State private var shouldShowWidget: Bool = true
    @State var destination: MarketplaceWidgetDestination?
    @State private var reloadView: Bool = false
    @State var itemSize: CGSize = .zero
    
    @Binding var popUp: PopUpAlert?
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text(Strings.marketplaceTitle)
                .font(Fonts.sectionTitle)
                .foregroundColor(Colors.baseText)
                .padding(.horizontal, Sizes.largePadding)
            
            CarouselView {
                HStack(alignment: .bottom, spacing: Sizes.mediumPadding) {
                    if residentType != .currentResident, !viewModel.services.isEmpty {
                        VStack(alignment: .leading, spacing: Sizes.smallPadding) {
                            Text(Strings.offers)
                                .font(Fonts.marketplaceSectionTitle)
                                .foregroundColor(Colors.baseText)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                
                            HStack(alignment: .bottom, spacing: Sizes.mediumPadding) {
                                ForEach(viewModel.services) { service in
                                    MarketplaceWidgetItemView(item: service, isLoading: viewModel.isLoading)
                                        .disabled(viewModel.isLoading)
                                }
                            }
                        }
                        
                        if CapabilityUtils.hasMarketplaceCommunityAccess() {
                            VStack(alignment: .leading, spacing: Sizes.smallPadding) {
                                Text(Strings.myCommunityTitle)
                                    .font(Fonts.marketplaceSectionTitle)
                                    .foregroundColor(Colors.baseText)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                
                                HStack(alignment: .bottom, spacing: Sizes.mediumPadding) {
                                    myComunitySection
                                }
                            }
                        }
                    } else {
                        myComunitySection
                    }
                }
                .padding(.horizontal, Sizes.largePadding)
                .padding(.vertical, Sizes.mediumPadding)
            }
        }
        .onAppear {
            viewModel.loadPosts(residentType: residentType)
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeView.rawValue))) { _ in
            viewModel.loadPosts(residentType: residentType)
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeViewUI.rawValue))) { _ in
            reloadView.toggle()
        }
        .id(reloadView)
        .onChange(of: residentType) { type in
            viewModel.loadPosts(residentType: type)
        }
        .hideFromParent(shouldHide: !shouldShowWidget)
        .onChange(of: viewModel.shouldShowWidget) { shouldShowWidget = $0 }
        .navigationDestination(for: $destination) { destination in
            switch destination {
            case .viewMore:
                MarketplaceCommunityView()
            case .createPost:
                CreateMarketplacePostView(viewModel: CreatePostViewModel(isFromHomeScreen: true))
            case .policy(let url):
                CommunityBulletinBoardPolicyView(policyUrl: url, isFromHomeScreen: true)
            }
        }
    }
}

private extension MarketplaceWidgetView {
    var myComunitySection: some View {
        Group {
            if !viewModel.posts.isEmpty {
                ChildSizeReader(size: $itemSize) {
                    HStack(alignment: .bottom, spacing: Sizes.mediumPadding) {
                        ForEach(Array(viewModel.posts.prefix(Limits.maxPostItems))) { post in
                            MarketplaceWidgetItemView(item: post, isLoading: viewModel.isLoading)
                                .disabled(viewModel.isLoading)
                        }
                    }
                }
            }
            
            if viewModel.showPrompt || viewModel.posts.isEmpty {
                createPost
            }
            
            if !viewModel.posts.isEmpty {
                ZStack(alignment: .center) {
                    VStack(alignment: .center, spacing: 0) {
                        Image(ImageNames.chevron)
                            .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
                            .foregroundColor(Colors.icon)
                            .frame(width: Sizes.amenityViewMoreIconSize, height: Sizes.amenityViewMoreIconSize)
                        
                        Text(Strings.viewMore)
                            .font(Fonts.amenityViewMoreCard)
                            .foregroundColor(Colors.baseText)
                    }
                }
                .frame(width: Sizes.viewMoreMarketplaceWidth,
                       height: viewModel.posts.isEmpty ? 174 : itemSize.height)
                .cardify() {
                    AnalyticsUtil.sendEvent(
                        event: .buttonPressed(
                            action: AnalyticsHomeAction.marketplaceViewMore,
                            screen: .home
                        )
                    )
                    
                    destination = .viewMore
                }
            }
        }
    }
    
    var createPost: some View {
        VStack(alignment: .leading, spacing: Sizes.smallPadding) {
            Text(Strings.connectWithCommunity)
                .font(Fonts.myBookingTitle)
            
            Text(Strings.freeSomeSpace)
                .font(Fonts.freeSomeSpace)
            
            Spacer()
            
            HStack {
                Spacer()
                
                WidgetActionButton(title: Strings.postItem, style: .noBackground, iconType: .arrow)
                    .disabled(viewModel.isLoading)
            }
            .padding(.top, Sizes.smallPadding)
        }
        .foregroundColor(Colors.myBookingTitle)
        .padding(.horizontal, Sizes.mediumPadding)
        .padding(.vertical, Sizes.mediumLargePadding)
        .frame(width: UIScreen.main.bounds.size.width - (Sizes.largePadding * 2),
               height: viewModel.posts.isEmpty ? 174 : itemSize.height)
        .cardify(backgroundColor: Colors.marketplaceCreatePostBackground) {
            AnalyticsUtil.sendEvent(
                event: .buttonPressed(
                    action: AnalyticsHomeAction.marketplacePrompt,
                    screen: .home
                )
            )
            AppState.sharedInstance.homeLoading = true
            Task {
                do {
                    let response = try await CommunityBulletinBoardStatus.get()
                    if response.isUserBanned {
                        popUp = PopUpAlert(title: Strings.marketplaceBannedDialog, type: .ok)
                    } else {
                        if let policyUrl = response.policyUrl {
                            destination = .policy(policyUrl)
                        } else {
                            destination = .createPost
                        }
                    }
                } catch {
                    popUp = .genericError(error: error)
                }
                AppState.sharedInstance.homeLoading = false
            }
            
        }
    }
}
