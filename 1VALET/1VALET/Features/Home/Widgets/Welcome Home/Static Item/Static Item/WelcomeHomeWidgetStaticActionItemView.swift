//
//  WelcomeHomeWidgetActionItemView.swift
//  1VALET
//
//  Created by <PERSON> on 15/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import UtilitiesKit
import Combine

struct WelcomeHomeWidgetStaticActionItemView: View {
    // MARK: - Typealias
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    
    private(set) var action: WelcomeHomeAction
    @Binding var destination: WelcomeHomeDestination?
    @ObservedViewModel var viewModel: WelcomeHomeWidgetStaticActionItemViewModelProtocol
    var showOneTimeAccessCodeToast: Binding<Bool>?
    
    @State private var badge: String?
    private let feedbackGenerator: UIImpactFeedbackGenerator = UIImpactFeedbackGenerator()
    
    private var parcelNotification: AnyPublisher<String, NotificationCenter.Publisher.Failure> {
        NotificationCenter.default
            .publisher(for: Notification.Name(NotificationName.notificationMessageReceived.rawValue))
            .compactMap { $0.userInfo?[NotificationConstants.EVENT_KEY] as? String }
            .filter {
                action == .parcels &&
                [
                    NotificationEvent.parcelDeposit.rawValue,
                    NotificationEvent.parcelRoomPickup.rawValue,
                    NotificationEvent.parcelPickup.rawValue
                ].contains($0)
            }
            .eraseToAnyPublisher()
    }
    
    var body: some View {
        ZStack(alignment: .topTrailing) {
            VStack(alignment: .center, spacing: 0) {
                Image(viewModel.iconName)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(Colors.welcomeHomeItemIcon)
                    .frame(width: Sizes.welcomeHomeSmallActionImageSize, height: Sizes.welcomeHomeSmallActionImageSize)
                
                Text(viewModel.title)
                    .lineLimit(1)
                    .font(Fonts.welcomeHomeTitle)
                    .foregroundColor(Colors.baseText)
            }
            .padding(Sizes.extraSmallPadding)
            .frame(width: Sizes.welcomeHomeSmallActionWidth)
            
            if let badge = badge {
                ZStack(alignment: .center) {
                    Text(badge)
                        .foregroundColor(Colors.welcomeHomeBadge)
                        .font(Fonts.welcomeHomeBadge)
                }
                .frame(width: 20, height: 20)
                .background(Colors.readDot)
                .clipShape(Circle())
                .padding(Sizes.extraSmallPadding)
            }
        }
        .onAppear {
            updateBadge()
        }
        .onReceive(parcelNotification) { _ in
            updateBadge()
        }
        .cardify(action: {
            AnalyticsUtil.sendEvent(
                event: .buttonPressed(
                    action: AnalyticsHomeAction.quickAction(action: action),
                    screen: .home
                )
            )
            
            Task {
                AppState.sharedInstance.homeLoading = (viewModel.item == .sendMessage) || (viewModel.item == .postItem)
                
                let action = await viewModel.action()
                
                AppState.sharedInstance.homeLoading = false
                
                DispatchQueue.main.async {
                    destination = action
                }
            }
        })
        .onLongPressGesture {
            if action == .guestAccess {
                feedbackGenerator.impactOccurred()
                getOneTimeAccessCode()
            }
        }
    }
}

private extension WelcomeHomeWidgetStaticActionItemView {
    func updateBadge() {
        Task {
            badge = await viewModel.getBadge()
        }
    }
    
    func getOneTimeAccessCode() {
        Task {
            do {
                guard let deviceIds = GuestAccessUtils.guestAccessDevices?.guestAccessDevices.filter({ $0.allowedFlows == .accessCode }).map({ $0.id })
                else { return }
                
                let oneTimeCode = try await GuestAccessOneTimeCode.get(deviceIds: deviceIds)
                
                UIPasteboard.general.string = oneTimeCode?.oneTimeAccessCode
                // If we use UIPasteboard.general.string as a value, the app will ask the user if they will allow Paste, and if they don't then too bad. So we're creating an AppState value
                AppState.sharedInstance.oneTimeAccessCode = oneTimeCode?.oneTimeAccessCode
                DispatchQueue.main.async {
                    showOneTimeAccessCodeToast?.wrappedValue = true
                }
                
            } catch {
                
            }
            
        }
    }
}
