//
//  WelcomeHomeWidgetStaticActionItemViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 10/04/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import UtilitiesKit
import Combine
import InjectionKit
import UIKit
import SwiftUI

class WelcomeHomeWidgetStaticActionItemViewModel: ObservableObject, WelcomeHomeWidgetStaticActionItemViewModelProtocol {
    // MARK: - Typealias
    
    private typealias Strings = HomeWidgetsConstants.Strings
    
    // MARK: - Public Properties
    var iconName: String
    var title: String
    
    // MARK: - Injected Properties
    
    var item: WelcomeHomeAction
    @Injected private var twoWayMessagingService: TwoWayMessagingRepositoryProtocol
    @Injected private var notificationRepository: NotificationsRepository
    @Injected private var storeRepository: BuildingStoreRepositoryProtocol
    
    // MARK: - Private Properties
    
    private var cancellables: [AnyCancellable] = [AnyCancellable]()
    private var popUp: Binding<PopUpAlert?>
    
    // MARK: - Initializer
    
    init(action: WelcomeHomeAction, popUp: Binding<PopUpAlert?>) {
        self.item = action
        self.iconName = action.icon
        self.title = action.title
        self.popUp = popUp
    }
    
    // MARK: - Public Methods
    
    func action() async -> WelcomeHomeDestination? {
        switch self.item {
        case .sendMessage:
            return await fetchMessagingConfiguration()
        case .startRequest:
            return .startMaintenanceRequest
        case .postItem:
            return await fetchCommunityBulletinBoardConfiguration()
        case .guestAccess:
            return .guestAccess
        case .parcels:
            return .parcels
        case .storeOrders:
            return .myOrders
        default:
            return nil
        }
    }
}

// MARK: - Door Sensor
private extension WelcomeHomeWidgetStaticActionItemViewModel {
    private func fetchCommunityBulletinBoardConfiguration() async -> WelcomeHomeDestination? {
        do {
            let response = try await CommunityBulletinBoardStatus.get()
            if response.isUserBanned {
                let okButton = PopUpAlertButton(title: "OK".localized(), action: {
                    AppState.sharedInstance.homeLoading = false
                })
                popUp.wrappedValue = PopUpAlert(title: Strings.marketplaceBannedDialog, buttons: [okButton])
                return nil
            } else {
                if let policyUrl = response.policyUrl {
                    return .communityBulletinBoardPolicy(policyUrl)
                } else {
                    return .postMarketplace
                }
            }
        } catch {
            popUp.wrappedValue = .genericError(error: error, action: {
                AppState.sharedInstance.homeLoading = false
            })
            return nil
        }
    }
    
    private func fetchMessagingConfiguration() async -> WelcomeHomeDestination? {
        do {
            let response = try await conversationConfigurationPublisher().async()
            
            Logger.print("\(#function) success: \(response)")
            
            if let canStartConversation = response.canResidentCreateNewConversations,
                !canStartConversation,
               let preferredMethodType = response.preferredMethodOfCommunicationType,
               let preferredMethod = response.preferredMethodOfCommunication {
                
                var preferredMethodURL: URL?
                if preferredMethodType == .phone {
                    preferredMethodURL = URL(string: "tel://\(preferredMethod)")
                } else if preferredMethodType == .email {
                    preferredMethodURL = URL(string: "mailto:\(preferredMethod)")
                }
                
                return .sendMessage(preferredMethod, preferredMethodURL)
            }
            
            return .sendMessage(nil, nil)
        } catch {
            Logger.print("error: \(error)")
            
            return nil
        }
    }
}

// MARK: - Parcels
extension WelcomeHomeWidgetStaticActionItemViewModel {
    func getBadge() async -> String? {
        switch item {
        case .parcels:
            return await getParcelsBadge()
        case .storeOrders:
            return await getMyOrdersBadge()
        default:
            return nil
        }
    }
    
    func getMyOrdersBadge() async -> String? {
        if let parcelsCount = try? await getMyOrdersPublisher().async().count,
           parcelsCount > 0 {
            return String(parcelsCount)
        }
        
        return nil
    }
    
    func getParcelsBadge() async -> String? {
        do {
            if let parcelsCount = try await getParcelsPublisher().async().count,
               parcelsCount > 0 {
                return String(parcelsCount)
            }
        } catch {
            Logger.print("error: \(error)")
            ExceptionLoggingUtils.report(error)
        }
        
        return nil
    }
}

private extension WelcomeHomeWidgetStaticActionItemViewModel {
    func conversationConfigurationPublisher() -> AnyPublisher<TwoWayMessagingSettingsResponse, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.settings(buildingID: buildingId)
    }
    
    func getParcelsPublisher() -> AnyPublisher<ParcelCountResponse, Swift.Error> {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        return notificationRepository.hasPendingParcelNotifications(suiteId: suiteId)
    }
    
    func getMyOrdersPublisher() -> AnyPublisher<[BuildingStoreOrderResponse], Swift.Error> {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        return storeRepository.getOrdersInProgress(suiteId: suiteId)
    }
}
