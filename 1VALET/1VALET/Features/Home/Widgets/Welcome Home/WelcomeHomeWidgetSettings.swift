//
//  WelcomeHomeWidgetSettings.swift
//  1VALET
//
//  Created by <PERSON> on 16/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import UtilitiesKit
import InjectionKit
import Combine

class WelcomeHomeWidgetSettings: ObservableObject {
    static var sharedInstance = WelcomeHomeWidgetSettings()
    
    @Published var unreadItems: [ActivityFeedItem] = []
    @Published var totalUnreadItems: Int = 0
    @Published var reloadItems: Bool = false
    @Published var canShowGuestAccess: Bool = true
    
    var showNotificationBellIndicator: Bool {
        guard !unreadItems.isEmpty,
              let firstId = unreadItems.first?.id,
              firstId != "placeholder" else { return false }
        
        return lastSeenItem != firstId
    }
    
    @Published var lastSeenItem = UserDefaults.standard.string(forKey: UserDefaultsConstants.LAST_ACTIVITY_FEED_ITEM_SEEN_ID) {
        didSet {
            UserDefaults.standard.set(lastSeenItem, forKey: UserDefaultsConstants.LAST_ACTIVITY_FEED_ITEM_SEEN_ID)
        }
    }
}

enum WelcomeHomeAction: Hashable {
    // MARK: - Typealias
    
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias ImageNames = HomeWidgetsConstants.ImageNames
    
    // Hardware Actions
    case doorSensor
    case thermostats(String)
    
    // Software Actions
    case parcels
    case payments
    case sendMessage
    case startRequest
    case postItem
    case guestAccess
    case storeOrders
    
    var icon: String {
        switch self {
        case .sendMessage:
            return ImageNames.message
        case .startRequest:
            return ImageNames.maintenance
        case .postItem:
            return ImageNames.pushPin
        case .guestAccess:
            return ImageNames.user
        case .parcels:
            return ImageNames.parcelNotification
        case .storeOrders:
            return ImageNames.cart
        default:
            return ""
        }
    }
    
    var title: String {
        switch self {
        case .sendMessage:
            return Strings.sendMessage
        case .startRequest:
            return Strings.startRequest
        case .postItem:
            return Strings.postItemLabel
        case .guestAccess:
            return Strings.guestAccess
        case .parcels:
            return Strings.deliveries
        case .storeOrders:
            return Strings.myOrders
        default:
            return ""
        }
    }
    
    enum ActionSize {
        case large
        case small
    }
    
    var actionButtonSize: Self.ActionSize {
        switch self {
        case .doorSensor, .thermostats, .payments:
            return .large
        case .sendMessage, .startRequest, .postItem, .guestAccess, .parcels, .storeOrders:
            return .small
        }
    }
}

enum WelcomeHomeDestination {
    case viewAllNotifications
    case profile
    
    // Static actions
    case sendMessage(String?, URL?)
    case startMaintenanceRequest
    case postMarketplace
    case communityBulletinBoardPolicy(String)
    case guestAccess
    case parcels
    case myOrders
    
    // Dynamic actions
    case initialSetup
    case enterPinCode(Bool)
    
    // Thermostat
    case thermostats(ThermostatStatusResponse)
    
    // Software
    case payments
}
