//
//  WelcomeHomeWidgetView.swift
//  1VALET
//
//  Created by <PERSON> on 15/02/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import NetworkKit
import UtilitiesKit

struct WelcomeHomeWidgetView: View {
    // MARK: - Typealias
    
    private typealias Colors = HomeWidgetsConstants.Colors
    private typealias Strings = HomeWidgetsConstants.Strings
    private typealias Sizes = HomeWidgetsConstants.Sizes
    private typealias Fonts = HomeWidgetsConstants.Fonts
    private typealias ImageNames = HomeWidgetsConstants.ImageNames
    private typealias Limits = HomeWidgetsConstants.Limits

    // MARK: - Private Properties
    
    @StateObject private var welcomeHomeSettings = WelcomeHomeWidgetSettings.sharedInstance
    
    // MARK: - State
    
    @SwiftUI.Environment(\.residentType) private var residentType
    @Binding var popUp: PopUpAlert?
    @State var actions: [WelcomeHomeAction] = []
    @State var destination: WelcomeHomeDestination?
    var showOneTimeAccessCodeToast: Binding<Bool>?
    
    @State private var suiteNumber: String = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.number ?? ""
    @StateObject private var feedbackState: FeedbackState = .sharedInstance
    @Binding var notificationsAuthorized: Bool
    @State private var reloadView: Bool = false
    
    // MARK: - Public Properties
    
    var body: some View {
        VStack(alignment: .leading, spacing: Sizes.largePadding) {
            HStack(alignment: .top, spacing: Sizes.mediumPadding) {
                profileImage
                
                ZStack(alignment: .topTrailing) {
                    VStack(alignment: .leading, spacing: 0) {
                        Text(Strings.greeting(for: firstName))
                            .font(Fonts.sectionTitle)
                            .foregroundColor(Colors.baseText)
                            .padding(.top, 2)
                            .lineLimit(2)
                            .fixedSize(horizontal: false, vertical: true)
                        
                        if residentType == .futureResident {
                            Text(daysToMoveIn)
                                .font(Fonts.welcomeHomeFutureResidentMessage)
                                .foregroundColor(Colors.baseText)
                                .lineLimit(1)
                                .fixedSize(horizontal: false, vertical: true)
                        }
                        if !suiteNumber.isEmpty {
                            Text(Strings.suite(suiteNumber))
                                .font(Fonts.welcomeHomeSuiteNumberLabel)
                                .foregroundColor(Colors.baseText)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .onTapGesture {
                        destination = .profile
                    }
                    .padding(.trailing, Sizes.mediumPadding + Sizes.welcomeHomeBellWidth)
                    
                    if residentType != .guest {
                        Button(action: {
                            AnalyticsUtil.sendEvent(
                                event: .buttonPressed(
                                    action: AnalyticsHomeAction.activityFeedBell,
                                    screen: .home
                                )
                            )
                            
                            destination = .viewAllNotifications
                        }) {
                            Image(ImageNames.bell)
                                .renderingMode(.template)
                                .resizable()
                                .scaledToFit()
                                .foregroundColor(Colors.icon)
                                .frame(width: Sizes.welcomeHomeBellWidth, height: Sizes.welcomeHomeBellHeight)
                                .overlay(bellIconIndicator.offset(x: Sizes.welcomeHomeBellOffsetX, y: Sizes.welcomeHomeBellOffsetY))
                        }
                    }
                }
            }
            .padding(.horizontal, Sizes.largePadding)
            
            if !actions.isEmpty && residentType != .guest {
                CarouselView {
                    HStack(alignment: .top, spacing: Sizes.smallPadding) {
                        ForEach(actions, id: \.self) { action in
                            button(for: action)
                        }
                    }
                    .padding(.horizontal, Sizes.largePadding)
                    .padding(.vertical, Sizes.mediumPadding)
                    .animation(.none)
                }
            }
            
            if feedbackState.showHomeBanner {
                FeedbackRatingView(type: .card, popUp: $popUp)
                    .padding(.horizontal, Sizes.largePadding)
                    .transition(.asymmetric(insertion: .identity, removal: .move(edge: .leading).combined(with: .opacity)))
            }
            
            if !notificationsAuthorized {          
                InfoCardView(
                    title: "",
                    message: Strings.notificationsDisabledBanner,
                    icon: ImageNames.notificationBellOff,
                    iconBackgroundColor: .clear,
                    iconForegroundColor: Color.errorWarning,
                    cardAction: {
                        OpenAppUtils.openNotificationSettings()
                    }
                )
                .padding(.horizontal, Sizes.largePadding)
            }
        }
        .onAppear {
            setupActions()
            NotificationUtils.sharedInstance.getAuthorizationStatus { authorized in
                notificationsAuthorized = authorized
            }
            FeedbackState.shouldShowFeedback(type: .general)
        }
        .onReceive(NotificationCenter.default.publisher(for: UIScene.willEnterForegroundNotification)) { _ in
            NotificationUtils.sharedInstance.getAuthorizationStatus { authorized in
                notificationsAuthorized = authorized
            }
        }        
        .onChange(of: welcomeHomeSettings.canShowGuestAccess) { canShowGuestAccess in
            if (canShowGuestAccess && !actions.contains(.guestAccess) && CapabilityUtils.hasGuestAccess())
                || (!canShowGuestAccess && actions.contains(.guestAccess)) {
                setupActions()
            }
        }
        .onReceive(InsuiteUtils.sharedInstance.$insuiteDetails.removeDuplicates()) { insuiteDetails in
            if insuiteDetails != nil {
                setupActions()
            }
        }
        .onReceive(GuestAccessUtils.sharedInstance.$newGuestAccessDevices.removeDuplicates()) { _ in
            setupActions()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeView.rawValue))) { _ in
            setupActions()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(NotificationName.reloadHomeViewUI.rawValue))) { _ in
            reloadView.toggle()
        }
        .id(reloadView)
        .onReceive(ThermostatUtils.sharedInstance.$localThermostats) { _ in
            setupActions()
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name(BroadcastConstants.THERMOSTAT_UPDATED))) { notification in
                 
            if let newData = notification.userInfo?[BroadcastConstants.THERMOSTAT_UPDATED_DATA] as? ThermostatData,
               var localThermostats = ThermostatUtils.sharedInstance.localThermostats,
               let index = localThermostats.firstIndex(where: { stat in
                   stat.thermostatId == newData.thermostatId
               }) {
                
                // Update our current local thermostat data with this new data
                localThermostats[index].isConnected = newData.isOn
                if localThermostats[index].hvacMode == .heat || localThermostats[index].hvacMode == .auxiliary {
                    localThermostats[index].heatingSetpoint = newData.setPointF
                } else if localThermostats[index].hvacMode == .cool {
                    localThermostats[index].coolingSetpoint = newData.setPointF
                }
                
                localThermostats[index].currentTemperature = newData.roomTemperatureF
                localThermostats[index].hvacMode = newData.hvacMode
                localThermostats[index].isCooling = newData.isCooling
                localThermostats[index].isHeating = newData.isHeating
                
                ThermostatUtils.sharedInstance.localThermostats = localThermostats
                setupActions()
            }
        }
        .onReceive(UserUtils.sharedInstance.$loggedInUser) { _ in
            suiteNumber = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.number ?? ""
        }
        .navigationDestination(for: $destination) { destination in
            switch destination {
            case .viewAllNotifications:
                NavigationLazyView(
                    ActivityFeedWidgetViewAllView(
                        unreadItems: welcomeHomeSettings.unreadItems,
                        totalUnread: welcomeHomeSettings.totalUnreadItems
                    )
                )
            case .profile:
                AccountSettingsView()
            case .sendMessage(let preferredMethod, let preferredMethodURL):
                if let preferredMethod = preferredMethod {
                    NavigationLazyView(StartConversationBlockedView(
                        preferredMethod: preferredMethod,
                        prefferedMethodUrl: preferredMethodURL
                    ))
                } else {
                    ComposeMessageView()
                }
            case .startMaintenanceRequest:
                MaintenanceRequestNewViewControllerRepresentable(ticketId: nil)
                    .edgesIgnoringSafeArea(.all)
                    .navigationBarHidden(true)
            case .postMarketplace:
                CreateMarketplacePostView(viewModel: CreatePostViewModel(isFromHomeScreen: true))
            case .guestAccess:
                GuestAccessHomeView()
            case .parcels:
                ParcelsView()
            case .initialSetup:
                DoorSensorSetupViewControllerRepresentable()
                    .edgesIgnoringSafeArea(.all)
                    .navigationBarHidden(true)
            case .enterPinCode(let triggered):
                DoorSensorDisarmViewControllerRepresentable(triggered: triggered)
                    .edgesIgnoringSafeArea(.all)
                    .navigationBarHidden(true)
            case .thermostats(let thermostat):
                ThermostatTempControlViewControllerRepresentable(thermostat: thermostat)
                    .edgesIgnoringSafeArea(.all)
                    .navigationBarHidden(true)
            case .payments:
                PaymentsRouterView()
            case .communityBulletinBoardPolicy(let url):
                CommunityBulletinBoardPolicyView(policyUrl: url, isFromHomeScreen: true)
            case .myOrders:
                BuildingStoreMyOrdersView(router: BuildingStoreRouter())
            }
        }
    }
}

extension WelcomeHomeWidgetView {
    @ViewBuilder
    func button(for action: WelcomeHomeAction) -> some View {
        if action.actionButtonSize == .large {
            switch action {
            case .doorSensor:
                WelcomeHomeWidgetDynamicActionItemView(action: action, destination: $destination, viewModel: DoorSensorDynamicActionItemViewModel())
            case .thermostats(let thermostatId):
                if let thermostat = ThermostatUtils.sharedInstance.localThermostats?.first(where: { $0.thermostatId == thermostatId }) {
                    WelcomeHomeWidgetDynamicActionItemView(action: action, destination: $destination, viewModel: ThermostatDynamicActionItemViewModel(thermostat: thermostat))
                }
            default:
                WelcomeHomeWidgetDynamicActionItemView(action: action, destination: $destination, viewModel: PaymentsDynamicActionItemViewModel())
            }
        } else {
            WelcomeHomeWidgetStaticActionItemView(
                action: action,
                destination: $destination,
                viewModel: WelcomeHomeWidgetStaticActionItemViewModel(
                    action: action,
                    popUp: $popUp
                ),
                showOneTimeAccessCodeToast: showOneTimeAccessCodeToast
            )
        }
    }
    
    @ViewBuilder
    var bellIconIndicator: some View {
        if welcomeHomeSettings.showNotificationBellIndicator {
            Circle()
                .fill(Colors.marketplaceOrange)
                .frame(width: Sizes.welcomeHomeBellIndicatorSize, height: Sizes.welcomeHomeBellIndicatorSize)
                .padding(Sizes.welcomeHomeBellIndicatorPadding)
                .background(Circle().fill(Colors.background))
        }
    }
    
    var profileImage: some View {
        Button(action: {
            AnalyticsUtil.sendEvent(
                event: .buttonPressed(
                    action: AnalyticsHomeAction.profilePicture,
                    screen: .home
                )
            )
            destination = .profile
        }) {
            ZStack(alignment: .center) {
                Text(UserUtils.getInitials())
                    .font(Fonts.bodySemiBold)
                    .foregroundColor(Colors.welcomeHomeProfileInitials)
            }
            .frame(width: Sizes.welcomeHomeProfileImageSize, height: Sizes.welcomeHomeProfileImageSize)
            .background(Colors.welcomeHomeProfileInitialsBackground)
            .clipShape(Circle())
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.top, Sizes.extraSmallPadding)
    }
}

extension WelcomeHomeWidgetView {
    var daysToMoveIn: String {
        if let timeAllowedForMoveIn = SuiteUtils.timeAllowedForMoveIn(),
           let defaultSuiteTimeZone = SuiteUtils.defaultSuiteTimeZone() {
            let currentDate = Date().changeTimeZone(to: defaultSuiteTimeZone)
            
            let days = currentDate.dateAtStartOf(.day).getInterval(toDate: timeAllowedForMoveIn.dateAtStartOf(.day), component: .day)
            
            return days > 0 ? Strings.remaining(days) : Strings.movinIsToday
        }
        return ""
    }
    
    var firstName: String {
        UserUtils.sharedInstance.loggedInUser?.profile?.firstName ?? ""
    }
    
    func setupActions() {
        var actions = [WelcomeHomeAction]()
        if !UserUtils.sharedInstance.isGuest {
            if CapabilityUtils.hasDoorSensor() {
                actions.append(.doorSensor)
            }
            
            if let thermostats = ThermostatUtils.sharedInstance.localThermostats,
               !thermostats.isEmpty,
               InsuiteUtils.sharedInstance.isCurrentResident {
                
                let thermostatsIds = thermostats.compactMap({ $0.thermostatId })
                thermostatsIds.forEach { thermostatId in
                    actions.append(.thermostats(thermostatId))
                }
                
                let subscribeThermostatIds = thermostats
                    .filter({ $0.supportsRealTimeUpdates == true })
                    .compactMap({ $0.thermostatId })
                subscribeThermostatIds.forEach { thermostatId in
                    setupThermostatStream(thermostatId: thermostatId)
                }
            }
                   
            if CapabilityUtils.hasRentPayment() {
                actions.append(.payments)
            }
            
            if CapabilityUtils.hasDeliveries() {
                actions.append(.parcels)
            }
            
            if CapabilityUtils.hasBuildingStoreAccess() {
                actions.append(.storeOrders)
            }
            
            if UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident {
                actions.append(.sendMessage)
            }
            
            if CapabilityUtils.hasTicketingService() && InsuiteUtils.sharedInstance.isCurrentResident {
                actions.append(.startRequest)
            }
            
            if CapabilityUtils.hasMarketplaceCommunityAccess() {
                actions.append(.postItem)
            }
            
           if CapabilityUtils.hasGuestAccess() &&
                UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident &&
                InsuiteUtils.sharedInstance.isCurrentResident {
                actions.append(.guestAccess)
            }
        }
        
        DispatchQueue.main.async {
            self.actions = actions
        }
    }
    
    func setupThermostatStream(thermostatId: String) {
        ThermostatConnection.sharedInstance.initializeConnection()
        ThermostatConnection.sharedInstance.thermostatConnection?.subscribeToThermostat(thermostatId: thermostatId)
    }
}
