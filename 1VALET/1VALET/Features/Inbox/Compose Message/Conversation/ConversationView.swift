//
//  ConversationView.swift
//  1VALET
//
//  Created by <PERSON> on 13/07/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import SwiftUI

struct ConversationView<ViewModel: ConversationViewModelProtocol>: View {
    // MARK: - Typealias
    private typealias Fonts = MessageConstants.Fonts
    private typealias Colors = MessageConstants.Colors
    private typealias Strings = MessageConstants.Strings
    private typealias Sizes = MessageConstants.Sizes
    private typealias Limits = MessageConstants.Limits
    private typealias ImageNames = MessageConstants.ImageNames
    
    // MARK: - ViewModel
    @ObservedObject var viewModel: ViewModel
    
    // MARK: - Properties
    @State var newMessageHeight: CGFloat = 0
    @State var message: String = ""
    @ObservedObject private var keyboard: KeyboardObserver = KeyboardObserver()
    
    var body: some View {
        VStack(spacing: 0) {
            conversationMessages
                .dismissKeyboardOnTap()
            
            if viewModel.isConversationOpen {
                newMessage
            }
        }
        .ignoreSafeArea(if: !viewModel.isConversationOpen, edges: .bottom)
        .onAppear {
            self.keyboard.addObserver()
        }
        .onDisappear {
            self.keyboard.removeObserver()
        }
        .standardNavigationStyle(viewModel.pageTitle, shouldScale: false) {
            deleteButton
        }
        .loading(show: $viewModel.isLoading)
        .popUpAlert(popUp: $viewModel.popUp)
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
            viewModel.fetchInfo()
        }
    }
}

private extension ConversationView {
    @ViewBuilder
    private var conversationMessages: some View {
        PaginatedScrollView(viewModel: viewModel.paginatedScrollViewModel,
                            reloadResults: $viewModel.shouldReload,
                            leadingSpace: 0,
                            itemsSpacing: 0,
                            footerView: { _ in footer }) { messageViewModel in
            ConversationMessageView(viewModel: messageViewModel, isConversationOpen: viewModel.isConversationOpen)
                .id(messageViewModel.id)
                .tag(messageViewModel.id)
        }
    }
    
    @ViewBuilder
    private var footer: some View {
        if !viewModel.isConversationOpen && !viewModel.isLoading {
           conversationClosed
                .padding(.vertical, Sizes.extraExtraLargePadding)
        }
    }
    
    @ViewBuilder
    private var newMessage: some View {
        VStack(spacing: 0) {
            if viewModel.isConversationOpen && viewModel.canCloseConversation && !keyboard.isShowing {
                resolveConversationButton
            }
            
            DividerView()
            
            if !viewModel.sentFiles.isEmpty {
                sentFilesView
                    .padding(.top)
            }
            
            HStack(alignment: .bottom, spacing: 0) {
                FilePickerView(uploadFile: { viewModel.send(action: .uploadFile(file: $0)) },
                               maxFiles: Limits.maxFiles,
                               currentFilesAmmount: viewModel.sentFiles.count) {
                    Image(ImageNames.attachment)
                        .renderingMode(.template)
                        .resizable()
                        .scaledToFit()
                        .foregroundColor(Color.slateGrey)
                        .frame(width: Sizes.attachmentImageSize, height: Sizes.attachmentImageSize)
                }
                .padding(.bottom, Sizes.extraLargePadding)
                
                messageView
                    .padding(.horizontal, Sizes.smallPadding)
                
                VStack(spacing: 0) {
                    sendMessageButton
                    
                    remainingCharacters
                }
            }
            .padding(.vertical)
            .padding(.horizontal, Sizes.largePadding)
        }
    }
    
    private var sentFilesView: some View {
        CarouselView {
            HStack {
                ForEach(viewModel.sentFiles, id: \.self) { sentFile in
                    FileMiniatureView(sentFile: sentFile,
                                      deleteFile: { viewModel.send(action: .deleteFile(file: $0))})
                        
                }
            }
            .padding(.horizontal, Sizes.largePadding)
        }
        .animation(.easeInOut)
    }
    
    private var messageView: some View {
        ZStack(alignment: .topLeading) {
            if viewModel.message.isEmpty {
                Text(Strings.messagePlaceholder)
                    .font(Fonts.fieldFont)
                    .foregroundColor(Colors.messagePlaceholder)
                    .padding(.horizontal, 6)
                    .padding(.top, Sizes.mediumPadding)
            }
            
            MultilineTextField(text: $message) {
                $0.tintColor = UIColor(Color.alwaysDarkBlue)
                $0.bounces = false
            }
            .padding(.vertical, Sizes.extraSmallPadding)
            .onChange(of: message) { newValue in
                viewModel.message = newValue
            }
            .onChange(of: viewModel.message) { newValue in
                if newValue.isEmpty {
                    message = newValue
                }
            }
        }
        .frame(height: Sizes.messageHeight)
        .padding(.horizontal, Sizes.smallPadding)
        .background (
            RoundedRectangle(cornerRadius: Dimensions.SMALL_ROUNDED_CORNER)
                .fill(Colors.messageBackground)
        )
        .defaultShadow()
    }
    
    private var sendMessageButton: some View {
        Button(action: {
                viewModel.send(action: .submitMessage)
            }) {
                Image(ImageNames.send)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(viewModel.isValidMessage ? Colors.validSend : Colors.invalidSend)
                    .frame(width: Sizes.sendButtonImageSize, height: Sizes.sendButtonImageSize)
            }
            .padding(.bottom, Sizes.mediumPadding)
            .disabled(!viewModel.isValidMessage)
    }
    
    private var resolveConversationButton: some View {
        Button(action: {
            viewModel.send(action: .showResolvePopUp)
            }) {
                VStack(spacing: 0) {
                    DividerView()
                    
                    HStack(spacing: 0) {
                        Spacer()
                        Text(Strings.endConversation)
                            .font(Fonts.endConversation)
                            .foregroundColor(Colors.endConversation)
                            .padding(.vertical, Sizes.mediumPadding)
                        Spacer()
                    }
                }
                
            }
    }
    
    private var remainingCharacters: some View {
        Text("\(Limits.messageMaxCharacters - viewModel.message.count)")
            .foregroundColor(Colors.remainingCharactersForeground)
            .font(Fonts.remainingCharacters)
    }
    
    private var conversationClosed: some View {
        VStack(spacing: Sizes.mediumPadding) {
            Text(Strings.conversationClosed)
                .foregroundColor(Colors.conversationClosedForeground)
                .font(Fonts.conversationClosed)
            
            Button(action: {
                viewModel.send(action: .loadConfiguration)
            }) {
                Text(Strings.startNewConversation).underline()
            }
            .buttonStyle(LinkButtonStyle())
            
            NavigationLink(isActive: $viewModel.showDestination) {
                NavigationLazyView(destination)
            } label: {
                EmptyView()
            }
        }
    }
    
    @ViewBuilder
    private var destination: some View {
        if viewModel.canStartConversation {
            ComposeMessageView()
        } else {
            StartConversationBlockedView(preferredMethod: viewModel.preferredMethod,
                                         prefferedMethodUrl: viewModel.preferredMethodURL)
        }
    }
    
    @ViewBuilder
    private var deleteButton: some View {
        if !viewModel.isConversationOpen {
            Button(action: {
                viewModel.send(action: .showDeletePopUp)
            }) {
                Image(ImageNames.trash)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .foregroundColor(Colors.deleteConversationIcon)
                    .frame(width: Sizes.deleteConversationIconSize, height: Sizes.deleteConversationIconSize)
                    .padding()
            }
        }
    }
}

struct ConversationView_Previews: PreviewProvider {
    static var previews: some View {
        ComposeMessageView()
    }
}
