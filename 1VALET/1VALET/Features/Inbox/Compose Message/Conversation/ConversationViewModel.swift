//
//  ConversationViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 13/07/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import Combine
import SwiftUI
import UtilitiesKit

class ConversationViewModel: ConversationBaseViewModel, ConversationViewModelProtocol {
    // MARK: - Typealias
    private typealias Limits = MessageConstants.Limits
    private typealias Strings = MessageConstants.Strings

    // MARK: Public Properties
    @Published var isValidMessage: Bool = false
    @Published var message: String = ""
    @Published var popUp: PopUpAlert?
    @Published var shouldReload: Bool = false
    @Published var pageTitle: String = ""
    @Published var isConversationOpen: Bool = true
    @Published var showDestination: Bool = false
    @Published var canStartConversation: Bool = false
    @Published var preferredMethod: String = ""
    @Published var preferredMethodURL: URL?
    @Published var canCloseConversation: Bool = true
    
    // MARK: Private Properties
    private var conversationId: String
    private var shouldPerformAction: ShouldPerformConversationAction?
    
    // MARK: Initializer
    init(conversationId: String) {
        self.conversationId = conversationId
        
        super.init()
        
        observeValidations()
        
        NotificationCenter.default
            .publisher(for: Notification.Name(NotificationName.notificationConversationReceived.rawValue))
            .compactMap { $0.userInfo?[NotificationConstants.CONVERSATION_ID_KEY] as? String }
            .filter { $0 == conversationId }
            .sink { [weak self] _ in
                self?.shouldReload = true
            }
            .store(in: &cancellables)
    }
    
    // MARK: Public Methods
    func send(action: ConversationActions) {
        switch action {
        case .submitMessage:
            if isValidMessage {
                addMessageToConversation()
            }
            return
        case .uploadFile(let fileData):
            shouldUpload(file: fileData)
            return
        case .deleteFile(let file):
            self.deleteFile(file: file)
            return
        case .showResolvePopUp:
            let positiveButton = PopUpAlertButton(title: Strings.endConversationPopUpPrimaryButton, action: {
                self.resolveCurrentConversation()
            })
            
            let negativeButton = PopUpAlertButton(title: Strings.endConversationPopUpSecondaryButton, style: .cancel)
            
            popUp = PopUpAlert(
                title: Strings.endConversationPopUpTitle,
                buttonArrangementLayout: .horizontal,
                buttons: [negativeButton, positiveButton]
            )
            return

        case .showDeletePopUp:
            let positiveButton = PopUpAlertButton(title: Strings.deletePopUpPrimaryButton, style: .delete, action: {
                self.deleteCurrentConversation()
            })
            
            let negativeButton = PopUpAlertButton(title: Strings.deletePopUpSecondaryButton, style: .cancel)
            
            popUp = PopUpAlert(
                title: Strings.deletePopUpTitle,
                buttonArrangementLayout: .horizontal,
                buttons: [negativeButton, positiveButton]
            )
            return

        case .loadConfiguration:
            fetchConfiguration()
        }
    }
    
    // MARK: Private Methods
    private func shouldUpload(file: FileData) {
        // Doing this way instead of && to only display the first error
        if isUnderSingleAtachmentLimit(file) {
            if isUnderAllAtachmentLimit(file) {
                self.uploadFile(file)
            } else {
                let button = PopUpAlertButton(title: Strings.attachmentsPopUpPrimaryButtonTitle)
                
                popUp = PopUpAlert(
                    title: Strings.maxSizeAllAttachmentsTitle,
                    buttonArrangementLayout: .vertical,
                    buttons: [button]
                )
            }
        } else {
            let button = PopUpAlertButton(title: Strings.attachmentsPopUpPrimaryButtonTitle)
            
            popUp = PopUpAlert(
                title: Strings.maxSizeSingleAttachmentTitle,
                buttonArrangementLayout: .horizontal,
                buttons: [button]
            )
        }
    }
    
    private func observeValidations() {
        self.allValidation.sink { _ in
            Logger.print("error:")
        } receiveValue: { [weak self] validation in
            self?.isValidMessage = validation.isSuccess
        }.store(in: &cancellables)
    }
    
    private lazy var loadingBinding: Binding<Bool> = {
        Binding<Bool>(get: { self.isLoading }, set: { self.isLoading = $0 })
    }()
    
    lazy var paginatedScrollViewModel: PaginatedScrollViewModel = {
        PaginatedScrollViewModel(isLoading: loadingBinding,
                                 isReversed: true,
                                 loadMore: { [unowned self] currentPage, isRefresh in
                                        self.allConversationInfoPublisher(currentPage: currentPage, isRefresh: isRefresh) },
                                 transformItems: { [unowned self] in self.handleResponses(response: $1) })
    }()
    
    // MARK: - Validations
    
    lazy var sentFilesValidation: ValidationPublisher = {
        $sentFiles.nonEmptyValidator(ValidationMessage(title: "", description: nil))
    }()
    
    lazy var messageValidation: ValidationPublisher = {
            $message.nonEmptyValidator(ValidationMessage(title: "", description: nil))
    }()
    
    lazy var messageLimitValidation: ValidationPublisher = {
                $message.countLimitValidator(Limits.messageMaxCharacters, ValidationMessage(title: "", description: nil))
    }()
    
    lazy var allMessageValidation: ValidationPublisher = {
        Publishers.CombineLatest(
            messageValidation,
            messageLimitValidation
            )
            .map { v1, v2 in
                return [v1, v2].allSatisfy { $0.isSuccess } ? .success : .failure(message: ValidationMessage(title: "", description: nil))
            }
            .eraseToAnyPublisher()
    }()
    
    lazy var allValidation: ValidationPublisher = {
        Publishers.CombineLatest(
            sentFilesValidation,
            allMessageValidation
            )
            .map { v1, v2 in
                return [v1, v2].filter({ $0.isSuccess }).isEmpty ? .failure(message: ValidationMessage(title: "", description: nil)) : .success
            }
            .eraseToAnyPublisher()
    }()
    
    private func createMessageViewModels(from conversation: TwoWayMessagingGetMessagesResponse) -> [ConversationMessageViewModel] {
        let messages = conversation.data?.reversed() ?? []
        return messages.reduce([ConversationMessageViewModel]()) { currentMessages, nextMessage in
            if let lastMessage = currentMessages.last {
                var currentMessages = currentMessages
                guard let lastMessageDate = Date.fromISO8601String(lastMessage.conversationMessage.createdDate ?? ""),
                      let nextMessageDate = Date.fromISO8601String(nextMessage.createdDate ?? "") else { return currentMessages }
                
                let isMoreThanFiveMinutes = nextMessageDate.minutes(from: lastMessageDate) > Limits.messageMinutesDifference
                
                currentMessages.append(ConversationMessageViewModel(conversationMessage: nextMessage, isFirstInGroup: isMoreThanFiveMinutes || lastMessage.senderBasUserId != nextMessage.senderBasUserId))
                return currentMessages
            }
            
            return [ConversationMessageViewModel(conversationMessage: nextMessage, isFirstInGroup: true)]
        }
    }
    
    private func handleResponses(response: AllConversationInfoResponse) -> [ConversationMessageViewModel] {
        if let info = response.info {
            self.isConversationOpen = info.status == .open
            self.pageTitle = info.subject ?? ""
            self.canCloseConversation = info.type != .storeOrder
        }
        
        return createMessageViewModels(from: response.messages)
    }
    
    func fetchInfo() {
        self.fetchConversationInfo()
            .receive(on: DispatchQueue.global(qos: .background))
            .sink { [weak self] completion in
            switch completion {
            case let .failure(error):
                self?.isLoading = false
                Logger.print("error: \(error)")
                ExceptionLoggingUtils.report(error)
            case .finished:
                Logger.print("\(#function) finished")
            }
        } receiveValue: { [weak self] response in
            guard let self = self else { return }
            self.isConversationOpen = response.status == .open
            self.canCloseConversation = response.type != .storeOrder
            if self.pageTitle.isEmpty {
                self.pageTitle = response.subject ?? ""
            }
            Logger.print("\(#function) success: \(response)")
        }.store(in: &cancellables)
    }
    
    private func addMessageToConversation() {
        isLoading = true
        self.addMessage().sink { [weak self] completion in
            switch completion {
            case let .failure(error):
                self?.isLoading = false
                Logger.print("error: \(error)")
                ExceptionLoggingUtils.report(error)
            case .finished:
                self?.message = ""
                self?.sentFiles = []
                self?.shouldReload = true
                Logger.print("\(#function) finished")
            }
        } receiveValue: { response in
            Logger.print("\(#function) success: \(response)")
        }.store(in: &cancellables)
    }
    
    private func resolveCurrentConversation() {
        isLoading = true
        self.resolveConversation().sink { [weak self] completion in
            self?.isLoading = false
            switch completion {
            case let .failure(error):
                Logger.print("error: \(error)")
                ExceptionLoggingUtils.report(error)
            case .finished:
                NavigationUtil.popToRootView()
                Logger.print("\(#function) finished")
            }
        } receiveValue: { response in
            Logger.print("\(#function) success: \(response)")
        }.store(in: &cancellables)
    }
    
    private func deleteCurrentConversation() {
        isLoading = true
        self.deleteConversation().sink { [weak self] completion in
            self?.isLoading = false
            switch completion {
            case let .failure(error):
                Logger.print("error: \(error)")
                ExceptionLoggingUtils.report(error)
            case .finished:
                NavigationUtil.popToRootView()
                Logger.print("\(#function) finished")
            }
        } receiveValue: { response in
            Logger.print("\(#function) success: \(response)")
        }.store(in: &cancellables)
    }
    
    private func fetchConfiguration() {
        self.isLoading = true
        self.conversationConfigurationPublisher()
            .sink { [weak self] completion in
                self?.isLoading = false
                switch completion {
                case let .failure(error):
                    self?.popUp = .genericError(error: error)
                    Logger.print("error: \(error)")
                case .finished:
                    Logger.print("\(#function) finished")
                }
            } receiveValue: { [weak self] response in
                guard let canStartConversation = response.canResidentCreateNewConversations else {
                    self?.popUp = .genericError()
                    return
                }
                
                self?.canStartConversation = canStartConversation
                
                if !canStartConversation {
                    guard let preferredMethodType = response.preferredMethodOfCommunicationType,
                          let preferredMethod = response.preferredMethodOfCommunication else {
                        self?.popUp = .genericError()
                        return
                    }
                    
                    self?.preferredMethod = preferredMethod
                    if preferredMethodType == .phone {
                        self?.preferredMethodURL = URL(string: "tel://\(preferredMethod)")
                    } else if preferredMethodType == .email {
                        self?.preferredMethodURL = URL(string: "mailto:\(preferredMethod)")
                    }
                }
                
                self?.showDestination = true
                
                Logger.print("\(#function) success: \(response)")
            }.store(in: &cancellables)
    }
}

extension ConversationViewModel {
    private func allConversationInfoPublisher(currentPage: Int, isRefresh: Bool) -> AnyPublisher<AllConversationInfoResponse, Swift.Error> {
        if currentPage == 1 || isRefresh {
            return Publishers.Zip(
                fetchConversation(currentPage: currentPage),
                fetchConversationInfo()
            )
            .map { AllConversationInfoResponse(messages: $0, info: $1) }
            .eraseToAnyPublisher()
        }
        
        return fetchConversation(currentPage: currentPage)
            .map { AllConversationInfoResponse(messages: $0, info: nil) }
            .eraseToAnyPublisher()
    }
    
    private func fetchConversationInfo() -> AnyPublisher<TwoWayMessagingGetMessageResponse, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.getMessage(buildingID: buildingId, conversationID: conversationId)
    }
    
    private func fetchConversation(currentPage: Int) -> AnyPublisher<TwoWayMessagingGetMessagesResponse, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.getMessages(buildingID: buildingId, conversationID: conversationId, page: currentPage)
    }
    
    private func addMessage() -> AnyPublisher<Bool, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.addMessage(
            buildingID: buildingId,
            conversationID: conversationId,
            message: message.basicHTML,
            conversationAttachmentIds: sentFiles.map { $0.id }
        )
    }
    
    private func resolveConversation() -> AnyPublisher<Bool, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.resolveConversation(buildingID: buildingId, conversationID: conversationId)
    }
    
    private func deleteConversation() -> AnyPublisher<Bool, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.delete(buildingID: buildingId, conversationID: conversationId)
    }
    
    private func conversationConfigurationPublisher() -> AnyPublisher<TwoWayMessagingSettingsResponse, Swift.Error> {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return Fail(error: CustomError.missingInformation(description: "missingInformation")).eraseToAnyPublisher() }
        
        return twoWayMessagingService.settings(buildingID: buildingId)
    }
}

struct AllConversationInfoResponse: Decodable {
    let messages: TwoWayMessagingGetMessagesResponse
    let info: TwoWayMessagingGetMessageResponse?
}
