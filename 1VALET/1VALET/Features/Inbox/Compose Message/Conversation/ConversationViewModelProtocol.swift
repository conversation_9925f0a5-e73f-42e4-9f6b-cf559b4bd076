//
//  ConversationViewModelProtocol.swift
//  1VALET
//
//  Created by <PERSON> on 13/07/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import Combine
import NetworkKit
import SwiftUI

protocol ConversationViewModelProtocol: ObservableObject {
    var isLoading                       : Bool { get set }
    var shouldReload                    : Bool { get set }
    var isValidMessage                  : Bool { get }
    var pageTitle                       : String { get }
    var isConversationOpen              : Bool { get }
    var popUp                           : PopUpAlert? { get set }
    var showDestination                 : Bool { get set }
    var canStartConversation            : Bool { get }
    var preferredMethod                 : String { get }
    var preferredMethodURL              : URL? { get }
    var canCloseConversation            : Bool { get }
    
    var message                         : String { get set }
    var sentFiles                       : [SentFile] { get set }
    var paginatedScrollViewModel        : PaginatedScrollViewModel<AllConversationInfoResponse, ConversationMessageViewModel> { get }
    
    func send(action: ConversationActions)
    func fetchInfo()
}

enum ShouldPerformConversationAction {
    case resolve
    case delete
}

enum ConversationActions {
    case submitMessage
    case uploadFile(file: FileData)
    case deleteFile(file: SentFile)
    case showResolvePopUp
    case showDeletePopUp
    case loadConfiguration
}
