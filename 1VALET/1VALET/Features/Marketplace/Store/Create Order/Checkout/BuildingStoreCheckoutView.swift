//
//  BuildingStoreCheckoutView.swift
//  1VALET
//
//  Created by <PERSON> on 02/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import SwiftUIIntrospect

struct BuildingStoreCheckoutView: View {
    // MARK: - Typealias
    private typealias Colors = StoreConstants.Colors
    private typealias Strings = StoreConstants.Strings
    private typealias Sizes = StoreConstants.Sizes
    private typealias Fonts = StoreConstants.Fonts
    private typealias Limits = StoreConstants.Limits

    // MARK: - State Objects
    @StateObject var viewModel: BuildingStoreCheckoutViewModel
    @StateObject var router: BuildingStoreRouter

    init(product: BuildingStoreProduct, router: BuildingStoreRouter) {
        _viewModel = StateObject(wrappedValue: DependencyResolver.resolve(args: product))
        _router = StateObject(wrappedValue: router)
    }

    var body: some View {
        GeometryReader { proxy in
            BaseScrollView(.vertical) {
                VStack(alignment: .leading, spacing: Sizes.extraLargePadding) {
                    VStack(alignment: .leading, spacing: Sizes.smallPadding) {
                        Text(viewModel.product.name)
                            .font(Fonts.productTitle)
                            .foregroundColor(Colors.sectionHeader)

                        Text(viewModel.productPrice)
                            .font(Fonts.productPrice)
                            .foregroundColor(Colors.secondaryLabel)

                        ProductInventoryView(product: viewModel.product)
                    }

                    formView
                }
                .padding(.horizontal, Sizes.largePadding)
                .padding(.top, Sizes.largePadding)
                .frame(minHeight: proxy.size.height)
            }
        }
        .standardNavigationStyle(Strings.checkout)
        .analytics(screen: .storeCheckout)
        .popUpAlert(popUp: $viewModel.popUp)
        .onReceive(viewModel.navigationStream) { destination in
            router.push(destination)
        }
    }
}

private extension BuildingStoreCheckoutView {
    var quantityView: some View {
        QuantitySelector(
            quantity: $viewModel.quantity,
            maxQuantity: viewModel.maxQuantity
        ) {
            viewModel.popUp = PopUpAlert(title: Strings.maxQuantityReached, type: .ok)
        }
    }

    func buttonsSection(validate: @escaping (Bool) -> Bool) -> some View {
        VStack(alignment: .center, spacing: Sizes.smallPadding) {
            Button(Strings.continueWithPrice(viewModel.price)) {
                viewModel.validateAndContinue(validationResult: validate(true))
            }
            .buttonStyle(PrimaryButtonStyle())

            Button(Strings.cancel) {
                router.popToProductDetails()
            }
            .buttonStyle(PlainTextButtonStyle())
        }
    }

    var formView: some View {
        TextFormView { validate in
            VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
                HStack(alignment: .center) {
                    Text(Strings.quantityLabel)
                        .font(Fonts.sectionSubtitle)
                        .foregroundColor(Colors.body)
                    
                    Spacer(minLength: 0)
                    
                    quantityView
                }

                if let additionalFields = viewModel.product.additionalFields {
                    ForEach(additionalFields, id: \.0) { field in
                        input(for: field.1, with: field.0)
                    }
                }
            }
            
            Spacer(minLength: 0)
            
            buttonsSection(validate: validate)
        }
    }

    func input(for field: String, with id: UUID) -> some View {
        let binding = Binding(
            get: { viewModel.additionalFieldValues[id] ?? "" },
            set: { viewModel.additionalFieldValues[id] = $0 }
        )

        return VStack(alignment: .leading, spacing: Sizes.smallPadding) {
            StyledText(text: [
                (text: field, styles: []),
                (text: " *", styles: [.foregroundColor(Colors.requiredFieldIndicator)])
            ])
            .font(Fonts.sectionSubtitle)
            .foregroundColor(Colors.sectionHeader)

            BaseTextEditor(text: binding, inputHeight: Sizes.checkoutInputHeight)
                .validate(fieldName: id.uuidString,
                          text: binding,
                          validationTypes: [
                            .notEmpty(errorMessage: Strings.fieldRequired)
                          ]
                )
                .limitInputLength(value: binding, length: Limits.textFieldLimit)
        }
    }
}
