//
//  BuildingStoreCheckoutViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 16/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import Foundation
import Combine
import InjectionKit

class BuildingStoreCheckoutViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var product: BuildingStoreProduct
    @Published var quantity: Int = 1
    @Published var additionalFieldValues: [UUID: String] = [:]
    @Published var popUp: PopUpAlert?
    
    private var navigateToStreamPublisher: PassthroughSubject<BuildingStoreScreen, Never> = .init()
    public var navigationStream: AnyPublisher<BuildingStoreScreen, Never> {
        navigateToStreamPublisher.eraseToAnyPublisher()
    }
    
    // MARK: - Initialization
    init(product: BuildingStoreProduct) {
        self.product = product
    }
    
    // MARK: - Public Methods
    func validateAndContinue(validationResult: Bool) {
        guard validationResult else { return }
        
        var additionalFields = [String]()
        
        for field in product.additionalFields ?? [] {
            let id = field.0
            if let value = additionalFieldValues[id] {
                additionalFields.append(value)
            }
        }
        
        let request = BuildingStoreOrderRequest(
            product: product,
            quantity: quantity,
            additionalFields: additionalFields
        )
        
        navigateToStreamPublisher.send(.paymentMethods(request: request))
    }
    
    // MARK: - Computed Properties
    var productPrice: String {
        return PaymentUtils.shared.formatCurrency(product.price.amountFromCents)
    }
    
    var price: String {
        let totalPrice = (quantity * product.price)
        return PaymentUtils.shared.formatCurrency(totalPrice.amountFromCents)
    }
    
    var maxQuantity: Int {
        min(product.inventory ?? StoreConstants.Limits.maxQuantity, StoreConstants.Limits.maxQuantity)
    }
}
