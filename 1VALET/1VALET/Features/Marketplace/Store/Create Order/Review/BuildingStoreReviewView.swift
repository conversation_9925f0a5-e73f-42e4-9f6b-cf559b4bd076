//
//  BuildingStoreReviewView.swift
//  1VALET
//
//  Created by <PERSON> on 06/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI
import UtilitiesKit
import InjectionKit
import Swift<PERSON>Introspect

struct BuildingStoreReviewView: View {
    // MARK: - Typealias
    private typealias Colors = StoreConstants.Colors
    private typealias Strings = StoreConstants.Strings
    private typealias Sizes = StoreConstants.Sizes
    private typealias Fonts = StoreConstants.Fonts
    private typealias ImageNames = StoreConstants.ImageNames
    private typealias Limits = StoreConstants.Limits

    @StateObject var viewModel: BuildingStoreReviewViewModel
    @StateObject var router: BuildingStoreRouter

    init(request: BuildingStoreOrderRequest, router: BuildingStoreRouter) {
        self._viewModel = StateObject(wrappedValue: DependencyResolver.resolve(args: request))
        self._router = StateObject(wrappedValue: router)
    }

    var body: some View {
        GeometryReader { proxy in
            BaseScrollView(.vertical) {
                VStack(alignment: .leading, spacing: Sizes.mediumPadding) {
                    totalCharges
                    paymentInfo
                    comments
                    termsAndConditions
                    Spacer(minLength: 0)
                    buttonsSection
                }
                .frame(minHeight: proxy.size.height)
            }
        }
        .standardNavigationStyle(Strings.review)
        .analytics(screen: .storeReview)
        .loading(show: viewModel.requestState.isInLoadingState)
        .popUpAlert(popUp: $viewModel.popUp)
        .onReceive(viewModel.navigationStream, perform: { destination in
            router.push(destination)
        })
    }
}

private extension BuildingStoreReviewView {
    var quantityView: some View {
        QuantitySelector(
            quantity: $viewModel.quantity,
            maxQuantity: viewModel.maxQuantity
        ) {
            viewModel.popUp = PopUpAlert(title: Strings.maxQuantityReached, type: .ok)
        }
    }

    var totalCharges: some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Text(Strings.totalCharges)
                .font(Fonts.productDescription.weight(.bold))

            HStack(alignment: .top, spacing: 0) {
                if let product = viewModel.requestState.loadedValue?.product {
                    VStack(alignment: .leading, spacing: Sizes.extraSmallPadding) {
                        Text(product.name)
                            .font(Fonts.productDescription)

                        Text(product.priceString())
                            .font(Fonts.productDescription.weight(.semibold))
                    }
                }

                Spacer(minLength: Sizes.mediumPadding)

                quantityView
            }

            DividerView()

            HStack(alignment: .center, spacing: 0) {
                Text(Strings.totalDue)
                Spacer(minLength: Sizes.mediumPadding)
                Text(viewModel.totalPrice)
            }
            .font(Fonts.productDescription.weight(.bold))
        }
        .foregroundColor(Colors.sectionHeader)
        .padding(.horizontal, Sizes.largePadding)
        .padding(.top, Sizes.extraLargePadding)
        .padding(.bottom, Sizes.mediumLargePadding)
        .background(Colors.inputBackground)
    }

    var paymentInfo: some View {
        VStack(alignment: .leading, spacing: 0) {
            VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
                Text(Strings.paymentInfo)
                    .font(Fonts.productDescription.weight(.bold))

                if let paymentMethod = viewModel.paymentMethod,
                   let request = viewModel.requestState.loadedValue {
                    Button {
                        router.navigateToPaymentMethods(request: request) {
                            self.viewModel.paymentMethod = $0
                        }
                    } label: {
                        PaymentMethodView(
                            paymentMethod: paymentMethod,
                            textFont: Fonts.productDescription.weight(.semibold),
                            miniCardIcon: paymentMethod.type == .payInPerson,
                            accessoryImageName: ImageNames.chevronRight,
                            optionsAction: {
                                router.navigateToPaymentMethods(request: request) {
                                    self.viewModel.paymentMethod = $0
                                }
                            }
                        )
                        .padding(.vertical, Sizes.extraSmallPadding)
                        .padding(.bottom, paymentMethod.type == .payInPerson ? Sizes.mediumPadding : 0)
                        .background(Colors.inputBackground)
                    }
                }
            }
            .padding(.horizontal, Sizes.largePadding)

            if let paymentMethod = viewModel.paymentMethod,
               paymentMethod.type != .payInPerson {
                WarningBannerView(
                    imageName: ImageNames.payment,
                    textColor: Colors.secondaryLabel,
                    backgroundColor: Colors.secondaryBackground,
                    text: Strings.paymentInfoBanner
                )
            }
        }
        .foregroundColor(Colors.sectionHeader)
        .padding(.top, Sizes.mediumLargePadding)
        .background(Colors.inputBackground)
    }

    var comments: some View {
        VStack(alignment: .leading, spacing: Sizes.smallPadding) {
            Text(Strings.orderComments)
                .font(Fonts.productDescription.weight(.semibold))
                .foregroundColor(Colors.sectionHeader)
            
            BaseTextEditor(
                text: $viewModel.comment,
                placeholder: Strings.maxCharacters,
                inputHeight: Sizes.checkoutInputHeight
            )
            .limitInputLength(value: $viewModel.comment, length: Limits.commentsLimit)
        }
        .padding(.horizontal, Sizes.largePadding)
    }

    var termsAndConditions: some View {
        HStack(spacing: Sizes.mediumPadding) {
            CheckboxView(size: Dimensions.ICON_MEDIUM, isMarked: viewModel.termsAccepted)
                .onTapGesture {
                    viewModel.termsAccepted.toggle()
                }

            Group {
                Text(Strings.iAgreeTo)
                    .foregroundColor(Colors.sectionHeader)
                    .font(Fonts.agreeTerms) +
                Text(Strings.termsAndConditions)
                    .underline()
                    .foregroundColor(Color.primaryLabel)
                    .font(Fonts.agreeTermsLink)
            }
            .onTapGesture {
                router.navigateToTermsAndConditions()
            }
        }
        .padding(Sizes.largePadding)
    }

    var buttonsSection: some View {
        VStack(alignment: .center, spacing: Sizes.smallPadding) {
            Button(Strings.placeOrder, action: viewModel.placeOrder)
                .buttonStyle(PrimaryButtonStyle())
                .disabled(!viewModel.termsAccepted)

            Button(Strings.cancel) {
                router.popToProductDetails()
            }
            .buttonStyle(PlainTextButtonStyle())
        }
        .padding(.horizontal, Sizes.largePadding)
    }
}
