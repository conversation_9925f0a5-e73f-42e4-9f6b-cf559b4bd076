//
//  BuildingStoreReviewViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 20/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//


import Foundation
import Combine
import InjectionKit
import NetworkKit
import UtilitiesKit

class BuildingStoreReviewViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var requestState: ViewState<BuildingStoreOrderRequest>
    @Published var popUp: PopUpAlert?
    @Published var termsAccepted: Bool = false
    @Published var quantity: Int
    @Published var comment: String = ""
    @Published var paymentMethod: PaymentMethod? {
        didSet {
            change(paymentMethod: paymentMethod)
        }
    }
    
    private var navigateToStreamPublisher: PassthroughSubject<BuildingStoreScreen, Never> = .init()
    public var navigationStream: AnyPublisher<BuildingStoreScreen, Never> {
        navigateToStreamPublisher.eraseToAnyPublisher()
    }
    
    // MARK: - Dependencies
    @Injected private var storeRepository: BuildingStoreRepositoryProtocol
    
    // MARK: - Initialization
    init(request: BuildingStoreOrderRequest) {
        self.requestState = .loaded(state: request)
        self.quantity = request.quantity
        self.paymentMethod = request.paymentMethod
    }
    
    // MARK: - Public Methods
    func placeOrder() {
        guard let paymentMethod = paymentMethod,
              let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id,
              let currentRequest = requestState.loadedValue else { return }
        
        Task {
            await MainActor.run {
                requestState = .loading(placeholder: currentRequest)
            }
            
            do {
                let success = try await storeRepository.createOrder(
                    suiteId: suiteId,
                    storeItemId: currentRequest.product.id,
                    quantity: quantity,
                    additionalFields: currentRequest.additionalFields,
                    payInPerson: paymentMethod.type == .payInPerson,
                    paymentMethodId: paymentMethod.id,
                    comments: comment
                ).async()
                
                if success {
                    await FeedbackState.shouldShowFeedback(type: .store)
                }
                
                await MainActor.run {
                    if success {
                        self.requestState = .loaded(state: currentRequest)
                        navigateToStreamPublisher.send(
                            .orderConfirmed(
                                description: StoreConstants.Strings.requestSentPayment(
                                    amount: totalPrice,
                                    paymentMethod: paymentMethod,
                                    email: UserUtils.sharedInstance.loggedInUser?.profile?.email
                                ),
                                isPayInPerson: paymentMethod.type == .payInPerson
                            )
                        )
                    } else {
                        self.requestState = .loaded(state: currentRequest)
                        self.popUp = .genericError()
                    }
                }
            } catch {
                await MainActor.run {
                    self.requestState = .loaded(state: currentRequest)
                    self.popUp = .genericError()
                }
                Logger.print("Failed to load products: \(error)")
                ExceptionLoggingUtils.report(error)
            }
        }
    }
    
    // MARK: - Private Methods
    private func change(paymentMethod: PaymentMethod?) {
        guard let request = self.requestState.loadedValue else { return }
        
        self.requestState = .loaded(
            state: BuildingStoreOrderRequest(
                product: request.product,
                quantity: request.quantity,
                additionalFields: request.additionalFields,
                paymentMethod: paymentMethod
            )
        )
    }
    
    // MARK: - Computed Properties
    var totalPrice: String {
        guard let request = requestState.loadedValue else { return "" }
        
        let totalPrice = (quantity * request.product.price)
        return PaymentUtils.shared.formatCurrency(totalPrice.amountFromCents)
    }
    
    var maxQuantity: Int {
        guard let request = requestState.loadedValue else { return 0 }
        
        return min(request.product.inventory ?? StoreConstants.Limits.maxQuantity, StoreConstants.Limits.maxQuantity)
    }
}
