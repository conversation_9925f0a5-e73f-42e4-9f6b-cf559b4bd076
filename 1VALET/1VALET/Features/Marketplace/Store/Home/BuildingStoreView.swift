//
//  BuildingStoreView.swift
//  1VALET
//
//  Created by <PERSON> on 05/02/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit

struct BuildingStoreView: View {
    // MARK: - Typealias
    private typealias Colors = StoreConstants.Colors
    private typealias Strings = StoreConstants.Strings
    private typealias Sizes = StoreConstants.Sizes
    private typealias Fonts = StoreConstants.Fonts
    private typealias ImageNames = StoreConstants.ImageNames
    private typealias Identifiers = StoreConstants.Identifiers
    
    // MARK: - State Objects
    @StateObject private var viewModel: BuildingStoreViewModel = DependencyResolver.resolve()
    @StateObject private var router = BuildingStoreRouter()
    
    @State private var itemsSize: CGSize = .zero
    
    var body: some View {
        GeometryReader { proxy in
            mainContent(proxy: proxy)
        }
        .onAppear {
            MarketplaceShopUtils.lastTimeStoreSeen = Date()
            viewModel.loadData()
        }
        .standardNavigationStyle(Strings.store)
        .analytics(screen: .storeHome)
    }
    
    private func mainContent(proxy: GeometryProxy) -> some View {
        SearchAdaptiveScrollView(contentSize: $itemsSize, itemsIdentifier: Identifiers.lastItem) {
            LazyVStack(alignment: .leading, spacing: 0, pinnedViews: [.sectionHeaders]) {
                myOrdersSection
                
                Section(header: headerSection) {
                    itemsForSaleSection
                        .readSize($itemsSize)
                }
                .id(Identifiers.lastItem)
                
                Spacer(minLength: 0)
            }
            .frame(minHeight: proxy.size.height, alignment: .top)
        }
        .onRefresh {
            await viewModel.refreshData()
        }
    }
    
    private var myOrdersSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(spacing: 0) {
                Text(Strings.yourOrders)
                    .font(Fonts.sectionHeader)
                    .foregroundColor(Colors.sectionHeader)
                    .padding(.leading, Sizes.largePadding)
                
                Spacer()
                
                Button {
                    router.navigateToAllOrders()
                } label: {
                    Text(Strings.viewMyOrders)
                        .font(Fonts.textLink)
                        .foregroundColor(Colors.body)
                        .underline()
                }
                .padding(.trailing, Sizes.largePadding)
            }
            .padding(.top, Sizes.largePadding)
            
            if let orders = viewModel.ordersState.loadedValue {
                ordersCarouselView(orders: orders)
                    .redacted(when: viewModel.ordersState.isInLoadingState, redactionType: .customPlaceholder)
            }
        }
    }
    
    @ViewBuilder
    private func ordersCarouselView(orders: [BuildingStoreOrder]) -> some View {
        if orders.isEmpty {
            noOrdersView
        } else {
            CarouselView {
                LazyHStack(alignment: .center, spacing: Sizes.smallPadding) {
                    ForEach(orders) { order in
                        BuildingOrderItemView(order: order)
                            .cardify {
                                router.navigateToOrderDetails(order: order)
                            }
                            .frame(width: UIScreen.main.bounds.size.width - (Sizes.largePadding * 2))
                    }
                }
                .animation(.easeInOut)
                .padding(Sizes.largePadding)
            }
        }
    }
    
    private var noOrdersView: some View {
        HStack(alignment: .center, spacing: Sizes.mediumPadding) {
            ZStack(alignment: .center) {
                Image(ImageNames.cart)
                    .resizable()
                    .renderingMode(.template)
                    .scaledToFit()
                    .frame(height: Sizes.icon)
                    .foregroundColor(Colors.icon)
                
                Circle()
                    .strokeBorder(Colors.circleBorder, lineWidth: 1)
                    .frame(width: Sizes.circleBorder, height: Sizes.circleBorder)
            }
                      
            Text(Strings.noPendingOrdersYet)
                .font(Fonts.emptyStateBody)
                .foregroundColor(Colors.body)
                .multilineTextAlignment(.leading)
        }
        .padding(Sizes.largePadding)
    }
    
    @ViewBuilder
    var itemsForSaleSection: some View {
        switch viewModel.productsState {
        case .loading(let placeholder):
            productsGrid(products: placeholder)
        
        case .loaded(let products) where products.isEmpty:
            noItemsView
            
        case .filtered(let filteredProducts , _) where filteredProducts.isEmpty:
            noResultsView
        
        case .filtered(let filteredProducts , _):
            productsGrid(products: filteredProducts)
        
        case .loaded(let products):
            productsGrid(products: products)
        
        default:
            EmptyView()
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Text(Strings.itemsForSale)
                .font(Fonts.sectionHeader)
                .foregroundColor(Colors.body)
                .padding(.leading, Sizes.largePadding)
            
            if viewModel.productsState.isInFilterState || (viewModel.productsState.loadedValue?.isEmpty == false) {
                SearchField(searchTerm: $viewModel.searchText)
                    .padding(.horizontal, Sizes.largePadding)
            }
        }
        .padding(.bottom, Sizes.mediumLargePadding)
        .padding(.top, Sizes.largePadding)
        .background(Colors.background)
    }
    
    private func productsGrid(products: [BuildingStoreProduct]) -> some View {
        LazyVStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            ForEach(products) { product in
                BuildingStoreItemView(product: product)
                    .redacted(when: viewModel.productsState.isInLoadingState, redactionType: .customPlaceholder)
                    .cardify {
                        router.navigateToProductDetails(product: product)
                    }
                    .padding(.horizontal, Sizes.largePadding)
            }
        }
    }
    
    private var noItemsView: some View {
        StoreEmptyStateView(
            icon: ImageNames.cart,
            size: Sizes.largeIcon,
            title: Strings.noItemsTitle,
            description: Strings.noItemsDescription
        )
        .padding(.horizontal, Sizes.largePadding)
        .padding(.top, Sizes.extraLargePadding)
    }
    
    private var noResultsView: some View {
        StoreEmptyStateView(
            icon: ImageNames.search,
            size: Sizes.largeIcon - Sizes.smallPadding,
            title: Strings.noSearchResultsTitle,
            description: Strings.noSearchResultsDescription
        )
        .padding(.horizontal, Sizes.largePadding)
        .padding(.top, Sizes.extraLargePadding)
    }
}
