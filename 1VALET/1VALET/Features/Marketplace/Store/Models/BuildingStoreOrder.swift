//
//  BuildingStoreOrder.swift
//  1VALET
//
//  Created by <PERSON> on 28/02/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import Foundation
import NetworkKit

struct BuildingStoreOrder: Identifiable, Equatable {
    var id: String = UUID().uuidString
    var number: String = "1234"
    var picture: URL?
    var name: String = "placeholder"
    var amount: Int = 1
    var date: Date = Date()
    var status: BuildingStoreOrderStatus = .new
    
    var quantity: Int?
    var paymentMethod: PaymentMethod?
    var paymentId: String?
    var isRefunded: Bool?
    var additionalFields: [(String, String)]?
    var comments: String?
    var canOrderAgain: Bool?
    var itemId: String?
    var isPayInPerson: Bool?
    var history: [OrderHistoryStatus]?
    var conversationId: String?
    var hasPolicies: Bool?
    
    var amountPerItem: Int {
        guard let quantity = quantity else { return 0 }
        
        return amount / quantity
    }
    
    static let placeholder: [BuildingStoreOrder] = [
        BuildingStoreOrder(),
        BuildingStoreOrder(),
        BuildingStoreOrder()
    ]
    
    func amountString() -> String {
        PaymentUtils.shared.formatCurrency(amount.amountFromCents)
    }
    
    static func == (lhs: BuildingStoreOrder, rhs: BuildingStoreOrder) -> Bool {
        return lhs.id == rhs.id &&
            lhs.number == rhs.number &&
            lhs.picture == rhs.picture &&
            lhs.name == rhs.name &&
            lhs.amount == rhs.amount &&
            lhs.date == rhs.date &&
            lhs.status == rhs.status &&
            lhs.quantity == rhs.quantity &&
            lhs.paymentMethod == rhs.paymentMethod &&
            lhs.paymentId == rhs.paymentId &&
            lhs.isRefunded == rhs.isRefunded &&
            lhs.additionalFields?.elementsEqual(rhs.additionalFields ?? [], by: ==) ?? (rhs.additionalFields == nil) &&
            lhs.comments == rhs.comments &&
            lhs.canOrderAgain == rhs.canOrderAgain &&
            lhs.itemId == rhs.itemId &&
            lhs.isPayInPerson == rhs.isPayInPerson &&
            lhs.history?.elementsEqual(rhs.history ?? [], by: ==) ?? (rhs.history == nil) &&
            lhs.conversationId == rhs.conversationId &&
            lhs.hasPolicies == rhs.hasPolicies
    }
}

struct OrderHistoryStatus: Hashable {
    var date: Date
    var status: BuildingStoreOrderStatus
}

extension Array where Element == BuildingStoreOrderResponse {
    func toOrders() -> [BuildingStoreOrder] {
        map { item in
            BuildingStoreOrder(
                id: item.orderId ?? UUID().uuidString,
                number: item.orderNumber ?? "1234",
                picture: URL(string: item.imageUrl ?? ""),
                name: item.itemName ?? "",
                amount: item.amountPaid ?? 0,
                date: (Date.from(string: item.orderDate ?? "", format: TimeConstants.apiDateFormatWithFractional) ?? Date()).changeTimeZone(from: .current, to: SuiteUtils.defaultSuiteTimeZone() ?? .current),
                status: item.status ?? .inProgress
            )
        }
    }
}

extension BuildingStoreOrderDetailsResponse {
    func toOrderDetails() -> BuildingStoreOrder {
        let paymentMethod: PaymentMethod = if let lastFourDigits = paymentInfo?.lastFourDigits,
           let paymentMethodName = paymentInfo?.paymentMethodName {
            PaymentMethod(
                id: UUID().uuidString,
                lastFourDigits: lastFourDigits,
                name: paymentMethodName
            )
        } else {
            .inPersonPayment
        }
        
        return BuildingStoreOrder(
            id: orderId ?? UUID().uuidString,
            number: orderNumber ?? "1234",
            name: itemName ?? "",
            amount: totalPrice ?? 0,
            date: (Date.from(string: orderDate ?? "", format: TimeConstants.apiDateFormatWithFractional) ?? Date()).changeTimeZone(from: .current, to: SuiteUtils.defaultSuiteTimeZone() ?? .current),
            status: status ?? .inProgress,
            quantity: quantity,
            paymentMethod: paymentMethod,
            paymentId: paymentInfo?.paymentId,
            isRefunded: paymentInfo?.isRefunded,
            additionalFields: additionalFields?.map({ ($0.name ?? "", $0.value ?? "") }),
            comments: comments,
            canOrderAgain: itemStatus == .published,
            itemId: itemId,
            isPayInPerson: isPayInPerson,
            history: history?.map({
                OrderHistoryStatus(
                    date: (Date.from(string: $0.timestamp ?? "", format: TimeConstants.apiDateFormatWithFractional) ?? Date()).changeTimeZone(from: .current, to: SuiteUtils.defaultSuiteTimeZone() ?? .current),
                    status: $0.status ?? .new
                )
            }),
            conversationId: conversationId,
            hasPolicies: hasPolicies
        )
    }
}

struct BuildingStoreOrderRequest: Equatable {
    var product: BuildingStoreProduct
    var quantity: Int
    var additionalFields: [String]
    var paymentMethod: PaymentMethod?
}
