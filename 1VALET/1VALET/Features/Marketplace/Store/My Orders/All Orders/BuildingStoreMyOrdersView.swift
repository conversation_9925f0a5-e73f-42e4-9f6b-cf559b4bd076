//
//  BuildingStoreMyOrdersView.swift
//  1VALET
//
//  Created by <PERSON> on 12/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit

struct BuildingStoreMyOrdersView: View {
    // MARK: - Typealias
    private typealias Colors = StoreConstants.Colors
    private typealias Strings = StoreConstants.Strings
    private typealias Sizes = StoreConstants.Sizes
    private typealias Fonts = StoreConstants.Fonts
    private typealias ImageNames = StoreConstants.ImageNames

    @StateObject var viewModel: BuildingStoreMyOrdersViewModel = DependencyResolver.resolve()
    @StateObject var router: BuildingStoreRouter
    
    var body: some View {
        GeometryReader { proxy in
            ScrollView(showsIndicators: false) {
                VStack(alignment: .leading, spacing: Sizes.largePadding) {
                    if viewModel.hasNoOrders {
                        emptyState
                    } else {
                        newOrdersView
                        completedOrdersView
                    }
                    Spacer(minLength: 0)
                }
                .padding(Sizes.largePadding)
                .frame(minHeight: proxy.size.height)
            }
            .onRefresh(viewModel.refreshAllData)
        }
        .standardNavigationStyle(Strings.myOrders)
        .analytics(screen: .storeMyOrders)
        .onAppear(perform: viewModel.loadDataIfNeeded)
    }
}

private extension BuildingStoreMyOrdersView {
    var newOrdersView: some View {
        let newOrders = viewModel.pendingOrdersState.loadedValue ?? []
        
        return VStack(alignment: .leading, spacing: Sizes.mediumPadding) {
            if !newOrders.isEmpty || viewModel.pendingOrdersState.isInLoadingState {
                Text(Strings.newOrders)
                    .font(Fonts.sectionHeader)
                    .foregroundColor(Colors.sectionHeader)
            }
                
            LazyVStack(alignment: .leading, spacing: Sizes.smallPadding) {
                ForEach(newOrders) { order in
                    BuildingOrderItemView(order: order)
                        .cardify {
                            router.navigateToOrderDetails(order: order)
                        }
                        .redacted(when: viewModel.pendingOrdersState.isInLoadingState, redactionType: .customPlaceholder)
                }
            }
        }
    }
    
    var completedOrdersView: some View {
        let completedOrders = viewModel.completedOrdersState.loadedValue ?? []
        
        return VStack(alignment: .leading, spacing: Sizes.mediumPadding) {
            if !completedOrders.isEmpty || viewModel.completedOrdersState.isInLoadingState {
                Text(Strings.completedOrders)
                    .font(Fonts.sectionHeader)
                    .foregroundColor(Colors.sectionHeader)
            }
            
            LazyVStack(alignment: .leading, spacing: Sizes.smallPadding) {
                ForEach(completedOrders) { order in
                    BuildingOrderItemView(order: order)
                        .cardify {
                            router.navigateToOrderDetails(order: order)
                        }
                        .onAppear {
                            viewModel.loadMoreCompletedOrders(after: order)
                        }
                        .redacted(when: viewModel.completedOrdersState.isInLoadingState, redactionType: .customPlaceholder)
                }
                
                if viewModel.completedOrdersState.isInLoadMoreState {
                    HStack {
                        Spacer()
                        ProgressView()
                        Spacer()
                    }
                }
            }
        }
    }
    
    var emptyState: some View {
        GeometryReader { proxy in
            ZStack(alignment: .center) {
                StoreEmptyStateView(
                    icon: ImageNames.cart,
                    size: Sizes.largeIcon,
                    title: Strings.noOrdersYet,
                    description: Strings.ordersWillAppearHere
                )
            }
            .frame(width: proxy.size.width, height: proxy.size.height, alignment: .center)
        }
    }
}
