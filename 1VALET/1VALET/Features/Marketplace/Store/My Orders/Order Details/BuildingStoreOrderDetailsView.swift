//
//  BuildingStoreOrderDetails.swift
//  1VALET
//
//  Created by <PERSON> on 12/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit

struct BuildingStoreOrderDetailsView: View {
    // MARK: - Typealias
    private typealias Colors = StoreConstants.Colors
    private typealias Strings = StoreConstants.Strings
    private typealias Sizes = StoreConstants.Sizes
    private typealias Fonts = StoreConstants.Fonts
    private typealias ImageNames = StoreConstants.ImageNames

    @StateObject var viewModel: BuildingStoreOrderDetailsViewModel
    @StateObject var router: BuildingStoreRouter
    
    init(order: BuildingStoreOrder, router: BuildingStoreRouter) {
        self._viewModel = StateObject(wrappedValue: DependencyResolver.resolve(args: order))
        self._router = StateObject(wrappedValue: router)
    }
    
    var body: some View {
        GeometryReader { proxy in
            BaseScrollView(.vertical) {
                VStack(alignment: .leading, spacing: 0) {
                    if let order = viewModel.orderState.loadedValue {
                        orderContent(order: order)
                    }
                    
                    Spacer(minLength: 0)
                }
                .frame(minHeight: proxy.size.height)
                .redacted(when: viewModel.orderState.isInLoadingState, redactionType: .customPlaceholder)
            }
        }
        .standardNavigationStyle(Strings.yourOrder)
        .analytics(screen: .storeMyOrder)
        .popUpAlert(popUp: $viewModel.popUp)
        .loading(show: viewModel.communicationMethodState.isInLoadingState || viewModel.receiptState.isInLoadingState || viewModel.policiesState.isInLoadingState)
        .onReceive(viewModel.navigationStream, perform: { destination in
            router.push(destination)
        })
        .onAppear {
            viewModel.loadOrder()
        }
    }
}

private extension BuildingStoreOrderDetailsView {
    func orderContent(order: BuildingStoreOrder) -> some View {
        Group {
            if order.isRefunded == true {
                WarningBannerView(
                    imageName: ImageNames.refund,
                    textColor: Colors.refund,
                    backgroundColor: Color.errorBackground,
                    text: Strings.orderRefunded
                )
            }
            
            orderInfo(order: order)
            
            if let comments = order.comments,
               !comments.isEmpty {
                orderComments(comments)
            }
            
            buttonsView
            
            if let history = order.history,
               !history.isEmpty {
                orderActivity(history: history)
            }
            
            totalCharges(order: order)
            
            paymentInfo(order: order)
            
            if order.status != .cancelled {
                cancellationPolicy
            }
        }
    }
    
    func orderInfo(order: BuildingStoreOrder) -> some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            VStack(alignment: .leading, spacing: Sizes.smallPadding) {
                BuildingOrderStatusView(status: order.status)
                
                Text(Strings.order(number: order.number))
                    .font(Fonts.productDescription.weight(.bold))
                    .foregroundColor(Colors.primaryLabel)
            }
            
            ReviewIconTextView(iconName: ImageNames.quantity, text: Strings.quantityItems(order.quantity ?? 0), font: Fonts.productDescription, alignment: .center)
            
            ReviewIconTextView(iconName: ImageNames.calendar, text: (order.date).toFormat(TimeConstants.STORE_ORDER, timeZone: .current), font: Fonts.productDescription, alignment: .center)
            
            if let info = order.additionalFields {
                VStack(alignment: .leading, spacing: Sizes.smallPadding) {
                    ReviewIconTextView(iconName: ImageNames.infoCircle, text: Strings.itemName, font: Fonts.productDescription.weight(.semibold), alignment: .center)
                    
                    ReviewIconTextView(iconName: "", text: order.name, font: Fonts.productDescription, fontColor: Colors.icon, alignment: .center)
                    
                    ForEach(info, id: \.0) { key, value in
                        VStack(alignment: .leading, spacing: Sizes.smallPadding) {
                            ReviewIconTextView(iconName: "", text: key, font: Fonts.productDescription.weight(.semibold), alignment: .center)
                            
                            ReviewIconTextView(iconName: "", text: value, font: Fonts.productDescription, fontColor: Colors.icon, alignment: .center)
                        }
                    }
                }
            }
            
            DividerView()
        }
        .padding(.top, Sizes.mediumLargePadding)
        .padding(.horizontal, Sizes.largePadding)
        .background(Colors.inputBackground)
    }
    
    func orderComments(_ comments: String) -> some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Text(Strings.yourOrderComments)
                .font(Fonts.productDescription.weight(.bold))
                .foregroundColor(Colors.primaryLabel)
            
            ReviewIconTextView(iconName: ImageNames.infoCircle, text: comments, font: Fonts.productDescription)
            
            DividerView()
        }
        .padding(.top, Sizes.mediumLargePadding)
        .padding(.horizontal, Sizes.largePadding)
        .background(Colors.inputBackground)
    }
    
    var buttonsView: some View {
        VStack(alignment: .leading, spacing: 0) {
            orderAgain
            
            DividerView()
                .padding(.horizontal, Sizes.largePadding)
            
            messageManagement
            
            DividerView()
                .padding(.horizontal, Sizes.largePadding)
            
            if viewModel.canShowPoliciesButton {
                storePolicy
                
                DividerView()
                    .padding(.horizontal, Sizes.largePadding)
            }
        }
        .background(Colors.inputBackground)
    }
    
    var orderAgain: some View {
        Button(action: viewModel.handleOrderAgainButtonTap) {
            HStack {
                ReviewIconTextView(iconName: ImageNames.cart, text: Strings.orderAgain, font: Fonts.productDescription.weight(.semibold), alignment: .center)
                Spacer()
                chevron
            }
            .padding(.vertical, Sizes.smallPadding)
            .padding(.horizontal, Sizes.largePadding)
        }
    }
    
    var messageManagement: some View {
        Button(action: viewModel.handleMessageManagementButtonTap) {
            HStack {
                ReviewIconTextView(iconName: ImageNames.compose, text: viewModel.messageManagementTitle, font: Fonts.productDescription.weight(.semibold), alignment: .center)
                Spacer()
                chevron
            }
            .padding(.vertical, Sizes.smallPadding)
            .padding(.horizontal, Sizes.largePadding)
        }
    }
    
    var storePolicy: some View {
        Button(action: viewModel.handlePoliciesButtonTap) {
            HStack {
                ReviewIconTextView(iconName: ImageNames.document, text: Strings.viewStorePolicy, font: Fonts.productDescription.weight(.semibold), alignment: .center)
                Spacer()
                chevron
            }
            .padding(.vertical, Sizes.smallPadding)
            .padding(.horizontal, Sizes.largePadding)
        }
    }
    
    var chevron: some View {
        Image(ImageNames.chevronRight)
            .resizable()
            .renderingMode(.template)
            .scaledToFit()
            .foregroundColor(Colors.icon)
            .frame(width: 32, height: 32)
    }
    
    func orderActivity(history: [OrderHistoryStatus]) -> some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Text(Strings.orderActivity)
                .font(Fonts.productDescription.weight(.bold))
                .foregroundColor(Colors.primaryLabel)
            
            ForEach(history, id: \.self) { item in
                VStack(alignment: .leading, spacing: Sizes.extraSmallPadding) {
                    Text(item.status.title)
                        .font(Fonts.sectionHeader)
                    
                    Text((item.date).toFormat(TimeConstants.STORE_ORDER_SHORT, timeZone: .current))
                        .font(Fonts.emptyStateBody)
                }
                .foregroundColor(Colors.body)
                .frame(maxWidth: .infinity, alignment: .leading)
                
                if history.last != item {
                    DividerView()
                }
            }
        }
        .padding(.top, Sizes.mediumLargePadding)
        .padding(.bottom, Sizes.mediumLargePadding)
        .padding(.horizontal, Sizes.largePadding)
        .background(Colors.inputBackground)
    }
    
    func totalCharges(order: BuildingStoreOrder) -> some View {
        let quantity = order.quantity ?? 0
        
        return VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Text(Strings.totalCharges)
                .font(Fonts.productDescription.weight(.bold))
                .foregroundColor(Colors.primaryLabel)
            
            HStack(alignment: .top, spacing: 0) {
                VStack(alignment: .leading, spacing: Sizes.extraSmallPadding) {
                    Text(Strings.itemPrice)
                    
                    Text(Strings.quantity(quantity))
                        .font(Fonts.productDescription)
                }
                
                Spacer(minLength: Sizes.mediumPadding)
                
                if quantity > 0 {
                    Text(viewModel.pricePerItem)
                }
            }
            .foregroundColor(Colors.body)
            .font(Fonts.productDescription)
            
            DividerView()
            
            HStack(alignment: .center, spacing: 0) {
                Text(Strings.totalPaid)
                
                Spacer(minLength: Sizes.mediumPadding)
                
                Text(order.amountString())
            }
            .foregroundColor(Colors.body)
            .font(Fonts.productDescription.weight(.semibold))
            
            if order.isRefunded == true {
                HStack(alignment: .center, spacing: 0) {
                    Text(Strings.totalRefunded)
                    
                    Spacer(minLength: Sizes.mediumPadding)
                    
                    Text(order.amountString())
                }
                .foregroundColor(Colors.requiredFieldIndicator)
                .font(Fonts.productDescription.weight(.semibold))
            }
        }
        .padding(.top, Sizes.mediumLargePadding)
        .padding(.horizontal, Sizes.largePadding)
        .background(Colors.inputBackground)
    }
    
    func paymentInfo(order: BuildingStoreOrder) -> some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Group {
                Text(Strings.paymentInfo)
                    .font(Fonts.productDescription.weight(.bold))
                    .foregroundColor(Colors.primaryLabel)
                
                if let paymentMethod = order.paymentMethod {
                    PaymentMethodView(
                        paymentMethod: paymentMethod,
                        textFont: Fonts.productDescription.weight(.semibold),
                        miniCardIcon: order.isPayInPerson == true,
                        accessoryImageName: nil
                    )
                }
                
                DividerView()
            }
            .padding(.horizontal, Sizes.largePadding)
            
            if viewModel.orderState.loadedValue?.paymentId != nil {
                Button(action: viewModel.handleReceiptButtonTap) {
                    HStack {
                        ReviewIconTextView(iconName: ImageNames.receipt, text: Strings.viewOrderReceipt, font: Fonts.productDescription.weight(.semibold), alignment: .center)
                        Spacer()
                        chevron
                    }
                    .padding(.vertical, Sizes.smallPadding)
                    .padding(.horizontal, Sizes.largePadding)
                }
            }
        }
        .padding(.top, Sizes.mediumLargePadding)
        .background(Colors.inputBackground)
    }
    
    var cancellationPolicy: some View {
        VStack(alignment: .leading, spacing: Sizes.mediumLargePadding) {
            Text(Strings.cancellationPolicy)
                .font(Fonts.productDescription.weight(.bold))
                .foregroundColor(Colors.primaryLabel)
            
            Text(Strings.contactManagementToCancel)
                .multilineTextAlignment(.leading)
                .font(Fonts.productDescription)
                .foregroundColor(Colors.body)
        }
        .padding(.vertical, Sizes.mediumLargePadding)
        .padding(.horizontal, Sizes.largePadding)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Colors.inputBackground)
    }
}
