//
//  BuildingStoreOrderDetailsViewModel.swift
//  1VALET
//
//  Created by <PERSON> on 21/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//


import Foundation
import Combine
import InjectionKit
import NetworkKit
import UtilitiesKit

class BuildingStoreOrderDetailsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var orderState: ViewState<BuildingStoreOrder> = .undefined
    @Published var communicationMethodState: ViewState<PropertyManagementCommunicationMethod?> = .undefined
    @Published var policiesState: ViewState<[URL]> = .undefined
    @Published var receiptState: ViewState<String?> = .undefined
    @Published var popUp: PopUpAlert?
    
    private var navigateToStreamPublisher: PassthroughSubject<BuildingStoreScreen, Never> = .init()
    public var navigationStream: AnyPublisher<BuildingStoreScreen, Never> {
        navigateToStreamPublisher.eraseToAnyPublisher()
    }
    
    // MARK: - Private properties
    private var order: BuildingStoreOrder
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Dependencies
    @Injected private var storeRepository: BuildingStoreRepositoryProtocol
    @Injected private var paymentRepository: RentPaymentRepositoryProtocol
    
    // MARK: - Initialization
    init(order: BuildingStoreOrder) {
        self.order = order
        
        NotificationCenter.default
            .publisher(for: Notification.Name(NotificationName.storeOrderReceived.rawValue))
            .compactMap {
                $0.userInfo?[NotificationConstants.STORE_ORDER_ID] as? String
            }
            .filter {
                $0 == order.id
            }
            .sink { [weak self] _ in self?.loadOrder(override: true) }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    func loadOrder(override: Bool = false) {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id else { return }
        
        if !override {
            guard case .undefined = orderState else { return }
        }
        
        Task {
            await MainActor.run {
                orderState = .loading(placeholder: orderState.loadedValue ?? order)
            }
            
            do {
                let fetchedOrder = try await storeRepository.getOrder(suiteId: suiteId, storeOrderId: order.id).async()
                await MainActor.run {
                    self.orderState = .loaded(state: fetchedOrder.toOrderDetails())
                }
            } catch {
                await MainActor.run {
                    self.orderState = .loaded(state: orderState.loadedValue ?? order)
                    self.popUp = .genericError()
                }
                Logger.print("Failed to load orders: \(error)")
                ExceptionLoggingUtils.report(error)
            }
        }
    }
    
    func handleOrderAgainButtonTap() {
        guard let order = orderState.loadedValue else { return }
        
        if order.canOrderAgain == true,
           let productId = order.itemId {
            navigateToStreamPublisher.send(.itemDetails(item: .init(id: productId)))
        } else {
            popUp = PopUpAlert(title: StoreConstants.Strings.itemNoLongerAvailable, type: .ok)
        }
    }
    
    func handleReceiptButtonTap() {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId,
              let order = orderState.loadedValue,
              let paymentId = order.paymentId else { return }
        
        receiptState = .loading(placeholder: nil)
        
        Task {
            do {
                let receiptUrl = try await paymentRepository.paymentReceiptUrl(buildingId: buildingId, paymentId: paymentId).async()
                
                if let url = URL(string: receiptUrl) {
                    navigateToStreamPublisher.send(.receiptURL(url: url, title: StoreConstants.Strings.storePayment))
                } else {
                    await MainActor.run {
                        popUp = .genericError()
                    }
                }
            } catch {
                await MainActor.run {
                    popUp = .genericError()
                }
            }
            
            await MainActor.run {
                receiptState = .loaded(state: nil)
            }
        }
    }
    
    func handleMessageManagementButtonTap() {
        communicationMethodState = .loading(placeholder: nil)
        
        Task {
            if let communicationMethod = await ConversationPreferences.fetchMessagingConfiguration() {
                switch communicationMethod {
                case .otherPreferredMethod(let method, let url):
                    navigateToStreamPublisher.send(.preferredMethod(method: method, url: url))
                case .conversation:
                    if let conversationId = orderState.loadedValue?.conversationId {
                        navigateToStreamPublisher.send(.continueConversation(id: conversationId))
                    } else {
                        navigateToStreamPublisher.send(
                            .startConversation(
                                subject: StoreConstants.Strings.storeOrderNumber(orderState.loadedValue?.number ?? ""),
                                orderId: order.id
                            )
                        )
                    }
                }
            } else {
                await MainActor.run {
                    popUp = .genericError()
                }
            }
            
            await MainActor.run {
                communicationMethodState = .loaded(state: nil)
            }
        }
    }
    
    func handlePoliciesButtonTap() {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id,
              let order = orderState.loadedValue,
              let itemId = order.itemId else { return }
        
        Task {
            await MainActor.run {
                policiesState = .loading(placeholder: [])
            }
            
            do {
                let policies = try await storeRepository.getPolicies(suiteId: suiteId, storeItemId: itemId).async()
                let urls = policies.compactMap({ URL(string: $0) })
                
                await MainActor.run {
                    if !urls.isEmpty {
                        navigateToStreamPublisher.send(.policy(product: nil, policies: urls, currentPolicyIndex: 0))
                    }
                }
            } catch {
                await MainActor.run {
                    self.popUp = .genericError()
                }
                Logger.print("Failed to load policies: \(error)")
                ExceptionLoggingUtils.report(error)
            }
            
            await MainActor.run {
                policiesState = .loaded(state: [])
            }
        }
    }
    
    // MARK: Computed properties
    var pricePerItem: String {
        guard let order = orderState.loadedValue,
              let quantity = order.quantity,
              quantity > 0 else { return PaymentUtils.shared.formatCurrency(0) }
        
        let price = (order.amount / quantity)
        return PaymentUtils.shared.formatCurrency(price.amountFromCents)
    }
    
    var messageManagementTitle: String {
        orderState.loadedValue?.conversationId == nil ? StoreConstants.Strings.messageManagement : StoreConstants.Strings.viewOrderMessages
    }
    
    var canShowPoliciesButton: Bool {
        guard let order = orderState.loadedValue else { return false }
        
        return order.hasPolicies ?? false
    }
}
