//
//  StoreEmptyStateView.swift
//  1VALET
//
//  Created by <PERSON> on 27/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI

struct StoreEmptyStateView: View {
    // MARK: - Typealias
    private typealias Colors = StoreConstants.Colors
    private typealias Strings = StoreConstants.Strings
    private typealias Sizes = StoreConstants.Sizes
    private typealias Fonts = StoreConstants.Fonts
    
    private(set) var icon: String
    private(set) var size: CGFloat
    private(set) var title: String
    private(set) var description: String
    
    var body: some View {
        VStack(alignment: .center, spacing: Sizes.largePadding) {
            ZStack(alignment: .center) {
                Image(icon)
                    .resizable()
                    .renderingMode(.template)
                    .scaledToFit()
                    .frame(height: size)
                    .foregroundColor(Colors.aqua)
                
                Circle()
                    .strokeBorder(Colors.circleBorder, lineWidth: Sizes.border)
                    .frame(width: Sizes.extraLargeIcon, height: Sizes.extraLargeIcon)
            }
            
            VStack(alignment: .center, spacing: Sizes.extraSmallPadding) {
                Text(title)
                    .font(Fonts.emptyStateTitle)
                    .foregroundColor(Colors.body)
                
                Text(description)
                    .font(Fonts.emptyStateBody)
                    .foregroundColor(Colors.body)
            }
            .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, alignment: .center)
    }
}
