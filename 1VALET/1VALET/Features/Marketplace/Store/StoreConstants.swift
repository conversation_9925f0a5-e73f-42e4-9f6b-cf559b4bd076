//
//  StoreConstants.swift
//  1VALET
//
//  Created by <PERSON> on 09/05/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI

public enum StoreConstants {
    enum Colors {
        static var sectionHeader: Color { Color.coolGrey }
        static var body: Color { Color.coolGrey }
        static var icon: Color { Color.slateGrey }
        static var background: Color { Color.appBackground }
        
        static var circleBorder: Color { Color.cloudyGrey }
        
        static var aqua: Color { Color.aqua }
        static var alwaysWhite: Color { Color.alwaysWhite }
        static var primary: Color { Color.primaryMain }
        static var primaryLabel: Color { Color.primaryLabel }
        static var secondaryLabel: Color { Color.secondaryLabel }
        static var secondaryBackground: Color { Color.secondaryBackground }
        
        static var inputBackground: Color { Color.pearlWhite }
        static var requiredFieldIndicator: Color { Color.errorWarning }
        static var refund: Color { Color.errorLabel }
    }
    
    enum Fonts {
        static let sectionHeader = Font.h3(.bold)
        static let textLink = Font.h5(.bold)
        
        static let emptyStateBody = Font.h4(.regular)
        static let emptyStateTitle = Font.h4(.semiBold)
        
        static let productTitle = Font.h2(.bold)
        static let productPrice = Font.h2(.bold)
        static let productDescription = Font.h3(.regular)
        static let sectionSubtitle = Font.h3(.semiBold)
        
        static let agreeTerms = Font.h5(.regular)
        static let agreeTermsLink = Font.h5(.semiBold)
        
        static let bodyRegular = Font.body(.regular)
    }
    
    enum Strings {
        static let store = "STORE".localized()
        static let yourOrders = "STORE_YOUR_ORDERS".localized()
        static let myOrders = "STORE_MY_ORDERS".localized()
        static let viewMyOrders = "STORE_VIEW_MY_ORDERS".localized()
        static let noPendingOrdersYet = "STORE_NO_PENDING_ORDERS_YET".localized()
        static let itemsForSale = "STORE_ITEMS_FOR_SALE".localized()
        static let noItemsTitle = "STORE_NO_ITEMS_TITLE".localized()
        static let noItemsDescription = "STORE_NO_ITEMS_DESCRIPTION".localized()
        static let noSearchResultsTitle = "STORE_NO_SEARCH_RESULTS_TITLE".localized()
        static let noSearchResultsDescription = "STORE_NO_SEARCH_RESULTS_DESCRIPTION".localized()
        static let outOfStock = "STORE_OUT_OF_STOCK".localized()
        static let inStock = "STORE_IN_STOCK".localized()
        static let continueButton = "CONTINUE".localized()
        static let contactManager = "STORE_CONTACT_MANAGER".localized()
        static let policy = "STORE_POLICY".localized()
        static let placeholder = "STORE_PLACEHOLDER".localized()
        static let iAgree = "STORE_I_AGREE".localized()
        static let cancel = "CANCEL".localized()
        static let returnToOrder = "STORE_RETURN_TO_ORDER".localized()
        static let nextPolicy = "STORE_NEXT_POLICY".localized()
        static let previousPolicy = "STORE_PREVIOUS_POLICY".localized()
        static let checkout = "STORE_CHECKOUT".localized()
        static let fieldRequired = "STORE_FIELD_REQUIRED".localized()
        static let paymentOptions = "STORE_PAYMENT_OPTIONS".localized()
        static let review = "STORE_REVIEW".localized()
        static let totalCharges = "STORE_TOTAL_CHARGES".localized()
        static let totalDue = "STORE_TOTAL_DUE".localized()
        static let paymentInfo = "STORE_PAYMENT_INFO".localized()
        static let paymentInfoBanner = "STORE_PAYMENT_INFO_BANNER".localized()
        static let orderComments = "STORE_ORDER_COMMENTS".localized()
        static let maxCharacters = "STORE_MAX_CHARACTERS".localized()
        static let iAgreeTo = "STORE_I_AGREE_TO".localized()
        static let termsAndConditions = "STORE_TERMS_AND_CONDITIONS".localized()
        static let placeOrder = "STORE_PLACE_ORDER".localized()
        static let maxQuantityReached = "STORE_MAX_QUANTITY_REACHED".localized()
        static let orderConfirmedTitle = "STORE_ORDER_CONFIRMED_TITLE".localized()
        static let ok = "OK".localized()
        static let addEmail = "PAYMENTS_ADD_EMAIL".localized()
        static let noThanks = "PAYMENTS_NO_THANKS".localized()
        static let newOrders = "STORE_NEW_ORDERS".localized()
        static let completedOrders = "STORE_COMPLETED_ORDERS".localized()
        static let yourOrder = "STORE_YOUR_ORDER".localized()
        static let orderRefunded = "STORE_ORDER_REFUNDED".localized()
        static let itemName = "STORE_ITEM_NAME".localized()
        static let yourOrderComments = "STORE_YOUR_ORDER_COMMENTS".localized()
        static let orderAgain = "STORE_ORDER_AGAIN".localized()
        static let messageManagement = "STORE_MESSAGE_MANAGEMENT".localized()
        static let viewStorePolicy = "STORE_VIEW_STORE_POLICY".localized()
        static let totalPaid = "STORE_TOTAL_PAID".localized()
        static let totalRefunded = "STORE_TOTAL_REFUNDED".localized()
        static let viewOrderReceipt = "STORE_VIEW_ORDER_RECEIPT".localized()
        static let cancellationPolicy = "STORE_CANCELLATION_POLICY".localized()
        static let contactManagementToCancel = "STORE_CONTACT_MANAGEMENT_TO_CANCEL".localized()
        static let genericError = "STORE_GENERIC_ERROR".localized()
        static let itemNoLongerAvailable = "STORE_ITEM_NO_LONGER_AVAILABLE".localized()
        static let storePayment = "STORE_PAYMENT".localized()
        static let noOrdersYet = "STORE_NO_ORDERS_YET".localized()
        static let ordersWillAppearHere = "STORE_ORDERS_WILL_APPEAR_HERE".localized()
        static let orderActivity = "STORE_ORDER_ACTIVITY".localized()
        static let viewOrderMessages = "STORE_ORDER_MESSAGES".localized()
        static let quantityLabel = "STORE_QUANTITY_TITLE".localized()
        static let itemPrice = "STORE_ITEM_PRICE".localized()
        
        static func outOfStockAmenityTitle(productName: String) -> String {
            String(format: "STORE_OUT_OF_STOCK_AMENITY_TITLE".localized(), productName)
        }
        
        static func order(number: String) -> String {
            String(format: "STORE_ORDER_NUMBER".localized(), number)
        }
        
        static func inStock(inventory: Int) -> String {
            String(format: "STORE_IN_STOCK_INVENTORY".localized(), inventory)
        }
        
        static func continueWithPrice(_ price: String) -> String {
            String(format: "STORE_CONTINUE_WITH_PRICE".localized(), price)
        }
        
        static func storeOrderNumber(_ number: String) -> String {
            String(format: "STORE_STORE_ORDER_NUMBER".localized(), number)
        }
        
        static func quantity(_ quantity: Int) -> String {
            String(format: "STORE_QUANTITY".localized(), quantity)
        }
        
        static func quantityItems(_ quantity: Int) -> String {
            String(format: "STORE_ITEMS".localized(), quantity)
        }
        
        static func requestSentPayment(amount: String, paymentMethod: PaymentMethod, email: String?) -> String {
            if paymentMethod.type == .payInPerson {
                return "STORE_REQUEST_SENT_PAYMENT_PAY_IN_PERSON".localized()
            }
            if let email = email, !email.isEmpty {
                return String(format: "STORE_REQUEST_SENT_PAYMENT_WITH_EMAIL".localized(), amount, paymentMethod.description.capitalized, email)
            }
            return String(format: "STORE_REQUEST_SENT_PAYMENT_NO_EMAIL".localized(), amount, paymentMethod.description.capitalized)
        }
    }
    
    enum Sizes {
        static let extraExtraSmallPadding = Spacing.EXTRA_EXTRA_SMALL
        static let extraSmallPadding = Spacing.EXTRA_SMALL
        static let smallPadding = Spacing.SMALL
        static let mediumPadding = Spacing.MEDIUM
        static let mediumLargePadding = Spacing.MEDIUM_LARGE
        static let largePadding = Spacing.LARGE
        static let extraLargePadding = Spacing.EXTRA_LARGE
        
        static let smallCornerRadius = Dimensions.SMALL_ROUNDED_CORNER
        
        static let border = Dimensions.GENERIC_BORDER_WIDTH
        
        static let icon: CGFloat = 24
        static let largeIcon: CGFloat = 48
        static let extraLargeIcon: CGFloat = 75
        
        static let circleBorder: CGFloat = 40
        static let goToTopOffset: CGFloat = 150
        
        static let orderItemImageWidth: CGFloat = 100
        static let productItemImageHeight: CGFloat = 140
        
        static let productImageHeight: CGFloat = 266
        
        static let checkoutInputHeight: CGFloat = 100
    }
    
    enum ImageNames {
        static let cart = "cart"
        static let search = "search"
        static let arrowsLeft = "double_arrows_left"
        static let emptyService = "image_empty_service"
        static let marketplace = "marketplace"
        static let chevronRight = "chevron_right"
        static let payment = "payment"
        static let alertCircle = "alert_circle"
        static let quantity = "quantity"
        static let calendar = "calendar"
        static let infoCircle = "info_circle"
        static let document = "document"
        static let compose = "compose"
        static let receipt = "receipt"
        static let refund = "refund"
    }
    
    enum Limits {
        static let maxQuantity = 99
        static let textFieldLimit = 50
        static let commentsLimit = 200
    }
    
    enum Identifiers {
        static let lastItem = "store_last_item"
    }
}
