//
//  SearchAdaptiveScrollView.swift
//  1VALET
//
//  Created by <PERSON> on 18/06/25.
//  Copyright © 2025 1Valet. All rights reserved.
//

import SwiftUI

// MARK: - ScrollView Modifier
struct SearchAdaptiveScrollView<Content: View>: View {
    @Binding var contentSize: CGSize
    let itemsIdentifier: String
    let content: () -> Content
    
    @State private var scrollOffset: CGFloat = 0
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView(.vertical, showsIndicators: false) {
                content()
                    .readOffset($scrollOffset)
                    .onChange(of: contentSize) { newSize in
                        if scrollOffset > 50 && newSize.height < (scrollOffset + UIScreen.main.bounds.height - 100) {
                            adjustScrollPosition(scrollProxy: proxy)
                        }
                    }
            }
            .coordinateSpace(name: "scroll")
        }
    }
    
    private func adjustScrollPosition(scrollProxy: ScrollViewProxy) {
        scrollProxy.scrollTo(itemsIdentifier, anchor: .top)
    }
}
