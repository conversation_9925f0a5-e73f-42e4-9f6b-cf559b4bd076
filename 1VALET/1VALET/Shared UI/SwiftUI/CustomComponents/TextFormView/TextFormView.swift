//
//  TextFormView.swift
//  1VALET
//
//  Created by <PERSON> on 2024-05-29.
//  Copyright © 2024 1Valet. All rights reserved.
//

import SwiftUI

struct ValidationError: Hashable {
    let fieldName: String
    let errorMessage: String?
}

struct FieldValidationModifier: ViewModifier {
    let fieldName: String
    let showErrorMessage: Bool
    @Binding var text: String
    let validationTypes: [ValidationType]
    @SwiftUI.Environment(\.validationErrors) private var validationErrors
    @SwiftUI.Environment(\.showFormValidationErrors) private var showErrors
    
    private var errorMessage: String? {
        validationErrors.wrappedValue.first(where: { $0.fieldName == fieldName && $0.errorMessage != nil })?.errorMessage
    }
    
    private var hasError: Bool {
        errorMessage != nil && showErrors
    }
    
    func validate() {
        let error = ValidationError(fieldName: fieldName, errorMessage: validationTypes.compactMap({ $0.validate(text) }).first)
        
        if let index = validationErro<PERSON>.wrappedValue.firstIndex(where: { fieldName == $0.fieldName }) {
            validationErrors.wrappedValue.remove(at: index)
        }
        validationErrors.wrappedValue.append(error)
    }
    
    func body(content: Content) -> some View {
        VStack(alignment: .center, spacing: Spacing.EXTRA_SMALL) {
            content
                .foregroundColor(hasError ? Color.errorWarning : nil)
                .overlay(errorOutline)
            
            if showErrorMessage, hasError, let errorMessage = errorMessage {
                HStack {
                    Text(errorMessage)
                        .foregroundColor(Color.errorWarning)
                        .font(Font.error)
                        .padding(.leading, Spacing.SMALL)
                    
                    Spacer(minLength: 0)
                }
            }
        }
        .onAppear(perform: validate)
        .onChange(of: text) { _ in
            validate()
        }
    }
    
    @ViewBuilder
    var errorOutline: some View {
        if hasError {
            RoundedRectangle(cornerRadius: Dimensions.MEDIUM_ROUNDED_CORNER)
                .fill(Color.clear.opacity(0), strokeBorderStyle: Color.errorWarning, lineWidth: 1)
                .allowsHitTesting(false)
        }
    }
}

enum ValidationType {
    case isEmail(errorMessage: String)
    case isPhoneNumber(errorMessage: String)
    case notEmpty(errorMessage: String)
    case custom(validate: () -> String?)
    
    func validate(_ string: String) -> String? {
        switch self {
        case .isEmail(let errorMessage):
            return evaluate(pattern: "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}", string: string) ? nil : errorMessage
        case .notEmpty(let errorMessage):
            return string.replacingOccurrences(of: "\n", with: "").trimmingCharacters(in: .whitespaces).isEmpty ? errorMessage : nil
        case .isPhoneNumber(let errorMessage):
            return evaluate(pattern: "^\\d{3}\\d{3}\\d{4}$", string: string.digitsOnly) ? nil : errorMessage
        case .custom(let customValidate):
            return customValidate()
        }
    }
    
    private func evaluate(pattern: String, string: String) -> Bool {
        let pattern = NSPredicate(format: "SELF MATCHES %@", pattern)
        
        return pattern.evaluate(with: string)
    }
}

extension View {
    func validate(fieldName: String, showErrorMessage: Bool = true, text: Binding<String>, validationTypes: [ValidationType]) -> some View {
      self
            .modifier(FieldValidationModifier(fieldName: fieldName, showErrorMessage: showErrorMessage, text: text, validationTypes: validationTypes))
   }
}

struct FormValidationErrors: EnvironmentKey {
    static var defaultValue: Binding<[ValidationError]> = .constant([])
}

struct ShowFormValidationErrors: EnvironmentKey {
    static var defaultValue: Bool = false
}

extension EnvironmentValues {
    var validationErrors: Binding<[ValidationError]> {
        get { self[FormValidationErrors.self] }
        set { self[FormValidationErrors.self] = newValue }
    }
    
    var showFormValidationErrors: Bool {
        get { self[ShowFormValidationErrors.self] }
        set { self[ShowFormValidationErrors.self] = newValue }
    }
}

struct TextFormView<Content : View> : View {
    @State var showErrors: Bool = false
    @State var validationErrors: [ValidationError] = []
    @ViewBuilder var content: (( @escaping (Bool) -> Bool)) -> Content
    
    var body: some View {
        content(validate)
            .environment(\.validationErrors, $validationErrors)
            .environment(\.showFormValidationErrors, showErrors)
    }

    private func validate(canShowErrors: Bool) -> Bool {
        showErrors = canShowErrors
        
        return validationErrors.allSatisfy({ $0.errorMessage == nil }) || validationErrors.isEmpty
   }
}
