//
//  PaymentMethodsListView.swift
//  1VALET
//
//  Created by <PERSON> on 2023-11-09.
//  Copyright © 2023 1Valet. All rights reserved.
//

import SwiftUI
import InjectionKit
import NetworkKit

struct PaymentMethodsListView: View {
    @Binding var chosenPaymentMethod: PaymentMethod?
    @ObservedViewModel var viewModel: PaymentMethodsListViewModelProtocol
    private var showOptions: Bool
    private var showSelector: Bool
    
    init(chosenPaymentMethod: Binding<PaymentMethod?> = .constant(nil), viewModel: PaymentMethodsListViewModelProtocol, showOptions: Bool = true, showSelector: Bool = true) {
        self._chosenPaymentMethod = chosenPaymentMethod
        self._viewModel = ObservedViewModel(wrappedValue: viewModel)
        self.showOptions = showOptions
        self.showSelector = showSelector
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.SMALL) {
            Text("PAYMENTS_PAYMENT_METHODS".localized())
                .font(Font.h3(.semiBold))
                .foregroundColor(Color.coolGrey)
                .padding(.leading, Spacing.LARGE)
            
            VStack(alignment: .center, spacing: 0) {
                DividerView()
                
                ForEach(viewModel.paymentMethods) { paymentMethod in
                    VStack(alignment: .center, spacing: 0) {

                        paymentMethodRow(for: paymentMethod)
                        
                        DividerView()
                    }
                }
                
                NavigationLink(destination: {
                    createPaymentMethodsView
                }) {
                    HStack {
                        Text("PAYMENTS_ADD_METHOD".localized())
                            .font(Font.h3(.semiBold))
                            .foregroundColor(Color.coolGrey)
                        
                        Spacer()
                        
                        Image("chevron_right")
                            .renderingMode(.template)
                            .resizable()
                            .scaledToFit()
                            .foregroundColor(Color.slateGrey)
                            .frame(width: Dimensions.ICON_SMALL, height: Dimensions.ICON_SMALL)
                    }
                    .padding(.horizontal, Spacing.LARGE)
                    .padding(.vertical, Spacing.MEDIUM)
                }
                
                DividerView()
            }
            .background(Color.pearlWhite)
            .onChange(of: viewModel.chosenPaymentMethod) { paymentMethod in
                chosenPaymentMethod = paymentMethod
            }
        }
    }
    
    @ViewBuilder
    var createPaymentMethodsView: some View {
        // TODO: Felipe change this to CreatePaymentMethodsView once get xcode 14
        if #available(iOS 16, *) {
            TempCreatePaymentMethodsView(flowType: viewModel.flowType)
                .edgesIgnoringSafeArea(.all)
        } else {
            CreatePaymentMethodView(flowType: viewModel.flowType)
        }
    }
    
    func paymentMethodRow(for paymentMethod: PaymentMethod) -> some View {
        HStack(alignment: .center, spacing: Spacing.MEDIUM) {
            if showSelector {
                Image("check_circle_filled")
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .frame(width: Dimensions.ICON_MEDIUM, height: Dimensions.ICON_MEDIUM)
                    .foregroundColor(viewModel.chosenPaymentMethod?.id == paymentMethod.id ? Color.secondaryMain : Color.cloudyGrey)
            }
            
            PaymentMethodView(paymentMethod: paymentMethod, textFont: Font.h3(.semiBold), showOptions: showOptions) { warningTitle in
                viewModel.send(action: .showWarningInfo(warning: warningTitle))
            } optionsAction: {
                guard showOptions else { return }
                
                withAnimation {
                    viewModel.send(action: .showOptions(method: paymentMethod))
                }
            }
            .redacted(when: viewModel.isLoading, redactionType: .customPlaceholder)
        }
        .padding(.horizontal, Spacing.LARGE)
        .padding(.vertical, Spacing.MEDIUM)
        .background(Color.pearlWhite)
        .onTapGesture {
            if showSelector,
               !viewModel.isLoading {
                
                switch viewModel.flowType {
                case .rent:
                    viewModel.chosenPaymentMethod = paymentMethod
                case .amenity:
                    switch paymentMethod.type {
                    case .bank:
                        viewModel.popUp = PopUpAlert(title: "AMENITY_BANK_ACCOUNT_DIALOG".localized(), type: .ok)
                    default:
                        viewModel.chosenPaymentMethod = paymentMethod
                    }
                case .store:
                    switch paymentMethod.type {
                    case .bank:
                        viewModel.popUp = PopUpAlert(title: "STORE_NO_BANK_ACCOUNTS".localized(), type: .ok)
                    default:
                        viewModel.chosenPaymentMethod = paymentMethod
                    }
                case .settings:
                    break
                }
            }
        }
    }
}
