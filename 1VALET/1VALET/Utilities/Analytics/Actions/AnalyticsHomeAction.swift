//
//  AnalyticsHomeAction.swift
//  1VALET
//
//  Created by <PERSON> on 25/04/23.
//  Copyright © 2023 1Valet. All rights reserved.
//

import Foundation
import NetworkKit

enum AnalyticsHomeAction: AnalyticsAction {
    case activityFeedBell
    case profilePicture
    case quickAction(action: WelcomeHomeAction)
    case activityFeedViewAll
    case activityFeedItem(type: String)
    case proxCard
    case remoteUnlock
    case myDoors
    case amenityItem(type: String)
    case amenityPrompt
    case amenityViewMore
    case upcomingBooking
    case viewMoreBookigns
    case marketplaceItem
    case marketplaceViewMore
    case marketplacePrompt
    case marketplaceOffer
    case discoverCard(name: String)
    
    var actionName: String {
        switch self {
        case .activityFeedBell:
            return "header_bell_activity_feed_click"
        case .profilePicture:
            return "header_profile_picture_click"
        case .quickAction(let action):
            switch action {
            case .doorSensor:
                return "shortcuts_door_sensor_click"
            case .thermostats:
                return "shortcuts_thermostat_click"
            case .payments:
                return "shortcuts_payment_click"
            case .sendMessage:
                return "shortcuts_send_message_click"
            case .startRequest:
                return "shortcuts_maintenance_click"
            case .postItem:
                return "shortcuts_post_an_item_click"
            case .guestAccess:
                return "shortcuts_guest_invite_click"
            case .parcels:
                return "shortcuts_parcels_click"
            case .storeOrders:
                return "shortcuts_storeOrders_click"
            }
        case .activityFeedViewAll:
            return "activity_feed_view_all_click"
        case .activityFeedItem:
            return "activity_feed_item_click"
        case .proxCard:
            return "digital_entry_prox_unlock_click"
        case .remoteUnlock:
            return "digital_entry_remote_unlock_click"
        case .myDoors:
            return "digital_entry_my_doors_click"
        case .amenityItem:
            return "amenity_item_click"
        case .amenityPrompt:
            return "amenity_prompt_click"
        case .amenityViewMore:
            return "amenity_view_more_click"
        case .upcomingBooking:
            return "upcoming_booking_click"
        case .viewMoreBookigns:
            return "upcoming_booking_view_more_click"
        case .marketplaceItem:
            return "marketplace_item_click"
        case .marketplaceViewMore:
            return "marketplace_view_more_click"
        case .marketplacePrompt:
            return "marketplace_prompt_click"
        case .marketplaceOffer:
            return "marketplace_offer_click"
        case .discoverCard:
            return "discovery_item_click"
        }
    }
    
    var parameters: [String : Any] {
        switch self {
        case .discoverCard(let name):
            return [
                "discoverCardName": name
            ]
        case .activityFeedItem(let type):
            return [
                "activityFeedType": type
            ]
        case .amenityItem(let type):
            return [
                "amenityItemType": type
            ]
        default:
            return [:]
        }
    }
}
