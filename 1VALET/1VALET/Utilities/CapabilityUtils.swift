//
//  CapabilityUtils.swift
//  1VALET
//
//  Created by <PERSON> on 2021-09-13.
//  Copyright © 2021 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import UtilitiesKit

class CapabilityUtils {
    static func hasDoorAccess() -> Bool {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id else { return false }
        return !DoorUtils.getRemoteDoors(suiteId: suiteId).isEmpty
    }
    
    static func hasProximityKey() -> Bool {
        guard let buildingId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.buildingId else { return false }
        return !DoorUtils.getProximityKeys(buildingId: buildingId).isEmpty
    }
    
    static func hasGuestAccess() -> Bool {
        guard !UserUtils.sharedInstance.isGuest,
              UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident,
              InsuiteUtils.sharedInstance.isCurrentResident
                
        else { return false }
        
        let hasGuestDevices = !(GuestAccessUtils.guestAccessDevices?.guestAccessDevices.isEmpty ?? true)
        let hasAccessToSuite = GuestAccessUtils.guestAccessDevices?.canHaveAccessToSuite ?? false
        
        return hasGuestDevices || hasAccessToSuite
    }
    
    static func hasMultipleSuites() -> Bool {
        guard let suites = UserUtils.sharedInstance.loggedInUser?.profile?.suites else { return false }
        return suites.count > 1
    }
    
    static func hasTicketingService() -> Bool {
        guard !UserUtils.sharedInstance.isGuest,
              UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident,
              InsuiteUtils.sharedInstance.isCurrentResident
        else { return false }
        
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.ticketingServices)
        }
        return false
    }
    
    static func hasDoorSensorCapability() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.doorSensors)
        }
        return false
    }
    
    static func hasDoorSensor() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        
        if let insuiteDetails = InsuiteUtils.sharedInstance.insuiteDetails,
           let buildingCapabilities = insuiteDetails.buildingCapabilities,
           UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident,
           InsuiteUtils.sharedInstance.isCurrentResident {
            
            return buildingCapabilities.contains(.doorSensors) && DoorSensorUtils.sharedInstance.alarmSettings != nil
        }
        return false
    }
    
    static func hasAlfredDoor(alfredSettings: AlfredSettingsResponse?) -> Bool {
        guard let alfredSettings = alfredSettings else { return false }
        
        return alfredSettings.bleId != nil &&
        alfredSettings.masterPin != nil &&
        alfredSettings.password1 != nil &&
        alfredSettings.password2 != nil &&
        alfredSettings.systemId != nil
    }
    
    static func hasInSuiteDoor() -> Bool {
        guard let suiteId = UserUtils.sharedInstance.loggedInUser?.defaultSuite?.id else { return false }
        return !DoorUtils.getInSuiteDoors(suiteId: suiteId).isEmpty
    }
    
    static func hasMarketplaceShopsAndServicesAccess() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.marketplaceShopsAndServices)
        }
        return false
    }
    
    static func hasBuildingStoreAccess() -> Bool {
        guard !UserUtils.sharedInstance.isGuest,
              UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident else { return false }
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.store)
        }
        return false
    }
    
    static func hasMarketplaceCommunityAccess() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        
        return InsuiteUtils.sharedInstance.insuiteDetails?.hasCommunityPosts ?? false
    }
    
    static func hasServicesAccess() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.residentServices)
        }
        return false
    }
    
    static func hasSuiteInspections() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.suiteInspections)
        }
        return false
    }
    
    static func hasPaymentProcessing() -> Bool {
        guard !UserUtils.sharedInstance.isGuest,
              UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident else { return false }
        
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities,
           let paymentCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.paymentCapabilities {
            return buildingCapabilities.contains(.paymentProcessing) && (self.hasPaymentsDiscoveryCardAccess() || paymentCapabilities.contains(.store))
        }

        return false
    }
    
    static func hasRentPayment() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        
        if let paymentCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.paymentCapabilities {
            return paymentCapabilities.contains(.suiteBalance) && InsuiteUtils.sharedInstance.isCurrentResident && CapabilityUtils.hasPaymentProcessing()
        }

        return false
    }
    
    static func hasPaymentsDiscoveryCardAccess() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        if let paymentCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.paymentCapabilities {
            return paymentCapabilities.contains(.amenity) || paymentCapabilities.contains(.suiteBalance)
        }

        return false
    }
    
    static func hasCRM() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.crm)
        }
        return false
    }
    
    static func hasAmenityBooking() -> Bool {
        guard !UserUtils.sharedInstance.isGuest,
              UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident
        else { return false }
        
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.amenityBooking)
        }
        return false
    }
    
    static func hasDeliveries() -> Bool {
        guard (UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType == .renter && InsuiteUtils.sharedInstance.isCurrentResident) || (UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType == .owner && InsuiteUtils.sharedInstance.isCurrentResident) else { return false }
        return InsuiteUtils.sharedInstance.insuiteDetails?.isActivityFeedSupported ?? false
    }
    
    static func hasThermostats() -> Bool {
        guard !UserUtils.sharedInstance.isGuest else { return false }
        if let buildingCapabilities = InsuiteUtils.sharedInstance.insuiteDetails?.buildingCapabilities {
            return buildingCapabilities.contains(.thermostats)
        }
        return false
    }
    
    static func canSetCallBySuiteResident() -> Bool {
        guard UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident && InsuiteUtils.sharedInstance.isCurrentResident else { return false }
        
        return (InsuiteUtils.sharedInstance.insuiteDetails?.isCallBySuiteSupported ?? false) && (UserUtils.sharedInstance.loggedInUser?.defaultSuite?.hasOtherCurrentOccupants ?? false)
    }
    
    static func hasMessaging() -> Bool {
        return !UserUtils.sharedInstance.isGuest && UserUtils.sharedInstance.loggedInUser?.defaultSuite?.suiteAssociationType != .ownerNonResident
    }
    
    static func hasFeedback() -> Bool {
        InsuiteUtils.sharedInstance.isCurrentResident && (UserUtils.sharedInstance.loggedInUser?.defaultSuite != nil)
    }
}
