//
//  PaymentUtils.swift
//  1VALET
//
//  Created by <PERSON> on 08/09/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import StripePaymentSheet
import NetworkKit
import SwiftUI
import UtilitiesKit

enum CurrencyCode: String {
    case CAD
    case USD
}

private extension PaymentFlowType {
    var capability: PaymentCapability? {
        return switch self {
        case .rent:
            .suiteBalance
        case .amenity:
            .amenity
        case .store:
            .store
        default:
            nil
        }
    }
}

private extension PaymentCapability {
    var toPaymentCategory: PaymentCategory? {
        return if self == .suiteBalance {
            .suiteBalance
        } else if self == .store {
            .store
        } else if self == .amenity {
            .amenityBooking
        } else {
            nil
        }
    }
}

class PaymentUtils {
    static let shared = PaymentUtils()
    
    private var numberFormatter: NumberFormatter
    private var partialPaymentAllowed: Bool = false
    private var autoPayDayOfMonth: Int = 0
    private var acceptedCardBrands: CardBrandCapability?
    private var capabilityDetails: [PaymentConfigurationCapabilityDetails] = []
    
    var currencyCode: String?
    var paymentUsageType: PaymentUsageType = .none
    var nextAutoPaymentDate: Date = Date()
    var suiteHasOtherOccupantUsingExternalPayments: Bool = false
    var isUserBlockedFromPayments: Bool = false
    
    private init() {
        self.numberFormatter = NumberFormatter()
        self.numberFormatter.numberStyle = .currency
    }
    
    public func set(currencyCode: String?) {
        if let currencyCode = currencyCode {
            let locale = "\(Locale.current.identifier)@currency=\(currencyCode)"
            self.numberFormatter.locale = Locale(identifier: locale)
            self.currencyCode = currencyCode
        }
    }
    
    private func set(configuration: PaymentConfiguration) {
        set(currencyCode: configuration.currencyCode)
            
        if let isPartialPaymentAllowed = configuration.isPartialPaymentAllowed {
            self.partialPaymentAllowed = isPartialPaymentAllowed
        }
        
        if let autoPayDayOfMonth = configuration.autoPayDayOfMonth {
            self.autoPayDayOfMonth = autoPayDayOfMonth
        }
        
        if let nextAutoPaymentDate = configuration.nextAutoPaymentDate {
            self.nextAutoPaymentDate = nextAutoPaymentDate
        }
        
        if let acceptedCardBrands = configuration.cardBrandCapabilities {
            self.acceptedCardBrands = acceptedCardBrands
        }
        
        if let capabilityDetails = configuration.capabilityDetails {
            self.capabilityDetails = capabilityDetails
        }
    }
    
    public func formatCurrency(_ amount: Decimal) -> String {
        return self.numberFormatter.string(for: amount) ?? ""
    }
    
    public func isPartialPaymentAllowed() -> Bool {
        partialPaymentAllowed
    }
    
    public func getAutoPayDayOfMonth() -> Int {
        autoPayDayOfMonth
    }
    
    public func hasToPayCardFees(for flowType: PaymentFlowType) -> Bool {
        capabilityDetails.first(where: { $0.capability == flowType.capability })?.hasResidentFeesCard ?? false
    }
    
    public func hasToPayBankFees(for flowType: PaymentFlowType) -> Bool {
        capabilityDetails.first(where: { $0.capability == flowType.capability })?.hasResidentFeesEft ?? false
    }
    
    public func formatter() -> NumberFormatter {
        numberFormatter
    }
    
    var availableCardBrands: [CardBrand] {
        (acceptedCardBrands?.rawValue.bitComponents().compactMap { cardBrand in
            switch cardBrand {
            case CardBrandCapability.visa.rawValue:
                return .visa
            case CardBrandCapability.mastercard.rawValue:
                return .mastercard
            case CardBrandCapability.amex.rawValue:
                return .amex
            case CardBrandCapability.diners.rawValue:
                return .diners
            case CardBrandCapability.discover.rawValue:
                return .discover
            case CardBrandCapability.eftpos_au.rawValue:
                return .eftpos_au
            case CardBrandCapability.jcb.rawValue:
                return .jcb
            case CardBrandCapability.unionpay.rawValue:
                return .unionpay
            case CardBrandCapability.unknown.rawValue:
                return .unknown
            default:
                return nil
            }
        } ?? []).sorted(by: <)
    }
    
    public func isAvailable(paymentMethodType: AcceptedPaymentMethod, for flowType: PaymentFlowType) -> Bool {
        switch flowType {
        case .settings:
            return capabilityDetails.contains(where: {
                $0.paymentMethod?.contains(paymentMethodType) ?? false
            })
        default:
            if let acceptedPaymentMethods = capabilityDetails.first(where: {
                $0.capability == flowType.capability
            })?.paymentMethod {
                return acceptedPaymentMethods.contains(paymentMethodType)
            }
        }
        return false
    }
    
    public func getGeneralCapability(for type: PaymentMethodType) -> PaymentCategory? {
        switch type {
        case .card:
            capabilityDetails.first(where: {
                $0.paymentMethod?.contains(.card) ?? false
            })?.capability?.toPaymentCategory
        default:
            capabilityDetails.first(where: {
                $0.paymentMethod?.contains(.electronicFundsTransfer) ?? false
            })?.capability?.toPaymentCategory
        }
    }
    
    public func handleAutoPayConfiguration(dayOfMonth: Int) -> String {
        if dayOfMonth <= 0 {
            return RentPaymentConstants.Strings.last
        }
        
        let formatter = NumberFormatter()
        formatter.numberStyle = .ordinal
        return formatter.string(from: NSNumber(value: dayOfMonth))!
    }
    
    var standardSheetConfiguration: PaymentSheet.Configuration {
        var configuration = PaymentSheet.Configuration()
        configuration.appearance.cornerRadius = Dimensions.SMALL_ROUNDED_CORNER
        configuration.appearance.borderWidth = Dimensions.GENERIC_BORDER_WIDTH
        configuration.appearance.colors.text = UIColor(Color.coolGrey)
        configuration.appearance.colors.background = UIColor(Color.appBackground)
        configuration.appearance.colors.componentBackground = UIColor(Color.pearlWhite)
        configuration.appearance.colors.componentBorder = UIColor(Color.cloudyGrey)
        configuration.appearance.colors.componentDivider = UIColor(Color.cloudyGrey)
        configuration.appearance.colors.componentPlaceholderText = UIColor(Color.coolGrey).withAlphaComponent(0.3)
        configuration.appearance.colors.componentText = UIColor(Color.coolGrey)
        configuration.appearance.colors.icon = UIColor(Color.slateGrey)
        configuration.appearance.colors.primary = UIColor(Color.slateGrey)
        configuration.appearance.colors.textSecondary = UIColor(Color.coolGrey)
        configuration.appearance.primaryButton.cornerRadius = Dimensions.MEDIUM_ROUNDED_CORNER
        configuration.appearance.primaryButton.textColor = TextStyle.buttonPrimary.color
        configuration.appearance.primaryButton.font = TextStyle.buttonPrimary.font
        configuration.appearance.primaryButton.backgroundColor = UIColor(Color.slateGrey)
        configuration.appearance.primaryButton.borderWidth = 0
        configuration.appearance.font.base = TextStyle.buttonPrimary.font
        configuration.appearance.colors.danger = UIColor(Color.errorWarning)
        configuration.style = DarkModeUtils.getMode() == .dark ? .alwaysDark : .alwaysLight
        return configuration
    }
    
    func fetchPaymentConfiguration() async throws {
        guard CapabilityUtils.hasPaymentProcessing() else { return }
        
        if let configuration = try await PaymentConfiguration.getConfiguration() {
            PaymentUtils.shared.set(configuration: configuration)
        }
    }
}

enum CardBrand: Int, Comparable {
    case mastercard
    case visa
    case amex
    case discover
    case diners
    case unionpay
    case jcb
    case eftpos_au
    case unknown
    
    var toSTPCardBrand: STPCardBrand? {
        switch self {
        case .visa:
            return .visa
        case .mastercard:
            return .mastercard
        case .amex:
            return .amex
        case .diners:
            return .dinersClub
        case .discover:
            return .discover
        case .jcb:
            return .JCB
        case .unionpay:
            return .unionPay
        default:
            return nil
        }
    }
    
    var title: String? {
        switch self {
        case .visa:
            return "Visa"
        case .mastercard:
            return "Mastercard"
        case .amex:
            return "American Express"
        case .diners:
            return "Diners Club"
        case .discover:
            return "Discover"
        case .eftpos_au:
            return "EFTPos"
        case .jcb:
            return "JCB"
        case .unionpay:
            return "Union Pay"
        case .unknown:
            return nil
        }
    }
    
    static func < (lhs: CardBrand, rhs: CardBrand) -> Bool {
        lhs.rawValue < rhs.rawValue
    }
}
