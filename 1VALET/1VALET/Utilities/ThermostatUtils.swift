//
//  ThermostatUtils.swift
//  1VALET
//
//  Created by <PERSON> on 2021-08-25.
//  Copyright © 2021 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import UtilitiesKit
import Combine

class ThermostatUtils: ObservableObject {
    public static let sharedInstance = ThermostatUtils()
    private var cancellables: [AnyCancellable] = [AnyCancellable]()
    
    @Published var localThermostats: [ThermostatStatusResponse]? {
        didSet {
            let encoder = JSONEncoder()
            if let encoded = try? encoder.encode(localThermostats) {
                UserDefaults.standard.set(encoded, forKey: UserDefaultsConstants.THERMOSTAT_STATUS)
            }
        }
    }
    
    init() {
        if let thermostatStatusData = UserDefaults.standard.data(forKey: UserDefaultsConstants.THERMOSTAT_STATUS) {
            let decoder = JSONDecoder()
            self.localThermostats = try? decoder.decode([ThermostatStatusResponse].self, from: thermostatStatusData) as [ThermostatStatusResponse]
        }
        
        UserUtils.sharedInstance.$loggedInUser
            .removeDuplicates()
            .filter({ $0 == nil })
            .sink(receiveValue: { [weak self] _ in self?.localThermostats = nil })
            .store(in: &cancellables)
    }
}
