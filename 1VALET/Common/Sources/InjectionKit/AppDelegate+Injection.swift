//
//  AppDelegate+Injection.swift
//  1VALET
//
//  Created by <PERSON> on 27/05/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation
import NetworkKit
import UtilitiesKit

extension DependencyResolver: DependencyResolverRegistering {
    public static func registerAllServices() {
        registerAllRepositories()
    }
    
    public static func registerAllRepositories() {
        register {
            MarketplaceRepository(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as MarketplaceRepositoryProtocol
        }.scope(.shared)
        
        register {
            CallSettingsRepositoryImpl(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as CallSettingsRepository
        }.scope(.shared)
        
        register {
            AuthenticationRepository(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as AuthenticationRepository
        }.scope(.shared)
        
        register {
            TwoWayMessagingRepository(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as TwoWayMessagingRepositoryProtocol
        }.scope(.shared)
        
        register {
            RentPaymentRepository(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as RentPaymentRepositoryProtocol
        }.scope(.shared)
        
        register {
            SuiteInspectionRepository(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as SuiteInspectionRepositoryProtocol
        }.scope(.shared)
                
        register {
            FacialRecognitionRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            DoorRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.cached)
        
        register {
            InsuiteRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            ThermostatRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            MerchantShopRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)

        register {
            AmenityRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            DoorSensorRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            NotificationsRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            VersionRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            StaffAccessRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            GuestAccessRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            HelpCenterRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            VideoCallRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            DocBoxRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            OnboardingRepository(appAuthProvider: SessionManager.defaultProvider(with: SessionManager.SessionType.appAuthentication))
        }.scope(.shared)

        register {
            TicketingRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.cached)

        register {
            FeedbackRepository(
                userAuthProvider: SessionManager.defaultProvider(
                    with: SessionManager.SessionType.userAuthentication,
                    deviceId: CallUtils.getPushKitToken()
                )
            ) as FeedbackRepositoryProtocol
        }.scope(.cached)
        
        register {
            LeaseRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            BrandingRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            )
        }.scope(.shared)
        
        register {
            BuildingStoreRepository(userAuthProvider: SessionManager.defaultProvider(
                with: SessionManager.SessionType.userAuthentication,
                deviceId: CallUtils.getPushKitToken()
            )
            ) as BuildingStoreRepositoryProtocol
        }.scope(.shared)
    }
}
