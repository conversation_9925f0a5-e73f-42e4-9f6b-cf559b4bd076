//
//  BuildingCapability.swift
//  NetworkKit
//
//  Created by <PERSON> on 2021-04-29.
//  Copyright © 2021 1Valet. All rights reserved.
//

import Foundation

public struct BuildingCapability: OptionSet, Codable {
    public init(rawValue: Int) {
        self.rawValue = rawValue
    }
    
    public let rawValue: Int
    
    public static let accessControl = BuildingCapability(rawValue: 1)
    public static let securityCameras = BuildingCapability(rawValue: 1 << 1)
    public static let inSuiteAccess = BuildingCapability(rawValue: 1 << 2)
    public static let crm = BuildingCapability(rawValue: 1 << 3)
    public static let lockers = BuildingCapability(rawValue: 1 << 4)
    public static let thermostats = BuildingCapability(rawValue: 1 << 5)
    public static let proximityKeys = BuildingCapability(rawValue: 1 << 6)
    public static let docbox = BuildingCapability(rawValue: 1 << 7)
    public static let remoteUnlock = BuildingCapability(rawValue: 1 << 8)
    public static let amenityBooking = BuildingCapability(rawValue: 1 << 9)
    public static let residentServices = BuildingCapability(rawValue: 1 << 14)
    public static let ticketingServices = BuildingCapability(rawValue: 1 << 15)
    public static let doorSensors = BuildingCapability(rawValue: 1 << 17)
    public static let store = BuildingCapability(rawValue: 1 << 20)
    public static let suiteInspections = BuildingCapability(rawValue: 1 << 21)
    public static let paymentProcessing = BuildingCapability(rawValue: 1 << 22)
    public static let marketplaceShopsAndServices = BuildingCapability(rawValue: 1 << 29)
    
    //Now replaced with hasCommunityPosts. it is removed from API, but kept for backward compatibility
//    public static let marketplaceCommunity = BuildingCapability(rawValue: 1 << 30)
    
}
