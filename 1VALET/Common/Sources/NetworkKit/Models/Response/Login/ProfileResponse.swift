//
//  ProfileResponse.swift
//  NetworkKit
//
//  Created by <PERSON> on 2021-04-29.
//  Copyright © 2021 1Valet. All rights reserved.
//

import Foundation

public struct ProfileResponse: Codable, Equatable {
    public let basUserId: String?
    public let firstName: String?
    public let lastName: String?
    public let phoneNumber: String?
    public let offlineQrCodeTotpSecret: String?
    public let hasLimitedMobility: Bool?
    public let email: String?
    public var preferredLocale: String?
    public var suites: [SuiteResponse]?
}

public struct SuiteResponse: Codable, Equatable {
    public let id: String
    public let occupantId: String?
    public let buildingId: String?
    public let buildingName: String?
    public let timezone: String?
    public let number: String?
    public let suiteAssociationType: SuiteAssociationType?
    public let isPrimaryContact: Bool?
       
    public let directoryCode: String?
    public var directoryPhoneNumber: String?
    public var forwardCallsToDirectoryPhoneNumber: String?
    public var isLinkedAccount: Bool?
    public var hasOtherCurrentOccupants: Bool?
    public var primaryCallBySuiteUserFullName: String?
    
    public func getName() -> String {
        return "#\(self.number ?? ""), \(self.buildingName ?? "")"
    }
    
    public static func == (lhs: SuiteResponse, rhs: SuiteResponse) -> Bool {
        return lhs.id == rhs.id
    }
}

public enum SuiteAssociationType: String, Codable {
    case renter = "Renter"
    case owner = "Owner"
    case ownerNonResident = "OwnerNonResident"
    case guest = "Guest"
}
