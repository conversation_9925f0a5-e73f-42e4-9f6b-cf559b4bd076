//
//  TwoWayMessagingGetMessageResponse.swift
//  NetworkKit
//
//  Created by <PERSON> on 12/07/22.
//  Copyright © 2022 1Valet. All rights reserved.
//

import Foundation

public struct TwoWayMessagingGetMessageResponse: Codable {
    public let status: ConversationStatus?
    public let messages: [TwoWayMessagingMessageResponse]?
    public let subject: String?
    public let type: ConversationType?
}
