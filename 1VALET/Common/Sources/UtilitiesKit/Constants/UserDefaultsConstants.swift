//
//  UserDefaultsConstants.swift
//  1VALET
//
//  Created by <PERSON> on 2019-11-05.
//  Copyright © 2019 1Valet. All rights reserved.
//

import Foundation

public struct UserDefaultsConstants {
    
    public static let NOT_FIRST_TIME_EVER_RUN = "residentapp.first_time_run"

    public static let APP_PREFIX = "com.1valet.residentapp"
    public static let PERSIST_PREFIX = "\(APP_PREFIX).persist"
    
    // Doors
    public static let ENTRY_CARDS_KEY = "\(APP_PREFIX).entry_cards"
    public static let INSUITE_DOORS_KEY = "\(APP_PREFIX).insuite_doors"
    public static let ACCESS_DOORS_KEY = "\(APP_PREFIX).access_doors"
    public static let USER_ID_KEY = "\(APP_PREFIX).user_id"
    public static let PROXIMITY_KEYS_KEY_OLD = "\(APP_PREFIX).proximity_keys"
    public static let DEFAULT_SUITE_ID_KEY = "\(APP_PREFIX).default_suite_id"

    public static let NOT_FIRST_TIME_SELFIE_INSTRUCTIONS_KEY = "\(APP_PREFIX).not_first_time_selfie_instructions"

    public static let CURRENT_APP_VERSION = "\(APP_PREFIX).current_app_version"

    public static let CURRENT_VERSION_NOTIFICATION_TOPIC = "\(APP_PREFIX).current_version_notification_topic"

    public static let USER_FIRST_NAME = "\(APP_PREFIX).user_first_name"
    public static let USER_OCCUPANT_ID = "\(APP_PREFIX).user_occupant_id"
    
    public static let THERMOSTAT_STATUS = "\(APP_PREFIX).thermostat_status"
    public static let TEMPERATURE_UNIT = "\(APP_PREFIX).temperature_unit"
    
    public static let DOCBOX_SORT_ORDER = "\(APP_PREFIX).docbox_sort_order"
    
    public static let MOVEIN_DATE_DETAILS = "\(APP_PREFIX).movein_date_details"
    public static let CURRENT_CALL = "\(APP_PREFIX).call_kit"
    public static let CURRENT_CALL_STATE = "\(APP_PREFIX).call_state"
    
    public static let DOOR_SENSOR_DELAY_PERIOD = "\(APP_PREFIX).door_sensor_delay_peiod"
    public static let DOOR_SENSOR_COUNTDOWN_EXPIRY = "\(APP_PREFIX).door_sensor_countdown_expiry"
    public static let DOOR_SENSOR_ALARM_SETTINGS = "\(APP_PREFIX).door_sensor_alarm_settings"
    
    public static let JUST_LOGGED_IN_KEY = "\(APP_PREFIX).just_logged_in"
    
    public static let HELP_CENTER_EMERGENCY_PHONE = "\(APP_PREFIX).help_center_emergency_phone"
    public static let HELP_CENTER_MAIN_PHONE = "\(APP_PREFIX).help_center_main_phone"
    public static let HELP_CENTER_MAIN_EMAIL = "\(APP_PREFIX).help_center_main_email"
    
    // App Update
    public static let APP_UPDATE_LATEST_VERSION = "\(APP_PREFIX).app_update_latest_version"
    public static let APP_UPDATE_DIALOG_SHOWN = "\(APP_PREFIX).app_update_dialog"
    public static let APP_UPDATE_OLD_DIALOG_SEEN = "\(APP_PREFIX).app_update_old_dialog_seen"
    
    public static let STAFF_MOVE_IN = "\(APP_PREFIX).staff_move_in"
    public static let STAFF_MOVE_IN_SUITE_ID = "\(APP_PREFIX).staff_move_in_suite_id"
    
    // Toast Count
    public static let DOOR_SENSOR_TOAST_COUNT = "\(APP_PREFIX).door_sensor_toast_count"
    
    public static let IN_SUITE_UNLOCK_STATUS = "\(APP_PREFIX).in_suite_unlock_status"
    
    // Resident Services
    public static let RESIDENT_SERVICES = "\(APP_PREFIX).resident_services"
    
    public static let PUSH_KIT_TOKEN_IN_BACKEND = "\(APP_PREFIX).push_kit_token_in_backend"
        
    // Guest Access
    public static let GUEST_ACCESS_DEVICES_KEY = "\(APP_PREFIX).guest_access_devices"
    
    // Missed Call Notification
    public static let LAST_MISSED_CALL_ROOM_ID = "\(APP_PREFIX).last_missed_call_room_id"
    
    // Facial Recognition Images
    public static let FACIAL_RECOGNITION_IMAGES = "\(APP_PREFIX).facial_recognition_images"
    
    // Staff Access
    public static let STAFF_ACCESS_REQUEST_TECHNICIAN_NAME = "\(APP_PREFIX).staff_access_request_technician_name"
    
    // Device ID Tokens
    public static let LAST_TIME_UPDATED_DEVICE_ID_TOKEN_KEY = "\(APP_PREFIX).last_time_updated_device_id_token"
    public static let LAST_TIME_UPDATED_FIREBASE_TOKEN_KEY = "\(APP_PREFIX).last_time_updated_firebase_token"
            
    // Color Themes
    public static let APP_COLOR_THEME = "\(APP_PREFIX).color_theme"
    public static let APP_COLOR_THEME_INFO = "\(APP_PREFIX).color_theme_info"
    
    // Notification
    public static let APP_NOTIFICATION_COUNT = "\(APP_PREFIX).app_notification_count"
    
    // Persist even after login
    public static let PROXIMITY_KEYS_KEY = "\(PERSIST_PREFIX).proximity_keys"
    public static let LATEST_BUILDING_KEY = "\(PERSIST_PREFIX).building"
    public static let FIRST_TIME_LOGGED_OUT_PROX_CARD_USE = "\(PERSIST_PREFIX).first_time_logged_out_prox_card_use"
    public static let LATEST_BAS_USER_ID_KEY = "\(PERSIST_PREFIX).bas_user_id"
    public static let LATEST_USERNAME_KEY = "\(PERSIST_PREFIX).username"
    public static let USERNAME_LIST_KEY = "\(PERSIST_PREFIX).username_list"
    public static let ENTRY_CARDS_TOOLTIPS_KEY = "\(PERSIST_PREFIX).entry_cards_tooltips"
    public static let AMENITY_BOOKING_TIME_CONSECUTIVE_TOOLTIPS_KEY = "\(PERSIST_PREFIX).amenity_booking_time_consecutive_tooltips"
    public static let AMENITY_BOOKING_TIME_LIMIT_TOOLTIPS_KEY = "\(PERSIST_PREFIX).amenity_booking_time_limit_tooltips"
    public static let NOT_FIRST_TIME_LOGGED_IN_KEY = "\(PERSIST_PREFIX).first_time_logged_in"
    public static let GUEST_ACCESS_INSTRUCTIONS_KEY = "\(PERSIST_PREFIX).guest_access_instructions"
    public static let DEVICE_ID_KEY = "\(PERSIST_PREFIX).device_id"
    public static let DARK_MODE_KEY = "\(PERSIST_PREFIX).dark_mode"
    public static let PUSH_KIT_TOKEN = "\(PERSIST_PREFIX).push_kit_token"
    public static let FIREBASE_DEVICE_ID_KEY = "\(PERSIST_PREFIX).firebase_device_id"
    
    // Insuite
    public static let INSUITE_DIALOG_TIP_NOT_SEEN = "\(PERSIST_PREFIX).insuite_dialog_tip_not_seen"
    
    // Suite Inspection
    public static let SUITE_INSPECTION_PROGRESS_KEY = "\(PERSIST_PREFIX).suite_inspection_progress_key"
    
    // Missed Call Improvements / Entry Console
    public static let HAS_SEEN_ENTRY_CONSOLE_OPTIONS = "\(PERSIST_PREFIX).has_seen_entry_console_options"
    public static let SUITE_DID_CHANGE = "\(APP_PREFIX).suite_did_change"
    
    // Profile Update
    public static let NEEDS_PROFILE_UPDATE_KEY = "\(PERSIST_PREFIX).needsProfileUpdate"
    
    // Activity Feed
    public static let LAST_ACTIVITY_FEED_ITEM_SEEN_ID = "\(PERSIST_PREFIX).lastActivityFeedItemSeenID"
    public static let SHOW_EMPTY_STATE_COMPLETE_ACTIVITY_FEED = "\(PERSIST_PREFIX).showEmptyStateCompleteActivityFeed"
    public static let HIDDEN_NOTIFICATIONS_IDS_ACTIVITY_FEED = "\(PERSIST_PREFIX).hiddenNotificationIDsWithDateActivityFeed"
    public static let LAST_LOCAL_NOTIFICATION_EXPIRED_REMOVE = "\(PERSIST_PREFIX).last_local_notification_expired_remove"
    public static let LAST_NOTIFICATION_BACKUP_DATE = "\(PERSIST_PREFIX).last_notification_backup_date"

    // Marketplace Shops
    public static let LAST_TIME_SEEN_SERVICES = "\(PERSIST_PREFIX).marketplace_last_time_seen_services"
    public static let LAST_TIME_SEEN_FOODS = "\(PERSIST_PREFIX).marketplace_last_time_seen_foods"
    public static let LAST_TIME_SEEN_COMMUNITY_POST = "\(PERSIST_PREFIX).marketplace_last_time_seen_community_post"
    public static let LAST_TIME_SEEN_STORE = "\(PERSIST_PREFIX).marketplace_last_time_seen_store"
    
    // Lock / Unlock Tips
    public static let DO_NOT_SHOW_LOCK_TIP = "\(PERSIST_PREFIX).do_not_show_lock_tip"
    public static let DO_NOT_SHOW_FIRST_TIME_UNLOCKING_TIP = "\(PERSIST_PREFIX).do_not_show_first_time_locking_tip"
    public static let UNLOCK_TOAST_COUNT = "\(PERSIST_PREFIX).unlock_toast_count"
    
    // Widgets Info banner
    public static let DO_NOT_SHOW_WIDGETS_INFO_BANNER = "\(PERSIST_PREFIX).do_not_show_widgets_info_banner"
    
    // Amenities approval pop up
    public static let DO_NOT_SHOW_AMENITIES_APPROVAL_POP_UP = "\(PERSIST_PREFIX).do_not_show_amenities_approval_pop_up"
    public static let DO_NOT_SHOW_AMENITIES_PRIVATE_USE_POP_UP = "\(PERSIST_PREFIX).do_not_show_amenities_private_use_pop_up"
    public static let DO_NOT_SHOW_AMENITIES_MULTIPLE_TIME_SLOTS_POP_UP = "\(PERSIST_PREFIX).do_not_show_amenities_multiple_time_slots_pop_up"
    
    // Alfred
    public static let ALFRED_IS_USING_BLUETOOTH = "\(PERSIST_PREFIX).lock_using_bluetooth"
    public static let ALFRED_IS_USING_BLUETOOTH_NOT_FIRST_TIME_DIALOG = "\(PERSIST_PREFIX).lock_using_bluetooth_dialog"
    
    // Primary Call By Suite Resident Change
    public static let SHOW_PRIMARY_RESIDENT_CHANGE_POP_UP = "\(PERSIST_PREFIX).show_primary_resident_change_pop_up"

    // Feedback dismiss
    public static let FEEDBACK_DISMISS = "\(PERSIST_PREFIX).feedback_dismiss"
    public static let FEEDBACK_GENERAL_DISMISS = "\(PERSIST_PREFIX).feedback_general_dismiss"
    
    // ProxKey
    public static let LAST_TIME_KEY_UPDATE = "\(PERSIST_PREFIX).last_time__key_update"
}

public struct KeychainConstants {
    public static let APP_PREFIX = "com.1valet.residentapp"
    
    public static let USER_MODEL = "\(APP_PREFIX).user_model"
    public static let INSUITE_DETAILS_KEY = "\(APP_PREFIX).insuite_details_key"
    public static let GUEST_ACCESSES_KEY = "\(APP_PREFIX).guest_accesses_key"
}
