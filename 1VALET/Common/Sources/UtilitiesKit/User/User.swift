//
//  User.swift
//  1VALET
//
//  Created by <PERSON> on 2019-08-20.
//  Copyright © 2019 1Valet. All rights reserved.
//

import Foundation
import NetworkKit

public struct User: Codable, Equatable {
    public var recipientId = UUID().uuidString
    
    public var username: String?
    public var defaultSuiteIndex: Int?
    public var profile: ProfileResponse?
    
    public init(username: String?, defaultSuiteIndex: Int?, profile: ProfileResponse?) {
        self.username = username
        self.defaultSuiteIndex = defaultSuiteIndex
        self.profile = profile
    }
    
    public var defaultSuite: SuiteResponse? {
        if let index = self.defaultSuiteIndex, let suites = self.profile?.suites, index < suites.count {
            return suites[index]
        }
        return nil
    }
}
